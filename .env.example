# =============================================================================
# TASKMASTER ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control - it contains sensitive credentials
# =============================================================================

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3030
FRONTEND_URL=http://localhost:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Neon PostgreSQL Database URL (Required)
# Format: ************************************************************
DATABASE_URL="your-neon-postgresql-url-here"

# Direct database connection (for migrations)
DIRECT_URL="your-neon-direct-url-here"

# Redis Configuration (Optional - for distributed rate limiting)
REDIS_URL="your-redis-url-here"

# =============================================================================
# AUTHENTICATION & SESSION MANAGEMENT
# =============================================================================
# NextAuth Configuration (Required)
# Generate a secure secret: openssl rand -base64 32
NEXTAUTH_SECRET="your-nextauth-secret-32-chars-minimum"
NEXTAUTH_URL="http://localhost:3000"

# =============================================================================
# OAUTH PROVIDERS
# =============================================================================
# Google OAuth (Required for Google login)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Twitter/X OAuth (Required for Twitter login and API access)
TWITTER_CLIENT_ID="your-twitter-client-id"
TWITTER_CLIENT_SECRET="your-twitter-client-secret"
TWITTER_CALLBACK_URL="http://localhost:3000/api/auth/twitter/callback"

# =============================================================================
# MEDIA UPLOAD SERVICE
# =============================================================================
# UploadThing Configuration (Required for media uploads)
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
UPLOADTHING_TOKEN="your-uploadthing-token"

# =============================================================================
# AI PROVIDERS
# =============================================================================
# OpenAI Configuration (Required for GPT models)
OPENAI_API_KEY="your-openai-api-key"
OPENAI_BASE_URL=""
OPENAI_DEFAULT_MODEL="gpt-4"
OPENAI_MAX_TOKENS="4000"
OPENAI_TEMPERATURE="0.7"

# Google Gemini (Required for Gemini models)
GEMINI_API_KEY="your-gemini-api-key"

# Mistral AI (Optional)
MISTRAL_API_KEY="your-mistral-api-key"

# Hugging Face (Optional)
HUGGINGFACE_API_KEY="your-huggingface-api-key"

# Groq (Optional)
GROQ_API_KEY="your-groq-api-key"

# OpenRouter (Optional)
OPENROUTER_API_KEY="your-openrouter-api-key"

# =============================================================================
# SCHEDULING CONFIGURATION
# =============================================================================
SCHEDULER_ENABLED=true
SCHEDULER_INTERVAL="*/1 * * * *"

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Logging and Debug
DEBUG=false
LOG_LEVEL="info"