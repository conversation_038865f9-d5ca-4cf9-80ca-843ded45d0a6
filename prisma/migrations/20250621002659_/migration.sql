-- AlterTable
ALTER TABLE "tweet_performance" ADD COLUMN     "clickThroughRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
ADD COLUMN     "profileClicks" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "reach" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "urlClicks" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "daily_analytics" (
    "id" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "userId" TEXT NOT NULL,
    "agentId" TEXT,
    "twitterAccountId" TEXT,
    "tweetsPublished" INTEGER NOT NULL DEFAULT 0,
    "totalLikes" INTEGER NOT NULL DEFAULT 0,
    "totalRetweets" INTEGER NOT NULL DEFAULT 0,
    "totalReplies" INTEGER NOT NULL DEFAULT 0,
    "totalImpressions" INTEGER NOT NULL DEFAULT 0,
    "totalReach" INTEGER NOT NULL DEFAULT 0,
    "avgEngagementRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "followerCount" INTEGER NOT NULL DEFAULT 0,
    "followerGrowth" INTEGER NOT NULL DEFAULT 0,
    "topPerformingTweetId" TEXT,
    "bestEngagementHour" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "daily_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "follower_analytics" (
    "id" TEXT NOT NULL,
    "twitterAccountId" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "followerCount" INTEGER NOT NULL,
    "followingCount" INTEGER NOT NULL,
    "growth" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "follower_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "content_insights" (
    "id" TEXT NOT NULL,
    "tweetId" TEXT NOT NULL,
    "sentiment" TEXT,
    "topics" TEXT[],
    "wordCount" INTEGER NOT NULL DEFAULT 0,
    "hasMedia" BOOLEAN NOT NULL DEFAULT false,
    "hasLinks" BOOLEAN NOT NULL DEFAULT false,
    "hasHashtags" BOOLEAN NOT NULL DEFAULT false,
    "language" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "content_insights_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "daily_analytics_date_idx" ON "daily_analytics"("date");

-- CreateIndex
CREATE INDEX "daily_analytics_userId_idx" ON "daily_analytics"("userId");

-- CreateIndex
CREATE INDEX "daily_analytics_agentId_idx" ON "daily_analytics"("agentId");

-- CreateIndex
CREATE INDEX "daily_analytics_twitterAccountId_idx" ON "daily_analytics"("twitterAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "daily_analytics_date_userId_agentId_twitterAccountId_key" ON "daily_analytics"("date", "userId", "agentId", "twitterAccountId");

-- CreateIndex
CREATE INDEX "follower_analytics_twitterAccountId_idx" ON "follower_analytics"("twitterAccountId");

-- CreateIndex
CREATE INDEX "follower_analytics_date_idx" ON "follower_analytics"("date");

-- CreateIndex
CREATE UNIQUE INDEX "follower_analytics_twitterAccountId_date_key" ON "follower_analytics"("twitterAccountId", "date");

-- CreateIndex
CREATE UNIQUE INDEX "content_insights_tweetId_key" ON "content_insights"("tweetId");

-- CreateIndex
CREATE INDEX "content_insights_tweetId_idx" ON "content_insights"("tweetId");

-- CreateIndex
CREATE INDEX "content_insights_sentiment_idx" ON "content_insights"("sentiment");

-- CreateIndex
CREATE INDEX "content_insights_hasMedia_idx" ON "content_insights"("hasMedia");

-- CreateIndex
CREATE INDEX "tweet_performance_collectedAt_idx" ON "tweet_performance"("collectedAt");

-- AddForeignKey
ALTER TABLE "daily_analytics" ADD CONSTRAINT "daily_analytics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_analytics" ADD CONSTRAINT "daily_analytics_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "agents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_analytics" ADD CONSTRAINT "daily_analytics_twitterAccountId_fkey" FOREIGN KEY ("twitterAccountId") REFERENCES "twitter_accounts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daily_analytics" ADD CONSTRAINT "daily_analytics_topPerformingTweetId_fkey" FOREIGN KEY ("topPerformingTweetId") REFERENCES "scheduled_tweets"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "follower_analytics" ADD CONSTRAINT "follower_analytics_twitterAccountId_fkey" FOREIGN KEY ("twitterAccountId") REFERENCES "twitter_accounts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "content_insights" ADD CONSTRAINT "content_insights_tweetId_fkey" FOREIGN KEY ("tweetId") REFERENCES "scheduled_tweets"("id") ON DELETE CASCADE ON UPDATE CASCADE;
