-- CreateTable
CREATE TABLE "tweet_performance" (
    "id" TEXT NOT NULL,
    "tweetId" TEXT NOT NULL,
    "likes" INTEGER NOT NULL DEFAULT 0,
    "retweets" INTEGER NOT NULL DEFAULT 0,
    "replies" INTEGER NOT NULL DEFAULT 0,
    "impressions" INTEGER NOT NULL DEFAULT 0,
    "engagementRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "collectedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tweet_performance_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tweet_performance_tweetId_key" ON "tweet_performance"("tweetId");

-- CreateIndex
CREATE INDEX "tweet_performance_tweetId_idx" ON "tweet_performance"("tweetId");

-- Ad<PERSON>F<PERSON>ignKey
ALTER TABLE "tweet_performance" ADD CONSTRAINT "tweet_performance_tweetId_fkey" FOREIGN KEY ("tweetId") REFERENCES "scheduled_tweets"("id") ON DELETE CASCADE ON UPDATE CASCADE;
