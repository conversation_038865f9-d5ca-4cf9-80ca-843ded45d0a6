// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String?  // Optional for OAuth-only users
  name         String?
  avatar       String?
  preferences  Json?    @db.JsonB // User preferences stored as JSONB
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  twitterAccounts TwitterAccount[]
  googleAccounts  GoogleAccount[]
  sessions        Session[]
  scheduledTweets ScheduledTweet[]
  mediaFiles      MediaFile[]
  agents          Agent[]
  dailyAnalytics  DailyAnalytics[]

  @@map("users")
}

model Agent {
  id               String   @id @default(cuid())
  name             String
  description      String
  personaData      Json     @db.JsonB // Complete persona configuration
  personaDefinition Json?    @db.JsonB // Uploaded persona definition JSON
  aiProvider       String   // 'openai' | 'google'
  aiModel          String
  isActive         Boolean  @default(true)
  tweetsGenerated  Int      @default(0)
  engagementRate   Float    @default(0.0)
  maxDailyTweets   Int      @default(10)
  userId           String
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]
  memories        AgentMemory[]
  dailyAnalytics  DailyAnalytics[]

  @@index([userId])
  @@index([isActive])
  @@map("agents")
}

model TwitterAccount {
  id              String   @id @default(cuid())
  twitterId       String   @unique // Twitter's unique user ID
  username        String   @unique // Twitter handle (e.g., @username)
  displayName     String   // Display name on Twitter
  profileImageUrl String?  // URL to profile image
  accessToken     String   // OAuth access token
  refreshToken    String?  // OAuth refresh token (if available)
  tokenSecret     String?  // OAuth token secret (for OAuth 1.0a)
  isActive        Boolean  @default(true)
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]
  dailyAnalytics  DailyAnalytics[]
  followerAnalytics FollowerAnalytics[]

  @@index([userId])
  @@index([isActive])
  @@index([twitterId])
  @@map("twitter_accounts")
}

model ScheduledTweet {
  id             String    @id @default(cuid())
  content        String
  mediaUrls      String[]  // Array of media URLs
  scheduledFor   DateTime?
  publishedAt    DateTime?
  status         String    @default("draft") // 'draft' | 'scheduled' | 'published' | 'failed'
  twitterTweetId String?   // ID from Twitter API after publishing
  errorMessage   String?   // Error message if publishing failed
  
  // Thread support
  threadId       String?   // Groups tweets into threads
  threadOrder    Int?      // Order within thread (0-based)
  isThreadStart  Boolean   @default(false) // First tweet in thread
  
  // Metrics
  likes          Int       @default(0)
  retweets       Int       @default(0)
  replies        Int       @default(0)
  impressions    Int       @default(0)
  engagementRate Float     @default(0.0)
  
  // Foreign Keys
  agentId          String
  userId           String
  twitterAccountId String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  agent          Agent           @relation(fields: [agentId], references: [id], onDelete: Cascade)
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  twitterAccount TwitterAccount? @relation(fields: [twitterAccountId], references: [id], onDelete: SetNull)
  performance   TweetPerformance?
  dailyAnalyticsAsTop DailyAnalytics[] @relation("TopPerformingTweet")
  contentInsight ContentInsight?

  @@index([agentId])
  @@index([userId])
  @@index([status])
  @@index([scheduledFor])
  @@index([publishedAt])
  @@index([threadId])
  @@index([threadOrder])
  @@map("scheduled_tweets")
}

model TweetPerformance {
  id             String   @id @default(cuid())
  tweetId        String   @unique
  likes          Int      @default(0)
  retweets       Int      @default(0)
  replies        Int      @default(0)
  impressions    Int      @default(0)
  reach          Int      @default(0)      // New: unique accounts reached
  urlClicks      Int      @default(0)      // New: URL clicks
  profileClicks  Int      @default(0)      // New: profile clicks
  engagementRate Float    @default(0.0)
  clickThroughRate Float  @default(0.0)    // New: CTR calculation
  collectedAt    DateTime @default(now())

  // Relations
  tweet ScheduledTweet @relation(fields: [tweetId], references: [id], onDelete: Cascade)

  @@index([tweetId])
  @@index([collectedAt])
  @@map("tweet_performance")
}

// New: Daily aggregated analytics for efficient querying
model DailyAnalytics {
  id                String   @id @default(cuid())
  date              DateTime @db.Date
  userId            String
  agentId           String?
  twitterAccountId  String?

  // Tweet metrics
  tweetsPublished   Int      @default(0)
  totalLikes        Int      @default(0)
  totalRetweets     Int      @default(0)
  totalReplies      Int      @default(0)
  totalImpressions  Int      @default(0)
  totalReach        Int      @default(0)
  avgEngagementRate Float    @default(0.0)

  // Follower metrics
  followerCount     Int      @default(0)
  followerGrowth    Int      @default(0)

  // Content insights
  topPerformingTweetId String?
  bestEngagementHour   Int?     // 0-23 hour of day

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  agent           Agent?          @relation(fields: [agentId], references: [id], onDelete: Cascade)
  twitterAccount  TwitterAccount? @relation(fields: [twitterAccountId], references: [id], onDelete: SetNull)
  topPerformingTweet ScheduledTweet? @relation("TopPerformingTweet", fields: [topPerformingTweetId], references: [id], onDelete: SetNull)

  @@unique([date, userId, agentId, twitterAccountId])
  @@index([date])
  @@index([userId])
  @@index([agentId])
  @@index([twitterAccountId])
  @@map("daily_analytics")
}

// New: Follower analytics tracking
model FollowerAnalytics {
  id               String   @id @default(cuid())
  twitterAccountId String
  date             DateTime @db.Date
  followerCount    Int
  followingCount   Int
  growth           Int      @default(0) // Daily growth (can be negative)

  createdAt        DateTime @default(now())

  // Relations
  twitterAccount TwitterAccount @relation(fields: [twitterAccountId], references: [id], onDelete: Cascade)

  @@unique([twitterAccountId, date])
  @@index([twitterAccountId])
  @@index([date])
  @@map("follower_analytics")
}

// New: Content insights and categorization
model ContentInsight {
  id          String   @id @default(cuid())
  tweetId     String   @unique
  sentiment   String?  // 'positive', 'negative', 'neutral'
  topics      String[] // Extracted topics/hashtags
  wordCount   Int      @default(0)
  hasMedia    Boolean  @default(false)
  hasLinks    Boolean  @default(false)
  hasHashtags Boolean  @default(false)
  language    String?  // Detected language

  createdAt   DateTime @default(now())

  // Relations
  tweet ScheduledTweet @relation(fields: [tweetId], references: [id], onDelete: Cascade)

  @@index([tweetId])
  @@index([sentiment])
  @@index([hasMedia])
  @@map("content_insights")
}

model AgentMemory {
  id        String   @id @default(cuid())
  content   String
  context   String?  // Additional context about the memory
  embedding Bytes?   @db.ByteA // pgvector embedding stored as bytes
  agentId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@index([agentId])
  // Note: pgvector index will be added via raw SQL in migration
  @@map("agent_memories")
}

model MediaFile {
  id       String   @id @default(cuid())
  filename String
  url      String
  mimeType String
  size     Int
  userId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([mimeType])
  @@map("media_files")
}

model GoogleAccount {
  id              String   @id @default(cuid())
  googleId        String   @unique // Google's unique user ID
  email           String   @unique // Google email
  name            String   // Display name from Google
  profileImageUrl String?  // URL to profile image
  accessToken     String   // OAuth access token (encrypted)
  refreshToken    String?  // OAuth refresh token (encrypted)
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([googleId])
  @@map("google_accounts")
}

model Session {
  id        String   @id @default(cuid())
  jwtId     String   @unique // JWT ID for revocation
  userId    String
  ipAddress String?
  userAgent String?
  expiresAt DateTime
  isRevoked Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([jwtId])
  @@index([expiresAt])
  @@map("sessions")
}