# Dependencies
node_modules/
package-lock.json

# Environment variables (NEVER commit these)
.env
.env.local
.env.development
.env.staging
.env.production
.env.test

# Build outputs
apps/web/.next/*
apps/web/.next/build-manifest.json
apps/web/.next/server/middleware-build-manifest.js
dist/
build/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp