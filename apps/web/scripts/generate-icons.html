<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas" style="border: 1px solid #ccc;"></canvas>
    <div>
        <button onclick="generateIcon(72)">72x72</button>
        <button onclick="generateIcon(96)">96x96</button>
        <button onclick="generateIcon(128)">128x128</button>
        <button onclick="generateIcon(144)">144x144</button>
        <button onclick="generateIcon(152)">152x152</button>
        <button onclick="generateIcon(192)">192x192</button>
        <button onclick="generateIcon(384)">384x384</button>
        <button onclick="generateIcon(512)">512x512</button>
    </div>

    <script>
        function generateIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // Background
            ctx.fillStyle = '#0a0a0a';
            ctx.fillRect(0, 0, size, size);
            
            // Gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#8b5cf6');
            gradient.addColorStop(1, '#a855f7');
            
            // Lightning bolt
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.moveTo(size * 0.5, size * 0.125);
            ctx.lineTo(size * 0.375, size * 0.5);
            ctx.lineTo(size * 0.625, size * 0.5);
            ctx.lineTo(size * 0.5, size * 0.875);
            ctx.lineTo(size * 0.625, size * 0.5);
            ctx.lineTo(size * 0.375, size * 0.5);
            ctx.closePath();
            ctx.fill();
            
            // Download
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
