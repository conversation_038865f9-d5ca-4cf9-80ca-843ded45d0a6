import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>ap, TrendingUp, Calendar } from "lucide-react"

export default function TestStylingPage() {
  return (
    <div className="min-h-screen bg-dark-bg p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-white">XTask Design System Test</h1>
          <p className="text-muted-foreground">Testing Tailwind CSS and shadcn/ui integration</p>
        </div>

        {/* Color Palette */}
        <Card>
          <CardHeader>
            <CardTitle>Color Palette</CardTitle>
            <CardDescription>Custom XTask color system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground font-medium">Primary</span>
                </div>
                <p className="text-sm text-muted-foreground">#8b5cf6</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-dark-surface rounded-lg flex items-center justify-center border border-dark-border">
                  <span className="text-white font-medium">Surface</span>
                </div>
                <p className="text-sm text-muted-foreground">#1a1a1a</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-dark-border rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium">Border</span>
                </div>
                <p className="text-sm text-muted-foreground">#2a2a2a</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-dark-bg rounded-lg flex items-center justify-center border border-dark-border">
                  <span className="text-white font-medium">Background</span>
                </div>
                <p className="text-sm text-muted-foreground">#0a0a0a</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>Button Variants</CardTitle>
            <CardDescription>Different button styles and states</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button>Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="link">Link</Button>
              <Button loading>Loading</Button>
              <Button disabled>Disabled</Button>
            </div>
            <div className="mt-4 flex flex-wrap gap-4">
              <Button size="sm">Small</Button>
              <Button size="default">Default</Button>
              <Button size="lg">Large</Button>
              <Button size="icon">
                <Bot className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Cards and Badges */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
              <Bot className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-green-500 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +1 from last week
              </p>
              <div className="mt-3 flex gap-2">
                <Badge>Active</Badge>
                <Badge variant="secondary">AI</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tweets Today</CardTitle>
              <Zap className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-green-500 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +20% from yesterday
              </p>
              <div className="mt-3 flex gap-2">
                <Badge variant="outline">Published</Badge>
                <Badge variant="destructive">Failed: 1</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Calendar className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground mt-1">Next in 2 hours</p>
              <div className="mt-3">
                <Badge>Pending</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Form Elements */}
        <Card>
          <CardHeader>
            <CardTitle>Form Elements</CardTitle>
            <CardDescription>Input fields and form components</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Agent Name</label>
                <Input placeholder="Enter agent name..." />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Input placeholder="Agent description..." />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Error State</label>
              <Input placeholder="This field has an error" error="This field is required" />
            </div>
            <div className="flex gap-2">
              <Button>Save Agent</Button>
              <Button variant="outline">Cancel</Button>
            </div>
          </CardContent>
        </Card>

        {/* Gradient Examples */}
        <Card>
          <CardHeader>
            <CardTitle>Gradient Backgrounds</CardTitle>
            <CardDescription>Custom gradient utilities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="h-24 bg-gradient-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-medium">Primary Gradient</span>
              </div>
              <div className="h-24 bg-gradient-dark rounded-lg flex items-center justify-center border border-dark-border">
                <span className="text-white font-medium">Dark Gradient</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}