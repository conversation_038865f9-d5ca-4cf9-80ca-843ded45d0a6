"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/hooks/use-auth"
import { 
  Chrome, 
  Twitter, 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  Unlink,
  ExternalLink,
  RefreshCw
} from "lucide-react"

interface ConnectedAccount {
  id: string
  provider: "google" | "twitter"
  email?: string
  username?: string
  displayName: string
  profileImage?: string
  isActive: boolean
  connectedAt: string
  lastUsed?: string
}

export default function AccountsPage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [accounts, setAccounts] = useState<ConnectedAccount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isConnecting, setIsConnecting] = useState<string | null>(null)

  // Fetch connected accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await fetch("/api/auth/accounts")
        if (response.ok) {
          const data = await response.json()
          setAccounts(data.accounts || [])
        }
      } catch (error) {
        console.error("Failed to fetch accounts:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAccounts()
  }, [])

  const handleConnectAccount = async (provider: "google" | "twitter") => {
    setIsConnecting(provider)
    
    try {
      // Store the current page for redirect after OAuth
      sessionStorage.setItem("oauth_redirect", "/settings/accounts")
      
      // Redirect to OAuth flow
      window.location.href = `/api/auth/${provider}?link=true`
    } catch (error) {
      setIsConnecting(null)
      toast({
        title: "Connection Failed",
        description: `Failed to connect ${provider} account. Please try again.`,
        variant: "destructive",
      })
    }
  }

  const handleDisconnectAccount = async (accountId: string, provider: string) => {
    try {
      const response = await fetch(`/api/auth/accounts/${accountId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setAccounts(accounts.filter(account => account.id !== accountId))
        toast({
          title: "Account Disconnected",
          description: `Successfully disconnected your ${provider} account.`,
        })
      } else {
        throw new Error("Failed to disconnect account")
      }
    } catch (error) {
      toast({
        title: "Disconnection Failed",
        description: `Failed to disconnect ${provider} account. Please try again.`,
        variant: "destructive",
      })
    }
  }

  const handleRefreshAccount = async (accountId: string, provider: string) => {
    try {
      const response = await fetch(`/api/auth/accounts/${accountId}/refresh`, {
        method: "POST",
      })

      if (response.ok) {
        toast({
          title: "Account Refreshed",
          description: `Successfully refreshed your ${provider} account.`,
        })
        
        // Refresh the accounts list
        window.location.reload()
      } else {
        throw new Error("Failed to refresh account")
      }
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: `Failed to refresh ${provider} account. Please try again.`,
        variant: "destructive",
      })
    }
  }

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case "google":
        return <Chrome className="h-5 w-5 text-blue-600" />
      case "twitter":
        return <Twitter className="h-5 w-5 text-blue-500" />
      default:
        return <Shield className="h-5 w-5" />
    }
  }

  const getProviderName = (provider: string) => {
    switch (provider) {
      case "google":
        return "Google"
      case "twitter":
        return "Twitter/X"
      default:
        return provider
    }
  }

  const availableProviders = [
    {
      id: "google",
      name: "Google",
      description: "Connect your Google account for enhanced features",
      icon: Chrome,
      color: "text-blue-600",
    },
    {
      id: "twitter",
      name: "Twitter/X",
      description: "Connect your Twitter account to manage tweets",
      icon: Twitter,
      color: "text-blue-500",
    },
  ]

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-32 bg-muted rounded"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Connected Accounts</h1>
        <p className="text-muted-foreground mt-2">
          Manage your connected social media accounts and authentication providers.
        </p>
      </div>

      {/* Security Notice */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Your account connections are secured with OAuth 2.0 and encrypted token storage. 
          We never store your passwords.
        </AlertDescription>
      </Alert>

      {/* Connected Accounts */}
      <Card>
        <CardHeader>
          <CardTitle>Connected Accounts</CardTitle>
          <CardDescription>
            Accounts currently linked to your profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {accounts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No accounts connected yet</p>
              <p className="text-sm">Connect your accounts below to get started</p>
            </div>
          ) : (
            accounts.map((account) => (
              <div key={account.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  {getProviderIcon(account.provider)}
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{getProviderName(account.provider)}</h3>
                      {account.isActive ? (
                        <Badge variant="secondary" className="text-green-600">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Inactive
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {account.email || account.username || account.displayName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Connected {new Date(account.connectedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRefreshAccount(account.id, account.provider)}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDisconnectAccount(account.id, account.provider)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Unlink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Available Providers */}
      <Card>
        <CardHeader>
          <CardTitle>Connect New Account</CardTitle>
          <CardDescription>
            Add additional accounts to expand your capabilities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {availableProviders.map((provider) => {
            const isConnected = accounts.some(account => account.provider === provider.id)
            const isConnectingThis = isConnecting === provider.id
            
            return (
              <div key={provider.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <provider.icon className={`h-5 w-5 ${provider.color}`} />
                  <div>
                    <h3 className="font-medium">{provider.name}</h3>
                    <p className="text-sm text-muted-foreground">{provider.description}</p>
                  </div>
                </div>
                
                <Button
                  onClick={() => handleConnectAccount(provider.id as "google" | "twitter")}
                  disabled={isConnected || isConnectingThis}
                  variant={isConnected ? "secondary" : "default"}
                >
                  {isConnectingThis ? (
                    "Connecting..."
                  ) : isConnected ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Connected
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Connect
                    </>
                  )}
                </Button>
              </div>
            )
          })}
        </CardContent>
      </Card>
    </div>
  )
}
