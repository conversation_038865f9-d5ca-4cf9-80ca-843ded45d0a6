import * as React from 'react'
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AgentMemoryPanel } from '@/components/agents/agent-memory-panel'
import { EnhancedContentGenerator } from '@/components/ai/enhanced-content-generator'
import { Badge } from '@/components/ui/badge'
import { Brain, Sparkles, Bot, Zap } from 'lucide-react'

export default function AgentMemoryPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <div className="flex items-center space-x-2">
          <h1 className="text-3xl font-bold text-white">Agent Memory</h1>
          <Badge className="ml-2">
            <Brain className="h-3 w-3 mr-1" />
            Vector Embeddings
          </Badge>
        </div>
        <p className="text-muted-foreground mt-1">
          Manage agent memories and generate content with contextual awareness
        </p>
      </div>

      {/* Memory System Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Memory System Overview</CardTitle>
          <CardDescription>
            How vector embeddings enhance your agents' capabilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
                  <Brain className="h-4 w-4 text-white" />
                </div>
                <h3 className="font-medium">Vector Embeddings</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Agent memories are stored as high-dimensional vectors that capture semantic meaning,
                enabling similarity search beyond simple keyword matching.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
                <h3 className="font-medium">Contextual Awareness</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Agents can recall relevant memories based on context, making responses more
                consistent and personalized over time.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
                  <Bot className="h-4 w-4 text-white" />
                </div>
                <h3 className="font-medium">Gemini Integration</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Google Gemini models provide advanced reasoning capabilities and cost-effective
                content generation with multimodal support.
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-dark-surface rounded-lg border border-dark-border">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <h3 className="font-medium">How It Works</h3>
            </div>
            <ol className="space-y-2 text-sm text-muted-foreground list-decimal list-inside">
              <li>Agent memories are converted to vector embeddings using advanced AI models</li>
              <li>When generating content, relevant memories are retrieved using similarity search</li>
              <li>These memories provide context to the AI, improving response quality</li>
              <li>New interactions can be stored as memories for future reference</li>
              <li>The system automatically selects the most cost-effective model for each task</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Memory Management */}
        <AgentMemoryPanel agentId="your-agent-id" />

        {/* Enhanced Content Generator */}
        <EnhancedContentGenerator />
      </div>
    </div>
  )
}