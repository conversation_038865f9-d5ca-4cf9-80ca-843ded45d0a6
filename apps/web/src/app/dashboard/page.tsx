'use client'

import * as React from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Bot, Calendar, TrendingUp, Zap, Plus, BarChart3, Clock, AlertCircle } from "lucide-react"
import { useAgentStats, useAgents } from '@/hooks/use-agents'
import { useScheduledTweets } from '@/hooks/use-schedule'
import { format } from 'date-fns'

export default function DashboardPage() {
  const { data: agentStats, isLoading: statsLoading } = useAgentStats()
  const { data: agentsData, isLoading: agentsLoading } = useAgents({
    limit: 3,
    sortBy: 'tweetsGenerated',
    sortOrder: 'desc'
  })
  const { data: scheduledData, isLoading: scheduledLoading } = useScheduledTweets({
    limit: 3,
    status: 'scheduled'
  })

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Welcome Section */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Welcome back!</h1>
          <p className="text-muted-foreground mt-1 text-sm sm:text-base">Here's what's happening with your AI agents today.</p>
        </div>
        <Link href="/dashboard/agents">
          <Button className="flex items-center space-x-2 w-full sm:w-auto">
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Create Agent</span>
            <span className="sm:hidden">Create</span>
          </Button>
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
            <Bot className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{agentStats?.activeAgents || 0}</div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              {statsLoading ? (
                <Skeleton className="h-4 w-20" />
              ) : (
                `${agentStats?.totalAgents || 0} total agents`
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tweets</CardTitle>
            <Zap className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{agentStats?.totalTweets || 0}</div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              Generated by agents
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            {scheduledLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{scheduledData?.pagination.total || 0}</div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              {scheduledData?.tweets?.[0] ? (
                <>Next: {format(new Date(scheduledData.tweets[0].scheduledFor!), 'MMM d, h:mm a')}</>
              ) : (
                'No upcoming tweets'
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Engagement</CardTitle>
            <BarChart3 className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">
                {agentStats?.avgEngagementRate ? `${(agentStats.avgEngagementRate * 100).toFixed(1)}%` : '0%'}
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              Across all agents
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Agents</CardTitle>
              <CardDescription>Your most active AI agents</CardDescription>
            </div>
            <Link href="/dashboard/agents">
              <Button variant="outline" size="sm">View All</Button>
            </Link>
          </CardHeader>
          <CardContent className="space-y-4">
            {agentsLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-dark-surface/50">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-5 w-12" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                </div>
              ))
            ) : agentsData?.agents.length ? (
              agentsData.agents.map((agent) => (
                <div key={agent.id} className="flex items-center justify-between p-3 rounded-lg bg-dark-surface/50">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center">
                      <Bot className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium">{agent.name}</p>
                      <p className="text-sm text-muted-foreground">{agent.tweetsGenerated} tweets generated</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={agent.isActive ? "default" : "secondary"}>
                      {agent.isActive ? "active" : "inactive"}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {(agent.engagementRate * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No agents created yet</p>
                <Link href="/dashboard/agents">
                  <Button className="mt-2" size="sm">Create Your First Agent</Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Upcoming Schedule</CardTitle>
              <CardDescription>Next tweets to be published</CardDescription>
            </div>
            <Link href="/dashboard/schedule">
              <Button variant="outline" size="sm">View All</Button>
            </Link>
          </CardHeader>
          <CardContent className="space-y-4">
            {scheduledLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="p-3 rounded-lg bg-dark-surface/50">
                  <Skeleton className="h-4 w-full mb-2" />
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))
            ) : scheduledData?.tweets.length ? (
              scheduledData.tweets.map((tweet) => (
                <div key={tweet.id} className="p-3 rounded-lg bg-dark-surface/50">
                  <p className="text-sm mb-2 line-clamp-2">{tweet.content}</p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span className="flex items-center">
                      <Bot className="h-3 w-3 mr-1" />
                      {tweet.agent?.name || 'Unknown Agent'}
                    </span>
                    <span className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {tweet.scheduledFor ? format(new Date(tweet.scheduledFor), 'MMM d, h:mm a') : 'No date'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No scheduled tweets</p>
                <Link href="/dashboard/compose">
                  <Button className="mt-2" size="sm">Schedule Your First Tweet</Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}