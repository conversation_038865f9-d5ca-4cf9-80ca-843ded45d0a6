import * as React from 'react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DraftList } from '@/components/drafts/draft-list';
import { SingleTweetComposer } from '@/components/compose/single-tweet-composer';
import { ThreadComposer } from '@/components/compose/thread-composer';
import { PenTool, MessageSquare, FileText, Plus } from 'lucide-react';
import type { Draft } from '@/types/draft';

export default function ComposePage() {
  const [showSingleComposer, setShowSingleComposer] = useState(false);
  const [showThreadComposer, setShowThreadComposer] = useState(false);
  const [editingDraft, setEditingDraft] = useState<Draft | null>(null);
  const [viewingThreadId, setViewingThreadId] = useState<string | null>(null);

  const handleCreateSingle = () => {
    setShowSingleComposer(true);
  };

  const handleCreateThread = () => {
    setShowThreadComposer(true);
  };

  const handleEditDraft = (draft: Draft) => {
    setEditingDraft(draft);
    setShowSingleComposer(true);
  };

  const handleViewThread = (threadId: string) => {
    setViewingThreadId(threadId);
    // TODO: Implement thread viewer
  };

  const handleCloseSingleComposer = () => {
    setShowSingleComposer(false);
    setEditingDraft(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Compose</h1>
          <p className="text-muted-foreground mt-1">Create tweets, threads, and manage your drafts</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-primary-500/20" onClick={handleCreateSingle}>
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 rounded-lg bg-gradient-primary flex items-center justify-center mb-4">
              <PenTool className="h-6 w-6 text-white" />
            </div>
            <CardTitle>Single Tweet</CardTitle>
            <CardDescription>
              Compose a single tweet with text and media
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" onClick={handleCreateSingle}>
              <Plus className="h-4 w-4 mr-2" />
              Create Tweet
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-primary-500/20" onClick={handleCreateThread}>
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 rounded-lg bg-gradient-primary flex items-center justify-center mb-4">
              <MessageSquare className="h-6 w-6 text-white" />
            </div>
            <CardTitle>Thread</CardTitle>
            <CardDescription>
              Create a multi-tweet thread with connected content
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" onClick={handleCreateThread}>
              <Plus className="h-4 w-4 mr-2" />
              Create Thread
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-primary-500/20">
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 rounded-lg bg-gradient-primary flex items-center justify-center mb-4">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <CardTitle>Drafts</CardTitle>
            <CardDescription>
              View and manage your saved drafts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              <FileText className="h-4 w-4 mr-2" />
              View Drafts
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="drafts" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="drafts">Drafts</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
        </TabsList>

        <TabsContent value="drafts" className="space-y-6">
          <DraftList
            onCreateDraft={handleCreateSingle}
            onEditDraft={handleEditDraft}
            onViewThread={handleViewThread}
          />
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <MessageSquare className="h-12 w-12 text-muted-foreground mb-4 mx-auto" />
                <h3 className="text-lg font-medium mb-2">Scheduled Tweets</h3>
                <p className="text-muted-foreground">
                  Scheduled tweets will appear here once you schedule your drafts.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Composers */}
      <SingleTweetComposer
        open={showSingleComposer}
        onOpenChange={handleCloseSingleComposer}
        draft={editingDraft || undefined}
      />

      <ThreadComposer
        open={showThreadComposer}
        onOpenChange={setShowThreadComposer}
      />
    </div>
  );
}