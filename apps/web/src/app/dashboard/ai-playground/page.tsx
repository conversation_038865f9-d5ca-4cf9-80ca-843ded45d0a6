import { ContentGenerator } from '@/components/ai/content-generator'
import { useAvailableModels } from '@/hooks/use-ai-generation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Sparkles, Cpu, Globe } from 'lucide-react'

export default function AIPlaygroundPage() {
  const { data: modelsData } = useAvailableModels()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white">AI Playground</h1>
        <p className="text-muted-foreground mt-1">
          Experiment with AI content generation using your agents' personas
        </p>
      </div>

      {/* Provider Info */}
      {modelsData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>AI Provider Information</span>
            </CardTitle>
            <CardDescription>
              Current OpenAI-compatible API configuration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Provider</p>
                <p className="text-lg font-semibold">{modelsData.provider.replace('https://', '').replace('/v1', '')}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Default Model</p>
                <p className="text-lg font-semibold">{modelsData.defaultModel}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Available Models</p>
                <p className="text-lg font-semibold">{modelsData.models.length}</p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-2">Models</p>
              <div className="flex flex-wrap gap-2">
                {modelsData.models.slice(0, 10).map((model) => (
                  <Badge key={model.id} variant="outline" className="text-xs">
                    <Cpu className="h-3 w-3 mr-1" />
                    {model.name}
                  </Badge>
                ))}
                {modelsData.models.length > 10 && (
                  <Badge variant="secondary" className="text-xs">
                    +{modelsData.models.length - 10} more
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content Generator */}
      <ContentGenerator />
    </div>
  )
}