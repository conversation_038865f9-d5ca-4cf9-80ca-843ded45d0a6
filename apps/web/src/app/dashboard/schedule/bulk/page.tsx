'use client'

import * as React from 'react'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar, 
  Clock, 
  Upload, 
  Download, 
  Plus,
  BarChart3,
  Users,
  Zap,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'
import { BulkScheduleDialog } from '@/components/schedule/bulk-schedule-dialog'
import { ScheduledTweetsList } from '@/components/schedule/scheduled-tweets-list'

export default function BulkSchedulePage() {
  const [showBulkDialog, setShowBulkDialog] = useState(false)

  const features = [
    {
      icon: Calendar,
      title: 'Batch Scheduling',
      description: 'Schedule multiple tweets at once with custom timing',
      color: 'text-blue-500',
    },
    {
      icon: Clock,
      title: 'Auto Timing',
      description: 'Automatically space tweets with intelligent intervals',
      color: 'text-green-500',
    },
    {
      icon: Upload,
      title: 'CSV Import',
      description: 'Import tweet content and schedules from CSV files',
      color: 'text-purple-500',
    },
    {
      icon: BarChart3,
      title: 'Timezone Support',
      description: 'Schedule across different timezones with automatic conversion',
      color: 'text-orange-500',
    },
  ]

  const quickActions = [
    {
      title: 'Schedule 5 Tweets',
      description: 'Quick setup for a small batch',
      action: () => setShowBulkDialog(true),
      icon: Plus,
    },
    {
      title: 'Import from CSV',
      description: 'Upload a CSV file with your content',
      action: () => {
        // Trigger CSV import
        setShowBulkDialog(true)
      },
      icon: Upload,
    },
    {
      title: 'Download Template',
      description: 'Get a CSV template to fill out',
      action: () => {
        const csv = 'Content,Scheduled For\n"Your tweet content here","2024-01-01T12:00"\n"Another tweet","2024-01-01T13:00"'
        const blob = new Blob([csv], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'bulk-schedule-template.csv'
        a.click()
        URL.revokeObjectURL(url)
      },
      icon: Download,
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Bulk Scheduling</h1>
          <p className="text-muted-foreground mt-1">
            Schedule multiple tweets efficiently with advanced timing controls
          </p>
        </div>
        <Button onClick={() => setShowBulkDialog(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          New Bulk Schedule
        </Button>
      </div>

      {/* Features Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {features.map((feature, index) => (
          <Card key={index}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-3">
                <feature.icon className={`h-8 w-8 ${feature.color}`} />
                <div>
                  <h3 className="font-medium">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {feature.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Quick Actions</span>
          </CardTitle>
          <CardDescription>
            Get started quickly with these common bulk scheduling tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {quickActions.map((action, index) => (
              <Card key={index} className="cursor-pointer hover:bg-muted/50 transition-colors">
                <CardContent className="pt-6" onClick={action.action}>
                  <div className="text-center space-y-3">
                    <action.icon className="h-8 w-8 mx-auto text-primary" />
                    <div>
                      <h3 className="font-medium">{action.title}</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tips & Best Practices */}
      <Card className="border-blue-500/20 bg-blue-500/5">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5 text-blue-500" />
            <span>Bulk Scheduling Tips</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">Timing Best Practices</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Space tweets at least 30 minutes apart</li>
                <li>• Avoid scheduling during off-peak hours</li>
                <li>• Consider your audience's timezone</li>
                <li>• Skip weekends for business content</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Content Guidelines</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Keep tweets under 280 characters</li>
                <li>• Include relevant hashtags and mentions</li>
                <li>• Mix different content types</li>
                <li>• Preview before scheduling</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Scheduled Tweets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Scheduled Tweets</span>
          </CardTitle>
          <CardDescription>
            View and manage your currently scheduled tweets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScheduledTweetsList />
        </CardContent>
      </Card>

      {/* Bulk Schedule Dialog */}
      <BulkScheduleDialog
        open={showBulkDialog}
        onOpenChange={setShowBulkDialog}
      />
    </div>
  )
}
