'use client'

import * as React from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  Plus,
  Users,
  BarChart3,
  Zap,
  ArrowRight
} from 'lucide-react'
import { ScheduledTweetsList } from '@/components/schedule/scheduled-tweets-list'
import { useScheduledTweets } from '@/hooks/use-schedule'

export default function SchedulePage() {
  const { data: scheduledData } = useScheduledTweets({ limit: 5 })

  const stats = [
    {
      title: 'Total Scheduled',
      value: scheduledData?.pagination.total || 0,
      icon: Calendar,
      color: 'text-blue-500',
    },
    {
      title: 'Next 24 Hours',
      value: scheduledData?.tweets.filter(tweet => {
        if (!tweet.scheduledFor) return false
        const scheduledTime = new Date(tweet.scheduledFor)
        const now = new Date()
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
        return scheduledTime >= now && scheduledTime <= tomorrow
      }).length || 0,
      icon: Clock,
      color: 'text-green-500',
    },
    {
      title: 'Active Agents',
      value: new Set(scheduledData?.tweets.map(tweet => tweet.agentId)).size || 0,
      icon: Users,
      color: 'text-purple-500',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Schedule</h1>
          <p className="text-muted-foreground mt-1">
            Manage your scheduled tweets and timing
          </p>
        </div>
        <div className="flex space-x-2">
          <Link href="/dashboard/compose">
            <Button variant="outline" className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Schedule Tweet
            </Button>
          </Link>
          <Link href="/dashboard/schedule/bulk">
            <Button className="w-full sm:w-auto">
              <Zap className="h-4 w-4 mr-2" />
              Bulk Schedule
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-3">
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
                <div>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <Link href="/dashboard/schedule/bulk">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Zap className="h-8 w-8 text-primary" />
                  <div>
                    <h3 className="font-medium">Bulk Scheduling</h3>
                    <p className="text-sm text-muted-foreground">
                      Schedule multiple tweets with advanced timing
                    </p>
                  </div>
                </div>
                <ArrowRight className="h-5 w-5 text-muted-foreground" />
              </div>
            </CardContent>
          </Link>
        </Card>

        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <Link href="/dashboard/analytics">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-8 w-8 text-green-500" />
                  <div>
                    <h3 className="font-medium">Schedule Analytics</h3>
                    <p className="text-sm text-muted-foreground">
                      Analyze your posting patterns and timing
                    </p>
                  </div>
                </div>
                <ArrowRight className="h-5 w-5 text-muted-foreground" />
              </div>
            </CardContent>
          </Link>
        </Card>
      </div>

      {/* Scheduled Tweets List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Scheduled Tweets</span>
          </CardTitle>
          <CardDescription>
            Your upcoming scheduled tweets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScheduledTweetsList />
        </CardContent>
      </Card>
    </div>
  )
}