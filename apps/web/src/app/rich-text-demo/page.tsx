"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { RichTextEditor } from '@/components/ui/rich-text-editor'
import { useToast } from '@/hooks/use-toast'
import { 
  Send, 
  Save, 
  RefreshCw, 
  Eye, 
  Code,
  Sparkles
} from 'lucide-react'

interface MediaFile {
  url: string
  fileId: string
  fileType?: string
  filename?: string
}

export default function RichTextDemo() {
  const { toast } = useToast()
  const [content, setContent] = useState('')
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [validation, setValidation] = useState<any>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  const handleMediaUpload = (files: MediaFile[]) => {
    setMediaFiles(prev => [...prev, ...files])
    toast({
      title: "Media uploaded",
      description: `${files.length} file(s) uploaded successfully`,
    })
  }

  const handleMediaRemove = (fileId: string) => {
    setMediaFiles(prev => prev.filter(file => file.fileId !== fileId))
    toast({
      title: "Media removed",
      description: "File removed from tweet",
    })
  }

  const handleValidationChange = (newValidation: any) => {
    setValidation(newValidation)
  }

  const handleSubmit = async () => {
    if (!validation?.isValid) {
      toast({
        title: "Invalid tweet",
        description: "Please fix the errors before submitting",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast({
      title: "Tweet posted!",
      description: "Your tweet has been posted successfully",
      variant: "success",
    })
    
    setIsSubmitting(false)
    setContent('')
    setMediaFiles([])
  }

  const handleSaveDraft = () => {
    toast({
      title: "Draft saved",
      description: "Your tweet has been saved as a draft",
    })
  }

  const handleClear = () => {
    setContent('')
    setMediaFiles([])
    toast({
      title: "Cleared",
      description: "Editor content has been cleared",
    })
  }

  const generateSampleContent = () => {
    const samples = [
      "Just shipped a new feature! 🚀 The rich text editor now supports media uploads, real-time character counting, and Twitter-specific validation. #WebDev #React",
      "Working on something exciting today! 💻 Building the future of social media management with AI-powered agents. What do you think? 🤔",
      "Pro tip: Always validate your tweets before posting! ✅ Our new editor helps you stay within character limits and catch common mistakes. #TwitterTips",
    ]
    
    const randomSample = samples[Math.floor(Math.random() * samples.length)]
    setContent(randomSample)
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold">Rich Text Tweet Composer</h1>
        <p className="text-muted-foreground">
          Advanced tweet composer with rich text editing, media uploads, and real-time validation
        </p>
      </div>

      {/* Main Composer */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Compose Tweet
          </CardTitle>
          <CardDescription>
            Create engaging tweets with rich text formatting and media
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <RichTextEditor
            content={content}
            onChange={handleContentChange}
            onMediaUpload={handleMediaUpload}
            mediaFiles={mediaFiles}
            onMediaRemove={handleMediaRemove}
            onValidationChange={handleValidationChange}
            placeholder="What's happening?"
            maxLength={280}
            showToolbar={true}
            showCharacterCount={true}
            autoFocus={true}
          />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={generateSampleContent}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Sample
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Clear
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? <Code className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                {showPreview ? 'Edit' : 'Preview'}
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={!content.trim()}
              >
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </Button>
              
              <Button
                onClick={handleSubmit}
                disabled={!validation?.isValid || isSubmitting || !content.trim()}
              >
                {isSubmitting ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                {isSubmitting ? 'Posting...' : 'Post Tweet'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      {showPreview && content && (
        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>
              How your tweet will appear on Twitter
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-muted/50">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-medium">
                  U
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">Your Name</span>
                    <span className="text-muted-foreground">@username</span>
                    <span className="text-muted-foreground">·</span>
                    <span className="text-muted-foreground">now</span>
                  </div>
                  <div className="whitespace-pre-wrap">{content}</div>
                  {mediaFiles.length > 0 && (
                    <div className="mt-3 grid grid-cols-2 gap-2">
                      {mediaFiles.slice(0, 4).map((file) => (
                        <div key={file.fileId} className="aspect-video bg-muted rounded-md overflow-hidden">
                          {file.fileType?.startsWith('image/') ? (
                            <img
                              src={file.url}
                              alt="Preview"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                              Media
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Validation Status */}
      {validation && (
        <Card>
          <CardHeader>
            <CardTitle>Validation Status</CardTitle>
            <CardDescription>
              Real-time validation feedback
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{validation.characterCount}</div>
                <div className="text-sm text-muted-foreground">Characters</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{validation.remainingCharacters}</div>
                <div className="text-sm text-muted-foreground">Remaining</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{mediaFiles.length}</div>
                <div className="text-sm text-muted-foreground">Media Files</div>
              </div>
              <div className="text-center">
                <Badge variant={validation.isValid ? "default" : "destructive"}>
                  {validation.isValid ? "Valid" : "Invalid"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Features</CardTitle>
          <CardDescription>
            What makes this rich text editor special
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Rich Text Editing</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Bold and italic formatting</li>
                <li>• Link insertion and editing</li>
                <li>• Undo/redo functionality</li>
                <li>• Keyboard shortcuts</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Twitter Integration</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Real-time character counting</li>
                <li>• URL length calculation</li>
                <li>• Mention and hashtag detection</li>
                <li>• Twitter-specific validation</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Media Support</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Image and video uploads</li>
                <li>• Media preview and management</li>
                <li>• Drag and drop support</li>
                <li>• File type validation</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">User Experience</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Real-time validation feedback</li>
                <li>• Error and warning messages</li>
                <li>• Responsive design</li>
                <li>• Accessibility support</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
