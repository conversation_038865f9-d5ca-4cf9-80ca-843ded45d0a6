"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Import all our enhanced components
import { Modal, ConfirmModal, AlertModal, FormModal } from "@/components/ui/modal"
import { UserAvatar, AvatarGroup } from "@/components/ui/avatar"
import { ActionMenu, UserMenu } from "@/components/ui/dropdown-menu"
import { EnhancedTabs, VerticalTabs } from "@/components/ui/tabs"
import { Toaster } from "@/components/ui/toaster"
import { useToast } from "@/hooks/use-toast"
import { Spinner, DotsSpinner, PulseSpinner, LoadingOverlay } from "@/components/ui/spinner"
import { Progress, CircularProgress, StepProgress } from "@/components/ui/progress"
import { ThemeToggle, ThemeSwitch, ThemeSelector } from "@/components/ui/theme-toggle"

// Icons
import { Settings, User, LogOut, Edit, Trash2, Copy, Share, Download } from "lucide-react"

export default function ComponentsDemo() {
  const { toast } = useToast()
  const [modalOpen, setModalOpen] = useState(false)
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [alertModalOpen, setAlertModalOpen] = useState(false)
  const [formModalOpen, setFormModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [progress, setProgress] = useState(33)

  // Sample data
  const avatars = [
    { name: "John Doe", src: "/api/placeholder/32/32" },
    { name: "Jane Smith", src: "/api/placeholder/32/32" },
    { name: "Bob Johnson", src: "/api/placeholder/32/32" },
    { name: "Alice Brown", src: "/api/placeholder/32/32" },
    { name: "Charlie Wilson", src: "/api/placeholder/32/32" },
  ]

  const actionMenuItems = [
    { label: "Edit", icon: Edit, onClick: () => toast({ title: "Edit clicked" }) },
    { label: "Copy", icon: Copy, onClick: () => toast({ title: "Copied!" }) },
    { label: "Share", icon: Share, onClick: () => toast({ title: "Shared!" }) },
    { separator: true, label: "" },
    { label: "Download", icon: Download, onClick: () => toast({ title: "Downloaded!" }) },
    { label: "Delete", icon: Trash2, onClick: () => toast({ title: "Deleted!", variant: "error" }), destructive: true },
  ]

  const tabItems = [
    {
      value: "overview",
      label: "Overview",
      content: <div className="p-4">Overview content goes here...</div>
    },
    {
      value: "analytics",
      label: "Analytics",
      badge: "New",
      content: <div className="p-4">Analytics dashboard content...</div>
    },
    {
      value: "settings",
      label: "Settings",
      icon: Settings,
      content: <div className="p-4">Settings panel content...</div>
    }
  ]

  const steps = [
    { label: "Account Setup", description: "Create your account", completed: true },
    { label: "Profile", description: "Complete your profile", completed: true },
    { label: "Verification", description: "Verify your email", current: true },
    { label: "Complete", description: "You're all set!" },
  ]

  const simulateLoading = () => {
    setLoading(true)
    setTimeout(() => setLoading(false), 2000)
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold">UI Components Showcase</h1>
        <p className="text-muted-foreground">
          Comprehensive demonstration of all enhanced UI components
        </p>
      </div>

      {/* Modals Section */}
      <Card>
        <CardHeader>
          <CardTitle>Modal Components</CardTitle>
          <CardDescription>Enhanced modal dialogs with different variants</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button onClick={() => setModalOpen(true)}>Basic Modal</Button>
            <Button onClick={() => setConfirmModalOpen(true)} variant="outline">Confirm Modal</Button>
            <Button onClick={() => setAlertModalOpen(true)} variant="secondary">Alert Modal</Button>
            <Button onClick={() => setFormModalOpen(true)} variant="ghost">Form Modal</Button>
          </div>

          <Modal
            open={modalOpen}
            onOpenChange={setModalOpen}
            title="Basic Modal"
            description="This is a basic modal example"
            size="md"
          >
            <div className="space-y-4">
              <p>This is the modal content. You can put any content here.</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setModalOpen(false)}>Cancel</Button>
                <Button onClick={() => setModalOpen(false)}>Save</Button>
              </div>
            </div>
          </Modal>

          <ConfirmModal
            open={confirmModalOpen}
            onOpenChange={setConfirmModalOpen}
            title="Confirm Action"
            description="Are you sure you want to proceed? This action cannot be undone."
            onConfirm={() => toast({ title: "Action confirmed!" })}
            variant="destructive"
          />

          <AlertModal
            open={alertModalOpen}
            onOpenChange={setAlertModalOpen}
            title="Success!"
            description="Your action was completed successfully."
            type="success"
          />

          <FormModal
            open={formModalOpen}
            onOpenChange={setFormModalOpen}
            title="Create New Item"
            description="Fill out the form below to create a new item."
            onSubmit={() => {
              toast({ title: "Form submitted!" })
              setFormModalOpen(false)
            }}
          >
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Name</label>
                <input className="w-full mt-1 px-3 py-2 border rounded-md" placeholder="Enter name" />
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <textarea className="w-full mt-1 px-3 py-2 border rounded-md" placeholder="Enter description" />
              </div>
            </div>
          </FormModal>
        </CardContent>
      </Card>

      {/* Avatars Section */}
      <Card>
        <CardHeader>
          <CardTitle>Avatar Components</CardTitle>
          <CardDescription>User avatars with status indicators and groups</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Individual Avatars</h4>
            <div className="flex items-center gap-4">
              <UserAvatar name="John Doe" size="sm" />
              <UserAvatar name="Jane Smith" size="md" showStatus status="online" />
              <UserAvatar name="Bob Johnson" size="lg" showStatus status="away" />
              <UserAvatar name="Alice Brown" size="xl" showStatus status="busy" />
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-3">Avatar Group</h4>
            <AvatarGroup avatars={avatars} max={4} size="md" />
          </div>
        </CardContent>
      </Card>

      {/* Dropdown Menus Section */}
      <Card>
        <CardHeader>
          <CardTitle>Dropdown Menu Components</CardTitle>
          <CardDescription>Action menus and user menus with enhanced features</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <ActionMenu items={actionMenuItems} />
            <UserMenu
              user={{
                name: "John Doe",
                email: "<EMAIL>",
                avatar: "/api/placeholder/32/32"
              }}
              onProfile={() => toast({ title: "Profile clicked" })}
              onSettings={() => toast({ title: "Settings clicked" })}
              onSignOut={() => toast({ title: "Signed out" })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Continue with more components... */}
      <Card>
        <CardHeader>
          <CardTitle>Loading States</CardTitle>
          <CardDescription>Various loading indicators and overlays</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Spinners</h4>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <Spinner size="sm" />
                <p className="text-xs mt-1">Small</p>
              </div>
              <div className="text-center">
                <DotsSpinner size="md" />
                <p className="text-xs mt-1">Dots</p>
              </div>
              <div className="text-center">
                <PulseSpinner size="lg" />
                <p className="text-xs mt-1">Pulse</p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Loading Overlay</h4>
            <LoadingOverlay isLoading={loading} text="Processing...">
              <div className="h-32 bg-muted rounded-md flex items-center justify-center">
                <Button onClick={simulateLoading}>Trigger Loading</Button>
              </div>
            </LoadingOverlay>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Section */}
      <Card>
        <CardHeader>
          <CardTitle>Tab Components</CardTitle>
          <CardDescription>Enhanced tabs with icons, badges, and variants</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Horizontal Tabs</h4>
            <EnhancedTabs items={tabItems} variant="default" />
          </div>

          <div>
            <h4 className="font-medium mb-3">Vertical Tabs</h4>
            <VerticalTabs items={tabItems} variant="line" />
          </div>
        </CardContent>
      </Card>

      {/* Progress Section */}
      <Card>
        <CardHeader>
          <CardTitle>Progress Components</CardTitle>
          <CardDescription>Progress bars, circular progress, and step indicators</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Linear Progress</h4>
            <div className="space-y-4">
              <Progress value={progress} showValue />
              <Progress value={75} color="success" size="lg" />
              <div className="flex gap-2">
                <Button size="sm" onClick={() => setProgress(Math.max(0, progress - 10))}>-10</Button>
                <Button size="sm" onClick={() => setProgress(Math.min(100, progress + 10))}>+10</Button>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Circular Progress</h4>
            <div className="flex gap-6">
              <CircularProgress value={progress} size={100} />
              <CircularProgress value={85} size={120} color="hsl(var(--destructive))" />
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Step Progress</h4>
            <StepProgress steps={steps} currentStep={2} />
          </div>
        </CardContent>
      </Card>

      {/* Theme Toggle Section */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Components</CardTitle>
          <CardDescription>Theme toggles and selectors</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Theme Toggle Variants</h4>
            <div className="flex items-center gap-4">
              <ThemeToggle />
              <ThemeSwitch />
              <ThemeSelector />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Toast notification trigger */}
      <Card>
        <CardHeader>
          <CardTitle>Toast Notifications</CardTitle>
          <CardDescription>Toast messages with different variants</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button onClick={() => toast({ title: "Default toast" })}>Default</Button>
            <Button onClick={() => toast({ title: "Success!", variant: "success" })} variant="outline">Success</Button>
            <Button onClick={() => toast({ title: "Warning!", variant: "warning" })} variant="secondary">Warning</Button>
            <Button onClick={() => toast({ title: "Error!", variant: "error" })} variant="destructive">Error</Button>
          </div>
        </CardContent>
      </Card>

      {/* Component Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Component Summary</CardTitle>
          <CardDescription>Overview of all implemented components</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Badge variant="secondary">Modal Components</Badge>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Basic Modal</li>
                <li>• Confirm Modal</li>
                <li>• Alert Modal</li>
                <li>• Form Modal</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Badge variant="secondary">Avatar Components</Badge>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• User Avatar</li>
                <li>• Avatar Group</li>
                <li>• Status Indicators</li>
                <li>• Size Variants</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Badge variant="secondary">Dropdown Menus</Badge>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Action Menu</li>
                <li>• User Menu</li>
                <li>• Enhanced Features</li>
                <li>• Icon Support</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Badge variant="secondary">Tab Components</Badge>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Enhanced Tabs</li>
                <li>• Vertical Tabs</li>
                <li>• Icon & Badge Support</li>
                <li>• Multiple Variants</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Badge variant="secondary">Loading States</Badge>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Spinner Variants</li>
                <li>• Dots Spinner</li>
                <li>• Pulse Spinner</li>
                <li>• Loading Overlay</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Badge variant="secondary">Progress Components</Badge>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Linear Progress</li>
                <li>• Circular Progress</li>
                <li>• Step Progress</li>
                <li>• Color Variants</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
