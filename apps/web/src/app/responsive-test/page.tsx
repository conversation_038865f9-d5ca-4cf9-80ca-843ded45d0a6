'use client'

import * as React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Tv, 
  CheckCircle,
  Grid3X3,
  Layout,
  Palette,
  Zap
} from "lucide-react"

export default function ResponsiveTestPage() {
  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">
          Responsive Design Test
        </h1>
        <p className="text-muted-foreground text-sm sm:text-base">
          Testing responsive breakpoints and mobile-first design
        </p>
      </div>

      {/* Breakpoint Indicators */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Monitor className="h-5 w-5" />
            <span>Current Breakpoint</span>
          </CardTitle>
          <CardDescription>
            Visual indicators for different screen sizes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-red-500/10 border border-red-500/20 sm:hidden">
              <Smartphone className="h-8 w-8 text-red-500" />
              <span className="text-sm font-medium text-red-500">Mobile</span>
              <span className="text-xs text-muted-foreground">&lt; 640px</span>
            </div>
            
            <div className="hidden sm:flex md:hidden flex-col items-center space-y-2 p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
              <Tablet className="h-8 w-8 text-yellow-500" />
              <span className="text-sm font-medium text-yellow-500">Small</span>
              <span className="text-xs text-muted-foreground">640px+</span>
            </div>
            
            <div className="hidden md:flex lg:hidden flex-col items-center space-y-2 p-4 rounded-lg bg-blue-500/10 border border-blue-500/20">
              <Tablet className="h-8 w-8 text-blue-500" />
              <span className="text-sm font-medium text-blue-500">Medium</span>
              <span className="text-xs text-muted-foreground">768px+</span>
            </div>
            
            <div className="hidden lg:flex xl:hidden flex-col items-center space-y-2 p-4 rounded-lg bg-green-500/10 border border-green-500/20">
              <Monitor className="h-8 w-8 text-green-500" />
              <span className="text-sm font-medium text-green-500">Large</span>
              <span className="text-xs text-muted-foreground">1024px+</span>
            </div>
            
            <div className="hidden xl:flex 2xl:hidden flex-col items-center space-y-2 p-4 rounded-lg bg-purple-500/10 border border-purple-500/20">
              <Tv className="h-8 w-8 text-purple-500" />
              <span className="text-sm font-medium text-purple-500">XL</span>
              <span className="text-xs text-muted-foreground">1280px+</span>
            </div>
            
            <div className="hidden 2xl:flex flex-col items-center space-y-2 p-4 rounded-lg bg-indigo-500/10 border border-indigo-500/20">
              <Tv className="h-8 w-8 text-indigo-500" />
              <span className="text-sm font-medium text-indigo-500">2XL</span>
              <span className="text-xs text-muted-foreground">1536px+</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grid System Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Grid3X3 className="h-5 w-5" />
            <span>Responsive Grid System</span>
          </CardTitle>
          <CardDescription>
            Testing grid layouts across different breakpoints
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {Array.from({ length: 12 }).map((_, i) => (
              <div
                key={i}
                className="aspect-square bg-gradient-primary rounded-lg flex items-center justify-center text-white font-bold"
              >
                {i + 1}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Component Responsiveness */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Layout className="h-5 w-5" />
              <span>Button Responsiveness</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button className="flex-1">Full Width on Mobile</Button>
              <Button variant="outline" className="flex-1">Responsive Button</Button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2">
              <Button size="sm">Small</Button>
              <Button>Default</Button>
              <Button size="lg" className="col-span-full sm:col-span-1">Large</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Palette className="h-5 w-5" />
              <span>Typography Scale</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold">
              Responsive Heading
            </h1>
            <p className="text-sm sm:text-base lg:text-lg text-muted-foreground">
              This paragraph text scales with screen size for optimal readability.
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge>Mobile</Badge>
              <Badge variant="secondary" className="hidden sm:inline-flex">Small+</Badge>
              <Badge variant="outline" className="hidden md:inline-flex">Medium+</Badge>
              <Badge className="hidden lg:inline-flex">Large+</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Summary */}
      <Card className="border-green-500/20 bg-green-500/5">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span>Responsive Design Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Mobile-first approach</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Responsive navigation</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Flexible grid system</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Adaptive typography</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Touch-friendly buttons</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Optimized spacing</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
