import { NextRequest, NextResponse } from "next/server"
import { verifyJWT } from "@/lib/auth/jwt"
import { prisma } from "@/lib/database"

interface RouteParams {
  params: {
    accountId: string
  }
}

async function refreshGoogleToken(refreshToken: string) {
  const response = await fetch("https://oauth2.googleapis.com/token", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      client_id: process.env.GOOGLE_CLIENT_ID!,
      client_secret: process.env.GOOGLE_CLIENT_SECRET!,
      refresh_token: refreshToken,
      grant_type: "refresh_token",
    }),
  })

  if (!response.ok) {
    throw new Error("Failed to refresh Google token")
  }

  return response.json()
}

async function refreshTwitterToken(refreshToken: string) {
  // Twitter OAuth 2.0 token refresh
  const response = await fetch("https://api.twitter.com/2/oauth2/token", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": `Basic ${Buffer.from(
        `${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`
      ).toString("base64")}`,
    },
    body: new URLSearchParams({
      refresh_token: refreshToken,
      grant_type: "refresh_token",
    }),
  })

  if (!response.ok) {
    throw new Error("Failed to refresh Twitter token")
  }

  return response.json()
}

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Get the JWT token from cookies
    const token = request.cookies.get("auth-token")?.value

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token
    const payload = await verifyJWT(token)
    if (!payload || !payload.userId) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      )
    }

    const { accountId } = params

    // Find the account and verify ownership
    const account = await prisma.account.findFirst({
      where: {
        id: accountId,
        userId: payload.userId,
      }
    })

    if (!account) {
      return NextResponse.json(
        { error: "Account not found or access denied" },
        { status: 404 }
      )
    }

    if (!account.refresh_token) {
      return NextResponse.json(
        { error: "No refresh token available for this account" },
        { status: 400 }
      )
    }

    let tokenData
    try {
      // Refresh token based on provider
      switch (account.provider) {
        case "google":
          tokenData = await refreshGoogleToken(account.refresh_token)
          break
        case "twitter":
          tokenData = await refreshTwitterToken(account.refresh_token)
          break
        default:
          return NextResponse.json(
            { error: "Unsupported provider for token refresh" },
            { status: 400 }
          )
      }

      // Update the account with new tokens
      const updatedAccount = await prisma.account.update({
        where: { id: accountId },
        data: {
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || account.refresh_token, // Keep old refresh token if new one not provided
          expires_at: tokenData.expires_in 
            ? Math.floor(Date.now() / 1000) + tokenData.expires_in
            : account.expires_at,
          token_type: tokenData.token_type || account.token_type,
          scope: tokenData.scope || account.scope,
          updatedAt: new Date(),
        }
      })

      return NextResponse.json({
        success: true,
        message: "Account tokens refreshed successfully",
        account: {
          id: updatedAccount.id,
          provider: updatedAccount.provider,
          refreshedAt: updatedAccount.updatedAt.toISOString(),
          expiresAt: updatedAccount.expires_at 
            ? new Date(updatedAccount.expires_at * 1000).toISOString()
            : null,
        }
      })

    } catch (refreshError) {
      console.error("Token refresh failed:", refreshError)
      
      // If refresh fails, the account might need to be re-authorized
      return NextResponse.json(
        { 
          error: "Token refresh failed. Please reconnect your account.",
          requiresReauth: true 
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error refreshing account tokens:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
