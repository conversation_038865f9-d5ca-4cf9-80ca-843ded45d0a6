import { NextRequest, NextResponse } from "next/server"
import { requireAuth } from "@/lib/auth/session"
import { prisma } from "@/lib/database"

interface RouteParams {
  params: {
    accountId: string
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { accountId } = params

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      )
    }

    // First, check if the account exists and belongs to the user
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        id: accountId,
        userId: session.userId,
      },
    })

    const googleAccount = await prisma.googleAccount.findFirst({
      where: {
        id: accountId,
        userId: session.userId,
      },
    })

    if (!twitterAccount && !googleAccount) {
      return NextResponse.json(
        { error: 'Account not found or access denied' },
        { status: 404 }
      )
    }

    // Check if this would be the last account (prevent user lockout)
    const totalAccounts = await prisma.user.findUnique({
      where: { id: session.userId },
      select: {
        _count: {
          select: {
            twitterAccounts: true,
            googleAccounts: true,
          },
        },
      },
    })

    const accountCount = (totalAccounts?._count.twitterAccounts || 0) + (totalAccounts?._count.googleAccounts || 0)

    if (accountCount <= 1) {
      return NextResponse.json(
        { error: 'Cannot disconnect the last connected account' },
        { status: 400 }
      )
    }

    // Delete the account
    if (twitterAccount) {
      await prisma.twitterAccount.delete({
        where: { id: accountId },
      })
    } else if (googleAccount) {
      await prisma.googleAccount.delete({
        where: { id: accountId },
      })
    }

    return NextResponse.json({
      message: 'Account disconnected successfully',
    })
  } catch (error) {
    console.error('Account disconnect error:', error)
    return NextResponse.json(
      { error: 'Failed to disconnect account' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { accountId } = params

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      )
    }

    // Check Twitter accounts
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        id: accountId,
        userId: session.userId,
      },
      select: {
        id: true,
        twitterId: true,
        username: true,
        displayName: true,
        profileImageUrl: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (twitterAccount) {
      return NextResponse.json({
        account: {
          id: twitterAccount.id,
          provider: 'twitter',
          providerAccountId: twitterAccount.twitterId,
          username: twitterAccount.username,
          displayName: twitterAccount.displayName,
          profileImageUrl: twitterAccount.profileImageUrl,
          isActive: twitterAccount.isActive,
          createdAt: twitterAccount.createdAt,
          updatedAt: twitterAccount.updatedAt,
        },
      })
    }

    // Check Google accounts
    const googleAccount = await prisma.googleAccount.findFirst({
      where: {
        id: accountId,
        userId: session.userId,
      },
      select: {
        id: true,
        googleId: true,
        email: true,
        name: true,
        profileImageUrl: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (googleAccount) {
      return NextResponse.json({
        account: {
          id: googleAccount.id,
          provider: 'google',
          providerAccountId: googleAccount.googleId,
          username: googleAccount.email,
          displayName: googleAccount.name,
          profileImageUrl: googleAccount.profileImageUrl,
          isActive: true,
          createdAt: googleAccount.createdAt,
          updatedAt: googleAccount.updatedAt,
        },
      })
    }

    return NextResponse.json(
      { error: 'Account not found' },
      { status: 404 }
    )
  } catch (error) {
    console.error('Get account error:', error)
    return NextResponse.json(
      { error: 'Failed to get account' },
      { status: 500 }
    )
  }
}
