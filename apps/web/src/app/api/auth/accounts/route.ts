import { NextRequest, NextResponse } from "next/server"
import { verifyJWT } from "@/lib/auth/jwt"
import { prisma } from "@/lib/database"

export async function GET(request: NextRequest) {
  try {
    // Get the JWT token from cookies
    const token = request.cookies.get("auth-token")?.value

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token
    const payload = await verifyJWT(token)
    if (!payload || !payload.userId) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      )
    }

    // Fetch user's connected accounts
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        accounts: {
          select: {
            id: true,
            provider: true,
            providerAccountId: true,
            type: true,
            createdAt: true,
            updatedAt: true,
            // Don't include sensitive token data
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Transform accounts data for frontend
    const accounts = user.accounts.map(account => ({
      id: account.id,
      provider: account.provider,
      email: account.provider === "google" ? user.email : undefined,
      username: account.provider === "twitter" ? `@${account.providerAccountId}` : undefined,
      displayName: account.provider === "google" ? user.name || user.email : 
                   account.provider === "twitter" ? user.name || `@${account.providerAccountId}` : 
                   "Unknown",
      isActive: true, // You can add logic to determine if account is active
      connectedAt: account.createdAt.toISOString(),
      lastUsed: account.updatedAt.toISOString(),
    }))

    return NextResponse.json({
      success: true,
      accounts,
    })

  } catch (error) {
    console.error("Error fetching connected accounts:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the JWT token from cookies
    const token = request.cookies.get("auth-token")?.value

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token
    const payload = await verifyJWT(token)
    if (!payload || !payload.userId) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { provider, accountData } = body

    if (!provider || !accountData) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Check if account is already connected
    const existingAccount = await prisma.account.findFirst({
      where: {
        userId: payload.userId,
        provider: provider,
        providerAccountId: accountData.id,
      }
    })

    if (existingAccount) {
      return NextResponse.json(
        { error: "Account already connected" },
        { status: 409 }
      )
    }

    // Create new account connection
    const newAccount = await prisma.account.create({
      data: {
        userId: payload.userId,
        type: "oauth",
        provider: provider,
        providerAccountId: accountData.id,
        access_token: accountData.access_token,
        refresh_token: accountData.refresh_token,
        expires_at: accountData.expires_at,
        token_type: accountData.token_type,
        scope: accountData.scope,
      }
    })

    return NextResponse.json({
      success: true,
      account: {
        id: newAccount.id,
        provider: newAccount.provider,
        connectedAt: newAccount.createdAt.toISOString(),
      }
    })

  } catch (error) {
    console.error("Error connecting account:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
