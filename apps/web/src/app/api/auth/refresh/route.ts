import { NextRequest, NextResponse } from 'next/server'
import { refreshSession } from '@/lib/auth/session'

export async function POST(request: NextRequest) {
  try {
    const success = await refreshSession(request)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to refresh session' },
        { status: 401 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Session refresh error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}