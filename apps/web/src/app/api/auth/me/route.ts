import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { updateUserProfileSchema } from '@/lib/validations/user'

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    // Get user with preferences and connected accounts
    const user = await prisma.user.findUnique({
      where: { id: session.userId },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        preferences: true,
        createdAt: true,
        updatedAt: true,
        twitterAccounts: {
          select: {
            id: true,
            twitterId: true,
            username: true,
            displayName: true,
            profileImageUrl: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        googleAccounts: {
          select: {
            id: true,
            googleId: true,
            email: true,
            name: true,
            profileImageUrl: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Format connected accounts
    const connectedAccounts = [
      ...user.twitterAccounts.map(account => ({
        id: account.id,
        provider: 'twitter' as const,
        providerAccountId: account.twitterId,
        username: account.username,
        displayName: account.displayName,
        profileImageUrl: account.profileImageUrl,
        isActive: account.isActive,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      })),
      ...user.googleAccounts.map(account => ({
        id: account.id,
        provider: 'google' as const,
        providerAccountId: account.googleId,
        username: account.email,
        displayName: account.name,
        profileImageUrl: account.profileImageUrl,
        isActive: true, // Google accounts are always active
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      })),
    ]

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        preferences: user.preferences,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        connectedAccounts,
      },
    })
  } catch (error) {
    console.error('Session validation error:', error)
    return NextResponse.json(
      { error: 'Session validation failed' },
      { status: 401 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = updateUserProfileSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.flatten().fieldErrors
        },
        { status: 400 }
      )
    }

    const { name, email, avatar, preferences } = validationResult.data

    // Check if email is being changed and if it's already taken
    if (email && email !== session.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email },
        select: { id: true }
      })

      if (existingUser && existingUser.id !== session.userId) {
        return NextResponse.json(
          { error: 'Email already in use' },
          { status: 409 }
        )
      }
    }

    // Prepare update data
    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (email !== undefined) updateData.email = email
    if (avatar !== undefined) updateData.avatar = avatar
    if (preferences !== undefined) {
      // Merge with existing preferences
      const currentUser = await prisma.user.findUnique({
        where: { id: session.userId },
        select: { preferences: true }
      })

      const currentPrefs = (currentUser?.preferences as any) || {}
      updateData.preferences = {
        ...currentPrefs,
        ...preferences,
        notifications: {
          ...currentPrefs.notifications,
          ...preferences.notifications,
        }
      }
    }

    // Update user in database
    const updatedUser = await prisma.user.update({
      where: { id: session.userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        preferences: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      user: updatedUser,
      message: 'Profile updated successfully'
    })
  } catch (error) {
    console.error('Profile update error:', error)
    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    )
  }
}