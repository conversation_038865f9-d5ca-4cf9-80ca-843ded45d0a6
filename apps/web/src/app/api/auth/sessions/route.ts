import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { getUserSessions, revokeSession, revokeAllUserSessions } from '@/lib/auth/jwt'

// Get user's active sessions
export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()
    const sessions = await getUserSessions(session.userId)
    
    return NextResponse.json({
      sessions: sessions.map(s => ({
        id: s.id,
        jwtId: s.jwtId,
        ipAddress: s.ipAddress,
        userAgent: s.userAgent,
        createdAt: s.createdAt,
        expiresAt: s.expiresAt,
        isCurrent: s.jwtId === session.jwtId
      }))
    })
  } catch (error) {
    console.error('Get sessions error:', error)
    return NextResponse.json(
      { error: 'Failed to get sessions' },
      { status: 500 }
    )
  }
}

// Revoke specific session or all sessions
export async function DELETE(request: NextRequest) {
  try {
    const session = await requireAuth()
    const { searchParams } = new URL(request.url)
    const jwtId = searchParams.get('jwtId')
    const all = searchParams.get('all') === 'true'
    
    if (all) {
      // Revoke all user sessions
      await revokeAllUserSessions(session.userId)
      return NextResponse.json({ message: 'All sessions revoked' })
    } else if (jwtId) {
      // Revoke specific session
      const success = await revokeSession(jwtId)
      if (!success) {
        return NextResponse.json(
          { error: 'Failed to revoke session' },
          { status: 400 }
        )
      }
      return NextResponse.json({ message: 'Session revoked' })
    } else {
      return NextResponse.json(
        { error: 'Missing jwtId or all parameter' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Revoke session error:', error)
    return NextResponse.json(
      { error: 'Failed to revoke session' },
      { status: 500 }
    )
  }
}