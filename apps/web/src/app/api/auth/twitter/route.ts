import { NextRequest, NextResponse } from 'next/server'
import { TwitterOAuthProvider } from '@/lib/auth/providers/twitter'
import { encryptToken } from '@/lib/auth/crypto'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const twitterProvider = new TwitterOAuthProvider()
    const { url, pkceData } = await twitterProvider.generateAuthUrl()

    // Store PKCE data and state in secure cookies
    const cookieStore = await cookies()
    
    const encryptedPKCEData = await encryptToken(pkceData)
    
    cookieStore.set('twitter_pkce', encryptedPKCEData, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 600, // 10 minutes
      path: '/',
    })

    cookieStore.set('oauth_state', pkceData.state, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 600, // 10 minutes
      path: '/',
    })

    return NextResponse.redirect(url)
  } catch (error) {
    console.error('Twitter OAuth initiation error:', error)
    return NextResponse.json(
      { error: 'Failed to initiate Twitter OAuth' },
      { status: 500 }
    )
  }
}