import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    // Get user's connected Twitter accounts
    const twitterAccounts = await prisma.twitterAccount.findMany({
      where: {
        userId: session.userId,
        isActive: true,
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        profileImageUrl: true,
        isActive: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json(twitterAccounts)
  } catch (error) {
    console.error('Get Twitter accounts error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch Twitter accounts' },
      { status: 500 }
    )
  }
}