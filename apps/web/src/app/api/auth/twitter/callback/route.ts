import { NextRequest, NextResponse } from 'next/server'
import { TwitterOAuthProvider } from '@/lib/auth/providers/twitter'
import { createSession } from '@/lib/auth/session'
import { encryptToken, decryptToken } from '@/lib/auth/crypto'
import { prisma } from '@/lib/database'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')

    // Handle OAuth errors
    if (error) {
      console.error('Twitter OAuth error:', error)
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=oauth_error&provider=twitter`
      )
    }

    // Validate required parameters
    if (!code || !state) {
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=missing_parameters`
      )
    }

    const cookieStore = await cookies()

    // Verify CSRF state
    const storedState = cookieStore.get('oauth_state')?.value
    if (!storedState || storedState !== state) {
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=invalid_state`
      )
    }

    // Retrieve and decrypt PKCE data
    const encryptedPKCEData = cookieStore.get('twitter_pkce')?.value
    if (!encryptedPKCEData) {
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=missing_pkce_data`
      )
    }

    const pkceData = await decryptToken(encryptedPKCEData)

    // Verify PKCE state matches
    if (pkceData.state !== state) {
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=pkce_state_mismatch`
      )
    }

    // Clear cookies
    cookieStore.delete('oauth_state')
    cookieStore.delete('twitter_pkce')

    const twitterProvider = new TwitterOAuthProvider()

    // Exchange code for tokens with PKCE verification
    const tokenResponse = await twitterProvider.exchangeCodeForTokens(
      code,
      pkceData.codeVerifier
    )
    
    // Get user information
    const userInfo = await twitterProvider.getUserInfo(tokenResponse.access_token)

    // Encrypt tokens before storage
    const encryptedAccessToken = await encryptToken({
      token: tokenResponse.access_token,
      type: 'access_token',
    })

    const encryptedRefreshToken = tokenResponse.refresh_token
      ? await encryptToken({
          token: tokenResponse.refresh_token,
          type: 'refresh_token',
        })
      : null

    // Create or update user in database (Twitter doesn't always provide email)
    let user
    if (userInfo.email) {
      user = await prisma.user.upsert({
        where: { email: userInfo.email },
        update: {
          name: userInfo.name,
          avatar: userInfo.profile_image_url,
          updatedAt: new Date(),
        },
        create: {
          email: userInfo.email,
          name: userInfo.name,
          avatar: userInfo.profile_image_url,
          preferences: {},
        },
      })
    } else {
      // Check if Twitter account already exists
      const existingTwitterAccount = await prisma.twitterAccount.findUnique({
        where: { twitterId: userInfo.id },
        include: { user: true },
      })

      if (existingTwitterAccount) {
        user = existingTwitterAccount.user
      } else {
        // Create new user with Twitter username as email fallback
        user = await prisma.user.create({
          data: {
            email: `${userInfo.username}@twitter.local`,
            name: userInfo.name,
            avatar: userInfo.profile_image_url,
            preferences: {},
          },
        })
      }
    }

    // Create or update Twitter account connection
    await prisma.twitterAccount.upsert({
      where: { twitterId: userInfo.id },
      update: {
        username: userInfo.username,
        displayName: userInfo.name,
        profileImageUrl: userInfo.profile_image_url,
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken,
        updatedAt: new Date(),
      },
      create: {
        twitterId: userInfo.id,
        username: userInfo.username,
        displayName: userInfo.name,
        profileImageUrl: userInfo.profile_image_url,
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken,
        userId: user.id,
      },
    })

    // Create session
    await createSession({
      userId: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
    }, request)

    // Redirect to dashboard
    return NextResponse.redirect(`${process.env.FRONTEND_URL}/dashboard`)
  } catch (error) {
    console.error('Twitter OAuth callback error:', error)
    return NextResponse.redirect(
      `${process.env.FRONTEND_URL}/login?error=callback_error&provider=twitter`
    )
  }
}