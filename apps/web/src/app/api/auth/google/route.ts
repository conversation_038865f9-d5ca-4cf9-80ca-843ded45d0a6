import { NextRequest, NextResponse } from 'next/server'
import { GoogleOAuthProvider } from '@/lib/auth/providers/google'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const googleProvider = new GoogleOAuthProvider()
    const { url, state } = googleProvider.generateAuthUrl()

    // Store state in secure cookie for CSRF protection
    const cookieStore = await cookies()
    cookieStore.set('oauth_state', state, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 600, // 10 minutes
      path: '/',
    })

    return NextResponse.redirect(url)
  } catch (error) {
    console.error('Google OAuth initiation error:', error)
    return NextResponse.json(
      { error: 'Failed to initiate Google OAuth' },
      { status: 500 }
    )
  }
}