import { NextRequest, NextResponse } from 'next/server'
import { GoogleOAuthProvider } from '@/lib/auth/providers/google'
import { createSession } from '@/lib/auth/session'
import { encryptToken } from '@/lib/auth/crypto'
import { prisma } from '@/lib/database'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')

    // Handle OAuth errors
    if (error) {
      console.error('Google OAuth error:', error)
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=oauth_error&provider=google`
      )
    }

    // Validate required parameters
    if (!code || !state) {
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=missing_parameters`
      )
    }

    // Verify CSRF state
    const cookieStore = await cookies()
    const storedState = cookieStore.get('oauth_state')?.value
    if (!storedState || storedState !== state) {
      return NextResponse.redirect(
        `${process.env.FRONTEND_URL}/login?error=invalid_state`
      )
    }

    // Clear state cookie
    cookieStore.delete('oauth_state')

    const googleProvider = new GoogleOAuthProvider()

    // Exchange code for tokens
    const tokenResponse = await googleProvider.exchangeCodeForTokens(code)
    
    // Get user information
    const userInfo = await googleProvider.getUserInfo(tokenResponse.access_token)

    // Encrypt tokens before storage
    const encryptedAccessToken = await encryptToken({
      token: tokenResponse.access_token,
      type: 'access_token',
    })

    const encryptedRefreshToken = tokenResponse.refresh_token
      ? await encryptToken({
          token: tokenResponse.refresh_token,
          type: 'refresh_token',
        })
      : null

    // Create or update user in database
    const user = await prisma.user.upsert({
      where: { email: userInfo.email },
      update: {
        name: userInfo.name,
        avatar: userInfo.picture,
        updatedAt: new Date(),
      },
      create: {
        email: userInfo.email,
        name: userInfo.name,
        avatar: userInfo.picture,
        preferences: {},
      },
    })

    // Create or update Google account connection
    await prisma.googleAccount.upsert({
      where: { googleId: userInfo.id },
      update: {
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken,
        updatedAt: new Date(),
      },
      create: {
        googleId: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        profileImageUrl: userInfo.picture,
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken,
        userId: user.id,
      },
    })

    // Create session
    await createSession({
      userId: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
    }, request)

    // Redirect to dashboard
    return NextResponse.redirect(`${process.env.FRONTEND_URL}/dashboard`)
  } catch (error) {
    console.error('Google OAuth callback error:', error)
    return NextResponse.redirect(
      `${process.env.FRONTEND_URL}/login?error=callback_error&provider=google`
    )
  }
}