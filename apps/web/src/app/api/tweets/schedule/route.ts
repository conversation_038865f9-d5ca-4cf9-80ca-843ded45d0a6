import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { scheduleSchema } from '@/lib/validations/schedule'
import { Prisma } from '@prisma/client'

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = scheduleSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid schedule data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const { draftId, scheduledFor, twitterAccountId } = validationResult.data

    // Get the draft to schedule
    const draft = await prisma.scheduledTweet.findFirst({
      where: {
        id: draftId,
        userId: session.userId,
        status: 'draft',
      },
      include: {
        agent: true,
      },
    })

    if (!draft) {
      return NextResponse.json(
        { error: 'Draft not found or already scheduled' },
        { status: 404 }
      )
    }

    // Verify Twitter account belongs to user
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        id: twitterAccountId,
        userId: session.userId,
        isActive: true,
      },
    })

    if (!twitterAccount) {
      return NextResponse.json(
        { error: 'Twitter account not found or inactive' },
        { status: 404 }
      )
    }

    // Check if scheduling time is in the future
    const scheduledDate = new Date(scheduledFor)
    if (scheduledDate <= new Date()) {
      return NextResponse.json(
        { error: 'Scheduled time must be in the future' },
        { status: 400 }
      )
    }

    // Update the draft to scheduled status
    const scheduledTweet = await prisma.scheduledTweet.update({
      where: { id: draft.id },
      data: {
        status: 'scheduled',
        scheduledFor: scheduledDate,
        twitterAccountId,
      },
      include: {
        agent: {
          select: { id: true, name: true },
        },
        twitterAccount: {
          select: { id: true, username: true },
        },
      },
    })

    return NextResponse.json(scheduledTweet, { status: 200 })
  } catch (error) {
    console.error('Schedule tweet error:', error)
    
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      return NextResponse.json(
        { error: 'Database error occurred' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to schedule tweet' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') || 'scheduled'
    const skip = (page - 1) * limit

    // Build where clause
    const where: Prisma.ScheduledTweetWhereInput = {
      userId: session.userId,
      status: status as any,
    }

    // Get scheduled tweets
    const [tweets, total] = await Promise.all([
      prisma.scheduledTweet.findMany({
        where,
        orderBy: { scheduledFor: 'asc' },
        skip,
        take: limit,
        include: {
          agent: {
            select: { id: true, name: true },
          },
          twitterAccount: {
            select: { id: true, username: true },
          },
        },
      }),
      prisma.scheduledTweet.count({ where }),
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      tweets,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    })
  } catch (error) {
    console.error('Get scheduled tweets error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch scheduled tweets' },
      { status: 500 }
    )
  }
}