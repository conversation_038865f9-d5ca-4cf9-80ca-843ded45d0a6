import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'

interface RouteParams {
  params: {
    id: string
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id } = params

    // Check if scheduled tweet exists and belongs to user
    const scheduledTweet = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: session.userId,
        status: 'scheduled',
      },
    })

    if (!scheduledTweet) {
      return NextResponse.json(
        { error: 'Scheduled tweet not found' },
        { status: 404 }
      )
    }

    // Update status back to draft
    await prisma.scheduledTweet.update({
      where: { id },
      data: {
        status: 'draft',
        scheduledFor: null,
        twitterAccountId: null,
      },
    })

    return NextResponse.json({ message: 'Tweet unscheduled successfully' })
  } catch (error) {
    console.error('Unschedule tweet error:', error)
    return NextResponse.json(
      { error: 'Failed to unschedule tweet' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id } = params
    const body = await request.json()

    const { scheduledFor, twitterAccountId } = body

    // Check if scheduled tweet exists and belongs to user
    const scheduledTweet = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: session.userId,
        status: 'scheduled',
      },
    })

    if (!scheduledTweet) {
      return NextResponse.json(
        { error: 'Scheduled tweet not found' },
        { status: 404 }
      )
    }

    // Validate new scheduled time
    if (scheduledFor) {
      const scheduledDate = new Date(scheduledFor)
      if (scheduledDate <= new Date()) {
        return NextResponse.json(
          { error: 'Scheduled time must be in the future' },
          { status: 400 }
        )
      }
    }

    // Update scheduled tweet
    const updatedTweet = await prisma.scheduledTweet.update({
      where: { id },
      data: {
        ...(scheduledFor && { scheduledFor: new Date(scheduledFor) }),
        ...(twitterAccountId && { twitterAccountId }),
      },
      include: {
        agent: {
          select: { id: true, name: true },
        },
        twitterAccount: {
          select: { id: true, username: true },
        },
      },
    })

    return NextResponse.json(updatedTweet)
  } catch (error) {
    console.error('Update scheduled tweet error:', error)
    return NextResponse.json(
      { error: 'Failed to update scheduled tweet' },
      { status: 500 }
    )
  }
}