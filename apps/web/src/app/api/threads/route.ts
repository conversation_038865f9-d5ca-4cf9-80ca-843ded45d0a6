import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/session';
import { prisma } from '@/lib/database';
import { createThreadSchema } from '@/lib/validations/draft';
import { randomUUID } from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth();
    const body = await request.json();

    // Validate request body
    const validationResult = createThreadSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid thread data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const threadData = validationResult.data;

    // Verify agent belongs to user
    const agent = await prisma.agent.findFirst({
      where: {
        id: threadData.agentId,
        userId: session.userId,
      },
    });

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    // Generate thread ID
    const threadId = randomUUID();

    // Create all tweets in the thread
    const tweets = await Promise.all(
      threadData.tweets.map((tweet, index) =>
        prisma.scheduledTweet.create({
          data: {
            content: tweet.content,
            mediaUrls: tweet.mediaUrls || [],
            status: 'draft',
            threadId,
            threadOrder: index,
            isThreadStart: index === 0,
            agentId: threadData.agentId,
            userId: session.userId,
          },
          include: {
            agent: {
              select: { id: true, name: true },
            },
          },
        })
      )
    );

    return NextResponse.json({
      threadId,
      tweets,
      totalTweets: tweets.length,
    }, { status: 201 });
  } catch (error) {
    console.error('Create thread error:', error);
    return NextResponse.json(
      { error: 'Failed to create thread' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth();
    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');

    if (!threadId) {
      return NextResponse.json(
        { error: 'Thread ID is required' },
        { status: 400 }
      );
    }

    // Get all tweets in the thread
    const tweets = await prisma.scheduledTweet.findMany({
      where: {
        threadId,
        userId: session.userId,
      },
      orderBy: {
        threadOrder: 'asc',
      },
      include: {
        agent: {
          select: { id: true, name: true },
        },
      },
    });

    if (tweets.length === 0) {
      return NextResponse.json(
        { error: 'Thread not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      threadId,
      tweets,
      totalTweets: tweets.length,
      createdAt: tweets[0].createdAt,
      updatedAt: Math.max(...tweets.map(t => t.updatedAt.getTime())),
    });
  } catch (error) {
    console.error('Get thread error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch thread' },
      { status: 500 }
    );
  }
}