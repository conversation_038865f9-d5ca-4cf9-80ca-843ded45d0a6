import { NextRequest, NextResponse } from 'next/server'
import { rateLimiter, RATE_LIMIT_CONFIGS, generateRateLimitKey, getClientIP } from '@/lib/security/rate-limiter'
import { validateSessionFromRequest } from '@/lib/auth/session'

/**
 * Get current rate limit status for the user
 */
export async function GET(request: NextRequest) {
  try {
    const ip = getClientIP(request)
    const session = await validateSessionFromRequest(request)
    const userId = session?.userId
    
    // Get rate limit status for different endpoint types
    const statuses = await Promise.all([
      {
        type: 'auth',
        ...await rateLimiter.getRateLimitStatus(
          generateRateLimitKey(ip, userId, 'auth'),
          RATE_LIMIT_CONFIGS.auth
        )
      },
      {
        type: 'api',
        ...await rateLimiter.getRateLimitStatus(
          generateRateLimitKey(ip, userId, 'api'),
          RATE_LIMIT_CONFIGS.api
        )
      },
      {
        type: 'content',
        ...await rateLimiter.getRateLimitStatus(
          generateRateLimitKey(ip, userId, 'content'),
          RATE_LIMIT_CONFIGS.content
        )
      },
      {
        type: 'upload',
        ...await rateLimiter.getRateLimitStatus(
          generateRateLimitKey(ip, userId, 'upload'),
          RATE_LIMIT_CONFIGS.upload
        )
      }
    ])

    return NextResponse.json({
      ip,
      userId,
      limits: statuses,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('Rate limit status error:', error)
    return NextResponse.json(
      { error: 'Failed to get rate limit status' },
      { status: 500 }
    )
  }
}