import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { createTwitterClient } from '@/lib/twitter/client'

/**
 * Test Twitter API integration
 */
export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()
    const { tweetText, mediaUrls } = body

    if (!tweetText) {
      return NextResponse.json(
        { error: 'Tweet text is required' },
        { status: 400 }
      )
    }

    // Get user's active Twitter account
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        userId: session.userId,
        isActive: true,
      },
    })

    if (!twitterAccount) {
      return NextResponse.json(
        { error: 'No active Twitter account found. Please connect your Twitter account.' },
        { status: 404 }
      )
    }

    // Create Twitter client
    const twitterClient = await createTwitterClient({
      accessToken: twitterAccount.accessToken,
      refreshToken: twitterAccount.refreshToken,
    })

    // Verify credentials first
    const userInfo = await twitterClient.verifyCredentials()
    if (!userInfo) {
      return NextResponse.json(
        { error: 'Twitter credentials are invalid. Please reconnect your account.' },
        { status: 401 }
      )
    }

    let mediaIds: string[] = []

    // Upload media if provided
    if (mediaUrls && mediaUrls.length > 0) {
      try {
        const uploadPromises = mediaUrls.map((url: string) => 
          twitterClient.uploadMediaFromUrl(url)
        )
        const uploadResults = await Promise.all(uploadPromises)
        mediaIds = uploadResults.map(result => result.mediaId)
      } catch (mediaError) {
        console.error('Media upload error:', mediaError)
        return NextResponse.json(
          { error: 'Failed to upload media to Twitter' },
          { status: 500 }
        )
      }
    }

    // Post tweet
    const tweetResult = await twitterClient.postTweet({
      text: tweetText,
      mediaIds: mediaIds.length > 0 ? mediaIds : undefined,
    })

    return NextResponse.json({
      success: true,
      data: {
        tweetId: tweetResult.id,
        text: tweetResult.text,
        createdAt: tweetResult.createdAt,
        mediaCount: mediaIds.length,
        twitterUrl: `https://twitter.com/${userInfo.username}/status/${tweetResult.id}`,
      },
    })
  } catch (error) {
    console.error('Twitter test API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to post test tweet',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * Get Twitter account status and recent tweets
 */
export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    // Get user's Twitter accounts
    const twitterAccounts = await prisma.twitterAccount.findMany({
      where: {
        userId: session.userId,
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (twitterAccounts.length === 0) {
      return NextResponse.json({
        connected: false,
        accounts: [],
        message: 'No Twitter accounts connected',
      })
    }

    const activeAccount = twitterAccounts.find(acc => acc.isActive)
    
    if (!activeAccount) {
      return NextResponse.json({
        connected: true,
        accounts: twitterAccounts,
        activeAccount: null,
        message: 'Twitter accounts connected but none are active',
      })
    }

    // Test connection with active account
    try {
      const fullAccount = await prisma.twitterAccount.findUnique({
        where: { id: activeAccount.id },
      })

      if (fullAccount) {
        const twitterClient = await createTwitterClient({
          accessToken: fullAccount.accessToken,
          refreshToken: fullAccount.refreshToken,
        })

        const userInfo = await twitterClient.verifyCredentials()
        
        if (userInfo) {
          // Get recent tweets
          const recentTweets = await twitterClient.getUserTweets(userInfo.id, 5)
          
          return NextResponse.json({
            connected: true,
            accounts: twitterAccounts,
            activeAccount: {
              ...activeAccount,
              verified: true,
              twitterId: userInfo.id,
            },
            recentTweets,
            message: 'Twitter API connection verified',
          })
        }
      }
    } catch (verifyError) {
      console.error('Twitter verification error:', verifyError)
    }

    return NextResponse.json({
      connected: true,
      accounts: twitterAccounts,
      activeAccount: {
        ...activeAccount,
        verified: false,
      },
      message: 'Twitter account connected but credentials may be invalid',
    })
  } catch (error) {
    console.error('Twitter status API error:', error)
    return NextResponse.json(
      { error: 'Failed to get Twitter status' },
      { status: 500 }
    )
  }
}