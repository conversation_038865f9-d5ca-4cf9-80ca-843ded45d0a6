import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { createTwitterClient } from '@/lib/twitter/client'

/**
 * Upload media to Twitter
 */
export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const formData = await request.formData()
    
    const file = formData.get('file') as File
    const mediaUrl = formData.get('mediaUrl') as string

    if (!file && !mediaUrl) {
      return NextResponse.json(
        { error: 'Either file or mediaUrl is required' },
        { status: 400 }
      )
    }

    // Get user's active Twitter account
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        userId: session.userId,
        isActive: true,
      },
    })

    if (!twitterAccount) {
      return NextResponse.json(
        { error: 'No active Twitter account found. Please connect your Twitter account.' },
        { status: 404 }
      )
    }

    // Create Twitter client
    const twitterClient = await createTwitterClient({
      accessToken: twitterAccount.accessToken,
      refreshToken: twitterAccount.refreshToken,
    })

    let uploadResult

    if (file) {
      // Upload from file
      const buffer = Buffer.from(await file.arrayBuffer())
      const mimeType = file.type || 'image/jpeg'
      
      uploadResult = await twitterClient.uploadMedia(buffer, mimeType)
    } else if (mediaUrl) {
      // Upload from URL
      uploadResult = await twitterClient.uploadMediaFromUrl(mediaUrl)
    }

    if (!uploadResult) {
      return NextResponse.json(
        { error: 'Failed to upload media' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        mediaId: uploadResult.mediaId,
        mediaKey: uploadResult.mediaKey,
        size: uploadResult.size,
        expiresAfterSecs: uploadResult.expiresAfterSecs,
      },
    })
  } catch (error) {
    console.error('Twitter media upload API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to upload media to Twitter',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * Get media upload limits and supported formats
 */
export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    return NextResponse.json({
      success: true,
      data: {
        limits: {
          image: {
            maxSize: 5 * 1024 * 1024, // 5MB
            maxCount: 4,
            supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
          },
          video: {
            maxSize: 512 * 1024 * 1024, // 512MB
            maxCount: 1,
            maxDuration: 140, // seconds
            supportedFormats: ['video/mp4', 'video/mov', 'video/avi'],
          },
        },
        guidelines: [
          'Images: JPEG, PNG, GIF, WebP up to 5MB each, max 4 per tweet',
          'Videos: MP4, MOV, AVI up to 512MB, max 140 seconds, 1 per tweet',
          'GIFs: Up to 15MB, max 512x512 pixels',
          'Media expires after 24 hours if not used in a tweet',
        ],
      },
    })
  } catch (error) {
    console.error('Twitter media info API error:', error)
    return NextResponse.json(
      { error: 'Failed to get media information' },
      { status: 500 }
    )
  }
}