import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { createTwitterClient } from '@/lib/twitter/client'
import { z } from 'zod'

const threadSchema = z.object({
  tweets: z.array(z.object({
    text: z.string().min(1).max(280),
    mediaUrls: z.array(z.string().url()).optional(),
  })).min(2).max(25),
})

/**
 * Post a Twitter thread
 */
export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = threadSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid thread data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const { tweets } = validationResult.data

    // Get user's active Twitter account
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        userId: session.userId,
        isActive: true,
      },
    })

    if (!twitterAccount) {
      return NextResponse.json(
        { error: 'No active Twitter account found. Please connect your Twitter account.' },
        { status: 404 }
      )
    }

    // Create Twitter client
    const twitterClient = await createTwitterClient({
      accessToken: twitterAccount.accessToken,
      refreshToken: twitterAccount.refreshToken,
    })

    // Verify credentials
    const userInfo = await twitterClient.verifyCredentials()
    if (!userInfo) {
      return NextResponse.json(
        { error: 'Twitter credentials are invalid. Please reconnect your account.' },
        { status: 401 }
      )
    }

    // Prepare thread data with media uploads
    const threadData = []
    
    for (let i = 0; i < tweets.length; i++) {
      const tweet = tweets[i]
      let mediaIds: string[] = []

      // Upload media if provided
      if (tweet.mediaUrls && tweet.mediaUrls.length > 0) {
        try {
          const uploadPromises = tweet.mediaUrls.map(url => 
            twitterClient.uploadMediaFromUrl(url)
          )
          const uploadResults = await Promise.all(uploadPromises)
          mediaIds = uploadResults.map(result => result.mediaId)
        } catch (mediaError) {
          console.error(`Media upload error for tweet ${i + 1}:`, mediaError)
          return NextResponse.json(
            { error: `Failed to upload media for tweet ${i + 1}` },
            { status: 500 }
          )
        }
      }

      threadData.push({
        text: tweet.text,
        mediaIds: mediaIds.length > 0 ? mediaIds : undefined,
      })
    }

    // Post thread
    const threadResults = await twitterClient.postThread(threadData)

    // Generate Twitter URLs for each tweet
    const tweetUrls = threadResults.map(result => 
      `https://twitter.com/${userInfo.username}/status/${result.id}`
    )

    return NextResponse.json({
      success: true,
      data: {
        threadId: threadResults[0].id, // Use first tweet ID as thread ID
        tweets: threadResults.map((result, index) => ({
          tweetId: result.id,
          text: result.text,
          createdAt: result.createdAt,
          order: index + 1,
          url: tweetUrls[index],
        })),
        threadUrl: tweetUrls[0], // First tweet URL
        totalTweets: threadResults.length,
      },
    })
  } catch (error) {
    console.error('Twitter thread API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to post Twitter thread',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}