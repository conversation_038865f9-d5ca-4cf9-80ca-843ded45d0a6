import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/session';
import { prisma } from '@/lib/database';
import { createDraftSchema, draftQuerySchema } from '@/lib/validations/draft';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth();
    const { searchParams } = new URL(request.url);
    
    // Validate query parameters
    const queryResult = draftQuerySchema.safeParse({
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      search: searchParams.get('search'),
      agentId: searchParams.get('agentId'),
      threadId: searchParams.get('threadId'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder'),
    });

    if (!queryResult.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: queryResult.error.errors },
        { status: 400 }
      );
    }

    const { page, limit, search, agentId, threadId, sortBy, sortOrder } = queryResult.data;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.ScheduledTweetWhereInput = {
      userId: session.userId,
      status: 'draft',
      ...(search && {
        content: { contains: search, mode: 'insensitive' },
      }),
      ...(agentId && { agentId }),
      ...(threadId && { threadId }),
    };

    // Build order by clause
    const orderBy: Prisma.ScheduledTweetOrderByWithRelationInput = {
      [sortBy]: sortOrder,
    };

    // Execute queries
    const [drafts, total] = await Promise.all([
      prisma.scheduledTweet.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          agent: {
            select: { id: true, name: true },
          },
        },
      }),
      prisma.scheduledTweet.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      drafts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Get drafts error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch drafts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth();
    const body = await request.json();

    // Validate request body
    const validationResult = createDraftSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid draft data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const draftData = validationResult.data;

    // Verify agent belongs to user
    const agent = await prisma.agent.findFirst({
      where: {
        id: draftData.agentId,
        userId: session.userId,
      },
    });

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      );
    }

    // If part of a thread, validate thread exists and belongs to user
    if (draftData.threadId) {
      const threadExists = await prisma.scheduledTweet.findFirst({
        where: {
          threadId: draftData.threadId,
          userId: session.userId,
        },
      });

      if (!threadExists) {
        return NextResponse.json(
          { error: 'Thread not found' },
          { status: 404 }
        );
      }
    }

    // Create draft
    const draft = await prisma.scheduledTweet.create({
      data: {
        content: draftData.content,
        mediaUrls: draftData.mediaUrls || [],
        status: 'draft',
        threadId: draftData.threadId,
        threadOrder: draftData.threadOrder,
        isThreadStart: draftData.isThreadStart,
        agentId: draftData.agentId,
        userId: session.userId,
      },
      include: {
        agent: {
          select: { id: true, name: true },
        },
      },
    });

    return NextResponse.json(draft, { status: 201 });
  } catch (error) {
    console.error('Create draft error:', error);
    return NextResponse.json(
      { error: 'Failed to create draft' },
      { status: 500 }
    );
  }
}