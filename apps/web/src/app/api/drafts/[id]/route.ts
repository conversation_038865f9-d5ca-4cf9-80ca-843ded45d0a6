import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/session';
import { prisma } from '@/lib/database';
import { updateDraftSchema } from '@/lib/validations/draft';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth();
    const { id } = params;

    const draft = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: session.userId,
        status: 'draft',
      },
      include: {
        agent: {
          select: { id: true, name: true },
        },
      },
    });

    if (!draft) {
      return NextResponse.json(
        { error: 'Draft not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(draft);
  } catch (error) {
    console.error('Get draft error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch draft' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth();
    const { id } = params;
    const body = await request.json();

    // Validate request body
    const validationResult = updateDraftSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid draft data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // Check if draft exists and belongs to user
    const existingDraft = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: session.userId,
        status: 'draft',
      },
    });

    if (!existingDraft) {
      return NextResponse.json(
        { error: 'Draft not found' },
        { status: 404 }
      );
    }

    // Update draft
    const draft = await prisma.scheduledTweet.update({
      where: { id },
      data: updateData,
      include: {
        agent: {
          select: { id: true, name: true },
        },
      },
    });

    return NextResponse.json(draft);
  } catch (error) {
    console.error('Update draft error:', error);
    return NextResponse.json(
      { error: 'Failed to update draft' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth();
    const { id } = params;

    // Check if draft exists and belongs to user
    const existingDraft = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: session.userId,
        status: 'draft',
      },
    });

    if (!existingDraft) {
      return NextResponse.json(
        { error: 'Draft not found' },
        { status: 404 }
      );
    }

    // Delete draft
    await prisma.scheduledTweet.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Draft deleted successfully' });
  } catch (error) {
    console.error('Delete draft error:', error);
    return NextResponse.json(
      { error: 'Failed to delete draft' },
      { status: 500 }
    );
  }
}