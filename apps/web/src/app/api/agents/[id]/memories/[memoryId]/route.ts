import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { memoryService } from '@/lib/ai/memory-service'
import { z } from 'zod'

interface RouteParams {
  params: {
    id: string
    memoryId: string
  }
}

const updateMemorySchema = z.object({
  content: z.string().min(1, 'Content is required').max(2000, 'Content too long'),
  context: z.string().optional(),
})

// Get a specific memory
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id: agentId, memoryId } = params

    // Verify agent belongs to user
    const { prisma } = await import('@/lib/database')
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Get memory
    const memory = await prisma.agentMemory.findFirst({
      where: {
        id: memoryId,
        agentId,
      },
    })

    if (!memory) {
      return NextResponse.json(
        { error: 'Memory not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: memory.id,
      content: memory.content,
      context: memory.context,
      agentId: memory.agentId,
      createdAt: memory.createdAt,
      updatedAt: memory.updatedAt,
    })
  } catch (error) {
    console.error('Get memory error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch memory' },
      { status: 500 }
    )
  }
}

// Update a memory
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id: agentId, memoryId } = params
    const body = await request.json()

    // Validate request body
    const validationResult = updateMemorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid memory data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    // Verify agent belongs to user
    const { prisma } = await import('@/lib/database')
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Verify memory exists
    const existingMemory = await prisma.agentMemory.findFirst({
      where: {
        id: memoryId,
        agentId,
      },
    })

    if (!existingMemory) {
      return NextResponse.json(
        { error: 'Memory not found' },
        { status: 404 }
      )
    }

    // Update memory
    const memory = await memoryService.updateMemory(
      memoryId,
      validationResult.data.content,
      validationResult.data.context
    )

    return NextResponse.json(memory)
  } catch (error) {
    console.error('Update memory error:', error)
    return NextResponse.json(
      { error: 'Failed to update memory' },
      { status: 500 }
    )
  }
}

// Delete a memory
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id: agentId, memoryId } = params

    // Verify agent belongs to user
    const { prisma } = await import('@/lib/database')
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Verify memory exists
    const existingMemory = await prisma.agentMemory.findFirst({
      where: {
        id: memoryId,
        agentId,
      },
    })

    if (!existingMemory) {
      return NextResponse.json(
        { error: 'Memory not found' },
        { status: 404 }
      )
    }

    // Delete memory
    await memoryService.deleteMemory(memoryId)

    return NextResponse.json({ message: 'Memory deleted successfully' })
  } catch (error) {
    console.error('Delete memory error:', error)
    return NextResponse.json(
      { error: 'Failed to delete memory' },
      { status: 500 }
    )
  }
}