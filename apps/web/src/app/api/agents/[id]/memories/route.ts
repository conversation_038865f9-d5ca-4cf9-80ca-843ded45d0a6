import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { memoryService } from '@/lib/ai/memory-service'
import { z } from 'zod'

interface RouteParams {
  params: {
    id: string
  }
}

const createMemorySchema = z.object({
  content: z.string().min(1, 'Content is required').max(2000, 'Content too long'),
  context: z.string().optional(),
})

const searchMemorySchema = z.object({
  query: z.string().min(1, 'Query is required'),
  limit: z.coerce.number().min(1).max(20).default(5),
  threshold: z.coerce.number().min(0).max(1).default(0.7),
})

// Get memories for an agent
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id: agentId } = params
    const { searchParams } = new URL(request.url)
    
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const query = searchParams.get('query')

    // Verify agent belongs to user
    const { prisma } = await import('@/lib/database')
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    if (query) {
      // Search memories by similarity
      const searchResult = searchMemorySchema.safeParse({
        query,
        limit: searchParams.get('limit'),
        threshold: searchParams.get('threshold'),
      })

      if (!searchResult.success) {
        return NextResponse.json(
          { error: 'Invalid search parameters', details: searchResult.error.errors },
          { status: 400 }
        )
      }

      const memories = await memoryService.searchMemories(
        searchResult.data.query,
        agentId,
        {
          limit: searchResult.data.limit,
          threshold: searchResult.data.threshold,
        }
      )

      return NextResponse.json({
        memories: memories.map(result => ({
          ...result.memory,
          similarity: result.similarity,
        })),
        query: searchResult.data.query,
        total: memories.length,
      })
    } else {
      // Get all memories
      const memories = await memoryService.getAgentMemories(agentId, limit, offset)
      const stats = await memoryService.getMemoryStats(agentId)

      return NextResponse.json({
        memories,
        stats,
        pagination: {
          limit,
          offset,
          total: stats.totalMemories,
        },
      })
    }
  } catch (error) {
    console.error('Get agent memories error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch memories' },
      { status: 500 }
    )
  }
}

// Create a new memory for an agent
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await requireAuth()
    const { id: agentId } = params
    const body = await request.json()

    // Validate request body
    const validationResult = createMemorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid memory data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    // Verify agent belongs to user
    const { prisma } = await import('@/lib/database')
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Create memory
    const memory = await memoryService.createMemory({
      content: validationResult.data.content,
      context: validationResult.data.context,
      agentId,
    })

    return NextResponse.json(memory, { status: 201 })
  } catch (error) {
    console.error('Create memory error:', error)
    return NextResponse.json(
      { error: 'Failed to create memory' },
      { status: 500 }
    )
  }
}