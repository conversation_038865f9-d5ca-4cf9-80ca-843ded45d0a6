import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    // Get agent statistics
    const [totalAgents, activeAgents, agentMetrics] = await Promise.all([
      prisma.agent.count({
        where: { userId: session.userId },
      }),
      prisma.agent.count({
        where: { userId: session.userId, isActive: true },
      }),
      prisma.agent.aggregate({
        where: { userId: session.userId },
        _sum: {
          tweetsGenerated: true,
        },
        _avg: {
          engagementRate: true,
        },
      }),
    ])

    const stats = {
      totalAgents,
      activeAgents,
      totalTweets: agentMetrics._sum.tweetsGenerated || 0,
      avgEngagementRate: agentMetrics._avg.engagementRate || 0,
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Get agent stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch agent statistics' },
      { status: 500 }
    )
  }
}