import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { createAgentSchema, agentQuerySchema, personaDataSchema } from '@/lib/validations/agent'
import { Prisma } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()
    const { searchParams } = new URL(request.url)
    
    // Validate query parameters
    const queryResult = agentQuerySchema.safeParse({
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      search: searchParams.get('search'),
      status: searchParams.get('status'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder'),
    })

    if (!queryResult.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: queryResult.error.errors },
        { status: 400 }
      )
    }

    const { page, limit, search, status, sortBy, sortOrder } = queryResult.data
    const skip = (page - 1) * limit

    // Build where clause
    const where: Prisma.AgentWhereInput = {
      userId: session.userId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(status !== 'all' && {
        isActive: status === 'active',
      }),
    }

    // Build order by clause
    const orderBy: Prisma.AgentOrderByWithRelationInput = {
      [sortBy]: sortOrder,
    }

    // Execute queries
    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        orderBy,
        skip,
        take: limit,
      }),
      prisma.agent.count({ where }),
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      agents,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    })
  } catch (error) {
    console.error('Get agents error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch agents' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = createAgentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid agent data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const agentData = validationResult.data

    // If a personaDefinition was uploaded, validate and use it for personaData
    if (agentData.personaDefinition) {
      const personaValidation = personaDataSchema.safeParse(
        agentData.personaDefinition
      )
      if (!personaValidation.success) {
        return NextResponse.json(
          {
            error: 'Invalid persona definition file content',
            details: personaValidation.error.errors,
          },
          { status: 400 }
        )
      }
      agentData.personaData = personaValidation.data
    }

    // Create agent
    const agent = await prisma.agent.create({
      data: {
        ...agentData,
        userId: session.userId,
      },
    })

    return NextResponse.json(agent, { status: 201 })
  } catch (error) {
    console.error('Create agent error:', error)
    
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return NextResponse.json(
          { error: 'Agent name must be unique' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to create agent' },
      { status: 500 }
    )
  }
}