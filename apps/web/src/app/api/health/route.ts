import { NextRequest, NextResponse } from 'next/server'
import { checkDatabaseHealth } from '@/lib/database'

// Helper function to get feature availability
function getFeatureAvailability() {
  return {
    database: !!process.env.DATABASE_URL,
    redis: !!process.env.REDIS_URL,
    googleOAuth: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
    twitterOAuth: !!(process.env.TWITTER_CLIENT_ID && process.env.TWITTER_CLIENT_SECRET),
    uploadThing: !!(process.env.UPLOADTHING_SECRET && process.env.UPLOADTHING_APP_ID && process.env.UPLOADTHING_TOKEN),
    openai: !!process.env.OPENAI_API_KEY,
    gemini: !!process.env.GEMINI_API_KEY,
    mistral: !!process.env.MISTRAL_API_KEY,
    huggingface: !!process.env.HUGGINGFACE_API_KEY,
    groq: !!process.env.GROQ_API_KEY,
    openrouter: !!process.env.OPENROUTER_API_KEY,
    scheduler: process.env.SCHEDULER_ENABLED !== 'false',
    nextAuth: !!(process.env.NEXTAUTH_SECRET && process.env.NEXTAUTH_URL),
  }
}

// Helper function to get AI provider count
function getAIProviderCount() {
  const providers = [
    process.env.OPENAI_API_KEY,
    process.env.GEMINI_API_KEY,
    process.env.MISTRAL_API_KEY,
    process.env.HUGGINGFACE_API_KEY,
    process.env.GROQ_API_KEY,
    process.env.OPENROUTER_API_KEY,
  ]
  return providers.filter(key => key && key.length > 0).length
}

/**
 * Health check endpoint for the Next.js application
 * Provides comprehensive status of environment configuration and services
 */
export async function GET(request: NextRequest) {
  try {
    const dbHealthy = await checkDatabaseHealth()
    const features = getFeatureAvailability()
    const aiProviderCount = getAIProviderCount()
    
    // Determine overall health status
    const hasRequiredFeatures = features.database && features.googleOAuth && features.twitterOAuth && features.uploadThing && features.nextAuth && aiProviderCount > 0
    const overallStatus = dbHealthy && hasRequiredFeatures ? 'ok' : 'degraded'
    
    // Get available AI providers
    const availableAIProviders = Object.entries(features)
      .filter(([key, enabled]) => ['openai', 'gemini', 'mistral', 'huggingface', 'groq', 'openrouter'].includes(key))
      .filter(([, enabled]) => enabled)
      .map(([key]) => key)
    
    return NextResponse.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: {
        connected: dbHealthy,
        provider: 'postgresql',
      },
      features,
      aiProviders: {
        count: aiProviderCount,
        available: availableAIProviders,
      },
      version: '1.0.0',
      platform: 'next.js',
    })
  } catch (error) {
    console.error('Health check error:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      platform: 'next.js',
    }, { status: 500 })
  }
}
