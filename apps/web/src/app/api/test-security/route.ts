import { NextRequest, NextResponse } from 'next/server'

/**
 * Test endpoint for security features
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Security test endpoint - GET request',
    timestamp: new Date().toISOString(),
    headers: Object.fromEntries(request.headers.entries()),
  })
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    return NextResponse.json({
      message: 'Security test endpoint - POST request',
      timestamp: new Date().toISOString(),
      body,
      headers: Object.fromEntries(request.headers.entries()),
    })
  } catch (error) {
    return NextResponse.json({
      message: 'Security test endpoint - POST request (no body)',
      timestamp: new Date().toISOString(),
      headers: Object.fromEntries(request.headers.entries()),
    })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    return NextResponse.json({
      message: 'Security test endpoint - PUT request',
      timestamp: new Date().toISOString(),
      body,
      headers: Object.fromEntries(request.headers.entries()),
    })
  } catch (error) {
    return NextResponse.json({
      message: 'Security test endpoint - PUT request (no body)',
      timestamp: new Date().toISOString(),
      headers: Object.fromEntries(request.headers.entries()),
    })
  }
}

export async function DELETE(request: NextRequest) {
  return NextResponse.json({
    message: 'Security test endpoint - DELETE request',
    timestamp: new Date().toISOString(),
    headers: Object.fromEntries(request.headers.entries()),
  })
}