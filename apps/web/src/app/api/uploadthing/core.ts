import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { requireAuth } from "@/lib/auth/session";
import { prisma } from "@/lib/database";

const f = createUploadthing();

// Auth middleware for UploadThing
const auth = async (req: Request) => {
  try {
    const session = await requireAuth();
    return { userId: session.userId };
  } catch (error) {
    throw new UploadThingError("Unauthorized");
  }
};

// Our FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Image uploader - accepts images up to 4MB
  imageUploader: f({ image: { maxFileSize: "4MB", maxFileCount: 4 } })
    .middleware(async ({ req }) => {
      const user = await auth(req);
      return { userId: user.userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Image upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Store file metadata in database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            url: file.url,
            mimeType: file.type || "image/unknown",
            size: file.size,
            userId: metadata.userId,
          },
        });

        console.log("Media file saved to database:", mediaFile.id);
        return { uploadedBy: metadata.userId, fileId: mediaFile.id };
      } catch (error) {
        console.error("Failed to save media file to database:", error);
        throw new UploadThingError("Failed to save file metadata");
      }
    }),

  // Video uploader - accepts videos up to 16MB
  videoUploader: f({ video: { maxFileSize: "16MB", maxFileCount: 1 } })
    .middleware(async ({ req }) => {
      const user = await auth(req);
      return { userId: user.userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Video upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Store file metadata in database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            url: file.url,
            mimeType: file.type || "video/unknown",
            size: file.size,
            userId: metadata.userId,
          },
        });

        console.log("Media file saved to database:", mediaFile.id);
        return { uploadedBy: metadata.userId, fileId: mediaFile.id };
      } catch (error) {
        console.error("Failed to save media file to database:", error);
        throw new UploadThingError("Failed to save file metadata");
      }
    }),

  // General media uploader - accepts both images and videos
  mediaUploader: f({ 
    image: { maxFileSize: "4MB", maxFileCount: 4 },
    video: { maxFileSize: "16MB", maxFileCount: 1 }
  })
    .middleware(async ({ req }) => {
      const user = await auth(req);
      return { userId: user.userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Media upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Determine file type based on MIME type
        const fileType = file.type?.startsWith('image/') ? 'image' : 
                        file.type?.startsWith('video/') ? 'video' : 'unknown';

        // Store file metadata in database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            url: file.url,
            mimeType: file.type || `${fileType}/unknown`,
            size: file.size,
            userId: metadata.userId,
          },
        });

        console.log("Media file saved to database:", mediaFile.id);
        return { 
          uploadedBy: metadata.userId, 
          fileId: mediaFile.id,
          fileType,
          url: file.url
        };
      } catch (error) {
        console.error("Failed to save media file to database:", error);
        throw new UploadThingError("Failed to save file metadata");
      }
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;