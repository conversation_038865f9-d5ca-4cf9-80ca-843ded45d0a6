import { NextRequest, NextResponse } from 'next/server'
import { getCSRFTokenForForm } from '@/lib/security/csrf'

/**
 * Get CSRF token for client-side forms
 */
export async function GET(request: NextRequest) {
  try {
    const csrfData = await getCSRFTokenForForm()
    
    return NextResponse.json({
      token: csrfData.token,
      fieldName: csrfData.fieldName,
      headerName: csrfData.headerName,
    })
  } catch (error) {
    console.error('CSRF token generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    )
  }
}