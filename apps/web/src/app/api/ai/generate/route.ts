import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { generateContent, generateTweetContent, generateThreadContent, improveContent } from '@/lib/ai/openai-client'
import { z } from 'zod'

// Request validation schemas
const generateContentSchema = z.object({
  type: z.enum(['tweet', 'thread', 'improve', 'custom']),
  agentId: z.string().min(1, 'Agent ID is required'),
  model: z.string().optional(),
  topic: z.string().optional(),
  content: z.string().optional(), // For improvement
  tweetCount: z.number().min(1).max(25).optional(),
  improvementType: z.enum(['engagement', 'clarity', 'tone', 'length']).optional(),
  customPrompt: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = generateContentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const { type, agentId, topic, content, tweetCount, improvementType, customPrompt } = validationResult.data
    const { model } = validationResult.data

    // Get agent and verify ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
        isActive: true,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found or inactive' },
        { status: 404 }
      )
    }

    let result

    try {
      switch (type) {
        case 'tweet':
          result = await generateTweetContent(agent.personaData, topic, undefined, model)
          break

        case 'thread':
          if (!topic) {
            return NextResponse.json(
              { error: 'Topic is required for thread generation' },
              { status: 400 }
            )
          }
          result = await generateThreadContent(agent.personaData, topic, tweetCount || 3, undefined, model)
          break

        case 'improve':
          if (!content) {
            return NextResponse.json(
              { error: 'Content is required for improvement' },
              { status: 400 }
            )
          }
          result = await improveContent(content, agent.personaData, improvementType || 'engagement', model)
          break

        case 'custom':
          if (!customPrompt) {
            return NextResponse.json(
              { error: 'Custom prompt is required' },
              { status: 400 }
            )
          }
          result = await generateContent({
            model,
            systemPrompt: `You are an AI social media agent with this persona: ${JSON.stringify(agent.personaData)}`,
            userPrompt: customPrompt,
          })
          break

        default:
          return NextResponse.json(
            { error: 'Invalid generation type' },
            { status: 400 }
          )
      }

      // Update agent metrics
      await prisma.agent.update({
        where: { id: agentId },
        data: {
          tweetsGenerated: {
            increment: type === 'thread' ? (tweetCount || 3) : 1,
          },
        },
      })

      return NextResponse.json({
        success: true,
        data: {
          content: result.content,
          usage: result.usage,
          model: result.model,
          type,
          agentId,
        },
      })
    } catch (aiError) {
      console.error('AI generation error:', aiError)
      
      return NextResponse.json(
        { 
          error: 'Content generation failed',
          message: aiError instanceof Error ? aiError.message : 'Unknown AI error',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Generate content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get generation history for an agent
export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()
    const { searchParams } = new URL(request.url)
    const agentId = searchParams.get('agentId')

    if (!agentId) {
      return NextResponse.json(
        { error: 'Agent ID is required' },
        { status: 400 }
      )
    }

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found' },
        { status: 404 }
      )
    }

    // Get recent generated content (drafts created by this agent)
    const recentContent = await prisma.scheduledTweet.findMany({
      where: {
        agentId,
        userId: session.userId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
      select: {
        id: true,
        content: true,
        status: true,
        createdAt: true,
        threadId: true,
        isThreadStart: true,
      },
    })

    return NextResponse.json({
      agent: {
        id: agent.id,
        name: agent.name,
        tweetsGenerated: agent.tweetsGenerated,
      },
      recentContent,
    })
  } catch (error) {
    console.error('Get generation history error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch generation history' },
      { status: 500 }
    )
  }
}