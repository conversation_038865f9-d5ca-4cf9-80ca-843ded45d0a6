import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { generateContentSuggestions } from '@/lib/ai/openai-client'
import { z } from 'zod'

const suggestionsSchema = z.object({
  agentId: z.string().min(1, 'Agent ID is required'),
  model: z.string().optional(),
  trendingTopics: z.array(z.string()).optional(),
  count: z.number().min(1).max(10).default(5),
})

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = suggestionsSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const { agentId, trendingTopics = [], count } = validationResult.data
    const { model } = validationResult.data

    // Get agent and verify ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
        isActive: true,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found or inactive' },
        { status: 404 }
      )
    }

    try {
      // Use agent's topics if no trending topics provided
      const topics = trendingTopics.length > 0 
        ? trendingTopics 
        : agent.personaData.topics || []

      if (topics.length === 0) {
        return NextResponse.json(
          { error: 'No topics available for suggestions' },
          { status: 400 }
        )
      }

      const suggestions = await generateContentSuggestions(
        agent.personaData,
        topics,
        count,
        model
      )

      return NextResponse.json({
        success: true,
        data: {
          suggestions,
          agentId,
          topics,
          count: suggestions.length,
        },
      })
    } catch (aiError) {
      console.error('AI suggestions error:', aiError)
      
      return NextResponse.json(
        { 
          error: 'Failed to generate suggestions',
          message: aiError instanceof Error ? aiError.message : 'Unknown AI error',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Suggestions API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}