import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { getAvailableModels } from '@/lib/ai/openai-client'

interface ModelInfo {
  id: string
  name: string
  description?: string
  provider: string
  contextLength?: number
  capabilities?: string[]
}

/**
 * Get available models from Gemini API using the new @google/genai library
 */
async function getGeminiModels(): Promise<ModelInfo[]> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      console.warn('Gemini API key not configured')
      return []
    }

    // Import the new @google/genai library
    const { GoogleAI } = await import('@google/genai')

    const client = new GoogleAI({
      apiKey: process.env.GEMINI_API_KEY,
    })

    // Use the new list models API
    const response = await client.models.list({
      config: {
        pageSize: 50,
        filter: 'supportedGenerationMethods:generateContent',
      }
    })

    const models: ModelInfo[] = []

    if (response.models) {
      for (const model of response.models) {
        // Only include models that support text generation
        if (model.supportedGenerationMethods?.includes('generateContent')) {
          models.push({
            id: model.name || '',
            name: model.displayName || model.name || '',
            description: model.description || `Gemini model: ${model.name}`,
            provider: 'Google',
            contextLength: model.inputTokenLimit || undefined,
            capabilities: [
              ...(model.supportedGenerationMethods || []),
              ...(model.supportedGenerationMethods?.includes('generateContent') ? ['text-generation'] : []),
              ...(model.supportedGenerationMethods?.includes('embedContent') ? ['embeddings'] : []),
            ],
          })
        }
      }
    }

    return models.sort((a, b) => a.name.localeCompare(b.name))
  } catch (error) {
    console.error('Failed to fetch Gemini models:', error)

    // Fallback to known Gemini models
    return [
      {
        id: 'gemini-2.0-flash-exp',
        name: 'Gemini 2.0 Flash (Experimental)',
        description: 'Latest experimental Gemini model with multimodal capabilities',
        provider: 'Google',
        contextLength: 1000000,
        capabilities: ['text-generation', 'multimodal', 'function-calling'],
      },
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: 'Most capable Gemini model for complex reasoning',
        provider: 'Google',
        contextLength: 2000000,
        capabilities: ['text-generation', 'multimodal', 'function-calling'],
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: 'Fast and efficient Gemini model',
        provider: 'Google',
        contextLength: 1000000,
        capabilities: ['text-generation', 'multimodal'],
      },
    ]
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()
    const { searchParams } = new URL(request.url)
    const provider = searchParams.get('provider') // 'openai', 'gemini', or 'all'

    // Get models from all providers
    const [openaiModels, geminiModels] = await Promise.allSettled([
      getAvailableModels(), // OpenAI-compatible models
      getGeminiModels(),    // Gemini models
    ])

    const openai = openaiModels.status === 'fulfilled' ? openaiModels.value.map(model => ({
      ...model,
      provider: model.provider || 'OpenAI',
    })) : []

    const gemini = geminiModels.status === 'fulfilled' ? geminiModels.value : []

    // Filter by provider if specified
    let models: ModelInfo[] = []
    if (provider === 'openai') {
      models = openai
    } else if (provider === 'gemini') {
      models = gemini
    } else {
      // Return all models grouped by provider
      models = [...openai, ...gemini]
    }

    // Group models by provider for better organization
    const groupedModels = models.reduce((acc, model) => {
      const providerKey = model.provider.toLowerCase()
      if (!acc[providerKey]) {
        acc[providerKey] = []
      }
      acc[providerKey].push(model)
      return acc
    }, {} as Record<string, ModelInfo[]>)

    return NextResponse.json({
      success: true,
      data: {
        models: provider ? models : groupedModels,
        summary: {
          total: models.length,
          byProvider: {
            openai: openai.length,
            gemini: gemini.length,
          },
          capabilities: {
            openaiAvailable: !!process.env.OPENAI_API_KEY,
            geminiAvailable: !!process.env.GEMINI_API_KEY,
          },
        },
        provider: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
        defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
      },
    })
  } catch (error) {
    console.error('Get models API error:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch models',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}