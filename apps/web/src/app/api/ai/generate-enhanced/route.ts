import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth/session'
import { prisma } from '@/lib/database'
import { enhancedOpenAIClient } from '@/lib/ai/enhanced-openai-client'
import { memoryService } from '@/lib/ai/memory-service'
import { geminiClient } from '@/lib/ai/gemini-client'
import { z } from 'zod'

const generateContentSchema = z.object({
  type: z.enum(['tweet', 'thread', 'improve', 'custom', 'contextual']),
  agentId: z.string().min(1, 'Agent ID is required'),
  model: z.string().optional(),
  topic: z.string().optional(),
  content: z.string().optional(),
  tweetCount: z.number().min(1).max(25).optional(),
  improvementType: z.enum(['engagement', 'clarity', 'tone', 'length']).optional(),
  customPrompt: z.string().optional(),
  useMemory: z.boolean().default(true),
  storeMemory: z.boolean().default(true),
})

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const body = await request.json()

    // Validate request body
    const validationResult = generateContentSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      )
    }

    const { 
      type, 
      agentId, 
      topic, 
      content, 
      tweetCount, 
      improvementType, 
      customPrompt,
      useMemory,
      storeMemory,
      model 
    } = validationResult.data

    // Get agent and verify ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.userId,
        isActive: true,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { error: 'Agent not found or inactive' },
        { status: 404 }
      )
    }

    let result
    let memoriesUsed = 0

    try {
      switch (type) {
        case 'tweet':
          result = await enhancedOpenAIClient.generateTweetWithMemory(
            agent.personaData,
            agentId,
            topic,
            undefined,
            model
          )
          memoriesUsed = result.memoriesUsed
          break

        case 'thread':
          if (!topic) {
            return NextResponse.json(
              { error: 'Topic is required for thread generation' },
              { status: 400 }
            )
          }
          result = await enhancedOpenAIClient.generateThreadWithMemory(
            agent.personaData,
            agentId,
            topic,
            tweetCount || 3,
            model
          )
          memoriesUsed = result.memoriesUsed
          break

        case 'contextual':
          if (!topic) {
            return NextResponse.json(
              { error: 'Query is required for contextual generation' },
              { status: 400 }
            )
          }
          const contextualResponse = await memoryService.generateContextualResponse(
            agentId,
            topic,
            agent.personaData,
            5
          )
          result = {
            content: contextualResponse,
            usage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
            model: 'gemini-enhanced',
            memoriesUsed: 5,
          }
          memoriesUsed = 5
          break

        case 'improve':
          if (!content) {
            return NextResponse.json(
              { error: 'Content is required for improvement' },
              { status: 400 }
            )
          }

          // Get relevant memories for improvement context
          let improvementMemories: string[] = []
          if (useMemory) {
            const memoryResults = await memoryService.searchMemories(
              content,
              agentId,
              { limit: 3, threshold: 0.6 }
            )
            improvementMemories = memoryResults.map(result => result.memory.content)
            memoriesUsed = improvementMemories.length
          }

          // Use Gemini for content improvement
          if (geminiClient.isAvailable()) {
            const prompt = `
You are an AI social media agent. Improve this content to be more ${improvementType}:

Original content: "${content}"

Your persona:
- Personality: ${agent.personaData.personality}
- Tone: ${agent.personaData.tone}
- Writing Style: ${agent.personaData.writingStyle}

${improvementMemories.length > 0 ? `
Based on these relevant memories:
${improvementMemories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}
` : ''}

Improved content:`

            const response = await geminiClient.generateContentWithRetry(prompt, {
              model: geminiClient.getOptimalModel('simple', true),
              temperature: 0.6,
              maxOutputTokens: 200,
            })

            result = {
              content: response.content,
              usage: response.usage || { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
              model: response.model,
              memoriesUsed,
            }
          } else {
            // Fallback to OpenAI (existing implementation)
            const { improveContent } = await import('@/lib/ai/openai-client')
            const response = await improveContent(
              content,
              agent.personaData,
              improvementType || 'engagement',
              model
            )
            result = {
              content: response.content,
              usage: response.usage,
              model: response.model,
              memoriesUsed,
            }
          }
          break

        case 'custom':
          if (!customPrompt) {
            return NextResponse.json(
              { error: 'Custom prompt is required' },
              { status: 400 }
            )
          }

          // Get relevant memories for custom prompt
          let customMemories: string[] = []
          if (useMemory) {
            const memoryResults = await memoryService.searchMemories(
              customPrompt,
              agentId,
              { limit: 3, threshold: 0.6 }
            )
            customMemories = memoryResults.map(result => result.memory.content)
            memoriesUsed = customMemories.length
          }

          if (geminiClient.isAvailable()) {
            const prompt = `
You are an AI social media agent with this persona:
- Personality: ${agent.personaData.personality}
- Tone: ${agent.personaData.tone}
- Writing Style: ${agent.personaData.writingStyle}
- Topics: ${agent.personaData.topics.join(', ')}

${customMemories.length > 0 ? `
Based on these relevant memories:
${customMemories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}
` : ''}

${customPrompt}`

            const response = await geminiClient.generateContentWithRetry(prompt, {
              model: geminiClient.getOptimalModel('medium', true),
              temperature: 0.7,
              maxOutputTokens: 1000,
            })

            result = {
              content: response.content,
              usage: response.usage || { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
              model: response.model,
              memoriesUsed,
            }
          } else {
            // Fallback to OpenAI
            const { generateContent } = await import('@/lib/ai/openai-client')
            const response = await generateContent({
              model,
              systemPrompt: `You are an AI social media agent with this persona: ${JSON.stringify(agent.personaData)}`,
              userPrompt: customPrompt,
            })
            result = {
              content: response.content,
              usage: response.usage,
              model: response.model,
              memoriesUsed,
            }
          }
          break

        default:
          return NextResponse.json(
            { error: 'Invalid generation type' },
            { status: 400 }
          )
      }

      // Store interaction as memory if requested
      if (storeMemory && result.content) {
        try {
          await enhancedOpenAIClient.storeInteractionMemory(agentId, {
            userInput: topic || customPrompt || content || `${type} generation`,
            agentResponse: result.content,
            context: type,
          })
        } catch (memoryError) {
          console.error('Failed to store memory:', memoryError)
          // Don't fail the request if memory storage fails
        }
      }

      // Update agent metrics
      await prisma.agent.update({
        where: { id: agentId },
        data: {
          tweetsGenerated: {
            increment: type === 'thread' ? (tweetCount || 3) : 1,
          },
        },
      })

      return NextResponse.json({
        success: true,
        data: {
          content: result.content,
          usage: result.usage,
          model: result.model,
          type,
          agentId,
          memoriesUsed,
          enhancedFeatures: {
            memoryIntegration: useMemory,
            geminiPowered: result.model.includes('gemini'),
            costOptimized: true,
          },
        },
      })
    } catch (aiError) {
      console.error('Enhanced AI generation error:', aiError)
      
      return NextResponse.json(
        { 
          error: 'Content generation failed',
          message: aiError instanceof Error ? aiError.message : 'Unknown AI error',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Enhanced generate content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get generation capabilities and status
export async function GET(request: NextRequest) {
  try {
    const session = await requireAuth()

    // Dynamically fetch available models from the models endpoint
    let availableModels = {
      openai: [] as string[],
      gemini: [] as string[],
    }

    try {
      // Import the functions to get models dynamically
      const { getAvailableModels } = await import('@/lib/ai/openai-client')

      // Get OpenAI models
      const openaiModels = await getAvailableModels()
      availableModels.openai = openaiModels.map(model => model.id)

      // Get Gemini models if available
      if (process.env.GEMINI_API_KEY) {
        try {
          const { GoogleAI } = await import('@google/genai')
          const client = new GoogleAI({
            apiKey: process.env.GEMINI_API_KEY,
          })

          const response = await client.models.list({
            config: {
              pageSize: 20,
              filter: 'supportedGenerationMethods:generateContent',
            }
          })

          if (response.models) {
            availableModels.gemini = response.models
              .filter(model => model.supportedGenerationMethods?.includes('generateContent'))
              .map(model => model.name || '')
              .filter(name => name.length > 0)
          }
        } catch (geminiError) {
          console.warn('Failed to fetch Gemini models, using fallback:', geminiError)
          // Fallback to known models
          availableModels.gemini = ['gemini-2.0-flash-exp', 'gemini-1.5-pro', 'gemini-1.5-flash']
        }
      }
    } catch (modelsError) {
      console.warn('Failed to fetch dynamic models, using fallbacks:', modelsError)
      // Fallback to basic models
      availableModels.openai = ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']
      availableModels.gemini = geminiClient.isAvailable() ? ['gemini-1.5-pro', 'gemini-1.5-flash'] : []
    }

    return NextResponse.json({
      success: true,
      data: {
        capabilities: {
          memoryIntegration: true,
          geminiSupport: geminiClient.isAvailable(),
          multimodalSupport: geminiClient.isAvailable(),
          functionCalling: geminiClient.isAvailable(),
          costOptimization: true,
          dynamicModelFetching: true,
        },
        models: availableModels,
        features: [
          'Vector-based memory search',
          'Contextual content generation',
          'Multi-model support',
          'Cost optimization',
          'Enhanced persona consistency',
          'Dynamic model discovery',
        ],
      },
    })
  } catch (error) {
    console.error('Get enhanced capabilities error:', error)
    return NextResponse.json(
      { error: 'Failed to get capabilities' },
      { status: 500 }
    )
  }
}