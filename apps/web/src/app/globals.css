@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* XTask Custom Colors - Proper HSL Format */
    --primary-50: 270 100% 98%;   /* #faf5ff */
    --primary-100: 269 100% 95%;  /* #f3e8ff */
    --primary-200: 269 100% 92%;  /* #e9d5ff */
    --primary-300: 268 71% 85%;   /* #d8b4fe */
    --primary-400: 270 95% 75%;   /* #c4b5fd */
    --primary-500: 271 91% 65%;   /* #8b5cf6 - Main purple */
    --primary-600: 271 81% 56%;   /* #7c3aed */
    --primary-700: 272 72% 47%;   /* #6d28d9 */
    --primary-800: 273 67% 39%;   /* #5b21b6 */
    --primary-900: 274 66% 32%;   /* #4c1d95 */
    --primary-950: 276 100% 20%;  /* #2e1065 */
    --primary-foreground: 0 0% 100%; /* White */

    /* Dark Theme Colors - Proper HSL Format */
    --dark-bg: 0 0% 4%;      /* #0a0a0a */
    --dark-surface: 0 0% 10%; /* #1a1a1a */
    --dark-border: 0 0% 16%;  /* #2a2a2a */

    /* shadcn/ui Variables - Proper HSL Format */
    --background: 0 0% 4%;        /* Dark background #0a0a0a */
    --foreground: 0 0% 98%;       /* Light text */
    --card: 0 0% 10%;             /* Dark surface #1a1a1a */
    --card-foreground: 0 0% 98%;  /* Light text on cards */
    --popover: 0 0% 10%;          /* Dark popover */
    --popover-foreground: 0 0% 98%; /* Light text in popovers */
    --primary: 271 91% 65%;       /* Primary purple #8b5cf6 */
    --primary-foreground: 0 0% 100%; /* White text on purple */
    --secondary: 0 0% 16%;        /* Dark border #2a2a2a */
    --secondary-foreground: 0 0% 98%; /* Light text on secondary */
    --muted: 0 0% 16%;            /* Muted background */
    --muted-foreground: 240 5% 65%; /* Muted text */
    --accent: 271 91% 65%;        /* Accent purple same as primary */
    --accent-foreground: 0 0% 100%; /* White text on accent */
    --destructive: 0 63% 31%;     /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%; /* Light text on destructive */
    --border: 0 0% 16%;           /* Border color #2a2a2a */
    --input: 0 0% 16%;            /* Input background */
    --ring: 271 91% 65%;          /* Focus ring purple */
    --radius: 0.75rem;            /* Border radius */
    --chart-1: 220 70% 50%;       /* Chart colors */
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  .light {
    /* Light theme overrides - Proper HSL Format */
    --background: 0 0% 100%;      /* White background */
    --foreground: 0 0% 4%;        /* Dark text */
    --card: 0 0% 100%;            /* White cards */
    --card-foreground: 0 0% 4%;   /* Dark text on cards */
    --popover: 0 0% 100%;         /* White popovers */
    --popover-foreground: 0 0% 4%; /* Dark text in popovers */
    --primary: 271 91% 65%;       /* Keep primary purple #8b5cf6 */
    --primary-foreground: 0 0% 100%; /* White text on purple */
    --secondary: 0 0% 96%;        /* Light gray secondary */
    --secondary-foreground: 0 0% 9%; /* Dark text on secondary */
    --muted: 0 0% 96%;            /* Light muted background */
    --muted-foreground: 0 0% 45%; /* Muted text */
    --accent: 0 0% 96%;           /* Light accent */
    --accent-foreground: 0 0% 9%; /* Dark text on accent */
    --destructive: 0 84% 60%;     /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%; /* Light text on destructive */
    --border: 0 0% 90%;           /* Light border */
    --input: 0 0% 90%;            /* Light input background */
    --ring: 271 91% 65%;          /* Focus ring purple */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-card;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary rounded-full;
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}