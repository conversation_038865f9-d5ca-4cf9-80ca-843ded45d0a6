"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { Spinner } from "@/components/ui/spinner"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/hooks/use-toast"
import { Bot, Zap, TrendingUp, Calendar, CheckCircle, AlertCircle } from "lucide-react"

export default function StylingTestPage() {
  const { toast } = useToast()

  const testToast = () => {
    toast({
      variant: "success",
      title: "Styling Test",
      description: "All components are working with the custom color palette!",
    })
  }

  return (
    <div className="min-h-screen bg-dark-bg p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header with Theme Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white">Styling System Test</h1>
            <p className="text-muted-foreground mt-2">
              Verifying Tailwind CSS 3.4.x and shadcn/ui integration with custom color palette
            </p>
          </div>
          <ThemeToggle />
        </div>

        {/* Color Palette Verification */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Color Palette</CardTitle>
            <CardDescription>
              Testing the specified colors: primary-500 (#8b5cf6), dark-bg (#0a0a0a), 
              dark-surface (#1a1a1a), dark-border (#2a2a2a)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-primary-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium">#8b5cf6</span>
                </div>
                <p className="text-sm text-center">Primary 500</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-dark-bg rounded-lg flex items-center justify-center border border-dark-border">
                  <span className="text-white font-medium">#0a0a0a</span>
                </div>
                <p className="text-sm text-center">Dark BG</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-dark-surface rounded-lg flex items-center justify-center border border-dark-border">
                  <span className="text-white font-medium">#1a1a1a</span>
                </div>
                <p className="text-sm text-center">Dark Surface</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-dark-border rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium">#2a2a2a</span>
                </div>
                <p className="text-sm text-center">Dark Border</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Gradient Backgrounds */}
        <Card>
          <CardHeader>
            <CardTitle>Gradient Backgrounds</CardTitle>
            <CardDescription>Custom gradient utilities using the color palette</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="h-24 bg-gradient-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-medium">Primary Gradient</span>
              </div>
              <div className="h-24 bg-gradient-secondary rounded-lg flex items-center justify-center">
                <span className="text-white font-medium">Secondary Gradient</span>
              </div>
              <div className="h-24 bg-gradient-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-medium">Accent Gradient</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Button Variants with Primary Color */}
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>
              Testing Button component with primary-500 background and proper theming
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button onClick={testToast}>Primary Button</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button loading>Loading</Button>
              <Button disabled>Disabled</Button>
            </div>
          </CardContent>
        </Card>

        {/* Card Components */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
              <Bot className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-green-500 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +1 from last week
              </p>
              <div className="mt-3 flex gap-2">
                <Badge>Active</Badge>
                <Badge variant="secondary">AI</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tweets Today</CardTitle>
              <Zap className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-green-500 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +20% from yesterday
              </p>
              <div className="mt-3">
                <Progress value={75} className="w-full" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Calendar className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground mt-1">Next in 2 hours</p>
              <div className="mt-3 flex items-center space-x-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="bg-gradient-primary text-white text-xs">
                    AI
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm text-muted-foreground">Tech Guru</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Form Elements */}
        <Card>
          <CardHeader>
            <CardTitle>Form Elements</CardTitle>
            <CardDescription>Input fields and form components with proper theming</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Agent Name</label>
                <Input placeholder="Enter agent name..." />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Input placeholder="Agent description..." />
              </div>
            </div>
            <div className="flex gap-2">
              <Button>Save Changes</Button>
              <Button variant="outline">Cancel</Button>
            </div>
          </CardContent>
        </Card>

        {/* Loading States */}
        <Card>
          <CardHeader>
            <CardTitle>Loading States</CardTitle>
            <CardDescription>Spinner components with different variants</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-6">
              <div className="space-y-2">
                <p className="text-sm font-medium">Default</p>
                <Spinner />
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Muted</p>
                <Spinner variant="muted" />
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Small</p>
                <Spinner size="sm" />
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Large</p>
                <Spinner size="lg" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Indicators */}
        <Card>
          <CardHeader>
            <CardTitle>Status Indicators</CardTitle>
            <CardDescription>Various status badges and indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">System Online</span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">Maintenance Mode</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 bg-primary-500 rounded-full animate-pulse"></div>
                  <span className="text-sm">Processing</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge>Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Error</Badge>
                <Badge className="bg-green-500 hover:bg-green-600">Success</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Animation Test */}
        <Card className="animate-fade-in">
          <CardHeader>
            <CardTitle>Animation Test</CardTitle>
            <CardDescription>Custom animations and transitions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-4 bg-dark-surface rounded-full overflow-hidden">
                <div className="h-full bg-gradient-primary animate-pulse-slow rounded-full w-3/4"></div>
              </div>
              <p className="text-sm text-muted-foreground animate-slide-up">
                This card uses custom fade-in animation, and this text slides up.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        <Card className="border-primary-500/20 bg-primary-500/5">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Styling System Integration Complete</span>
            </CardTitle>
            <CardDescription>
              All components are properly styled with the custom color palette and theme system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>✅ Tailwind CSS 3.4.x configured and working</p>
              <p>✅ Custom color palette implemented with CSS variables</p>
              <p>✅ shadcn/ui components integrated and themed</p>
              <p>✅ Dark/Light theme support with next-themes</p>
              <p>✅ Custom gradients and animations working</p>
              <p>✅ All components responsive and accessible</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}