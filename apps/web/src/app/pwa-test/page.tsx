'use client'

import * as React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Smartphone, 
  Wifi, 
  WifiOff, 
  Download, 
  CheckCircle,
  AlertCircle,
  Globe,
  Zap,
  Bell
} from "lucide-react"
import { usePWA, useOnlineStatus, useAppInstall } from '@/components/pwa/pwa-provider'

export default function PWATestPage() {
  const { isOnline } = usePWA()
  const { canInstall, installApp } = useAppInstall()
  const [serviceWorkerStatus, setServiceWorkerStatus] = React.useState<string>('checking')
  const [notificationPermission, setNotificationPermission] = React.useState<string>('default')

  React.useEffect(() => {
    // Check service worker status
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(() => {
        setServiceWorkerStatus('active')
      }).catch(() => {
        setServiceWorkerStatus('failed')
      })
    } else {
      setServiceWorkerStatus('unsupported')
    }

    // Check notification permission
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission)
    }
  }, [])

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      setNotificationPermission(permission)
    }
  }

  const testNotification = () => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('XTask PWA Test', {
        body: 'This is a test notification from XTask!',
        icon: '/icons/icon.svg',
        badge: '/icons/icon.svg'
      })
    }
  }

  const testOfflineMode = () => {
    // Simulate offline mode for testing
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        registration.active?.postMessage({ type: 'TEST_OFFLINE' })
      })
    }
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">
          PWA Functionality Test
        </h1>
        <p className="text-muted-foreground text-sm sm:text-base">
          Testing Progressive Web App features and capabilities
        </p>
      </div>

      {/* Connection Status */}
      <Card className={isOnline ? "border-green-500/20 bg-green-500/5" : "border-red-500/20 bg-red-500/5"}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {isOnline ? <Wifi className="h-5 w-5 text-green-500" /> : <WifiOff className="h-5 w-5 text-red-500" />}
            <span>Connection Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className={`font-medium ${isOnline ? 'text-green-500' : 'text-red-500'}`}>
                {isOnline ? 'Online' : 'Offline'}
              </p>
              <p className="text-sm text-muted-foreground">
                {isOnline ? 'All features available' : 'Limited functionality available'}
              </p>
            </div>
            <Badge variant={isOnline ? "default" : "destructive"}>
              {isOnline ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* PWA Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
        {/* Service Worker Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <span>Service Worker</span>
            </CardTitle>
            <CardDescription>Background processing and caching</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium capitalize">{serviceWorkerStatus}</p>
                <p className="text-sm text-muted-foreground">
                  {serviceWorkerStatus === 'active' && 'Caching enabled'}
                  {serviceWorkerStatus === 'checking' && 'Initializing...'}
                  {serviceWorkerStatus === 'failed' && 'Registration failed'}
                  {serviceWorkerStatus === 'unsupported' && 'Not supported'}
                </p>
              </div>
              {serviceWorkerStatus === 'active' ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-yellow-500" />
              )}
            </div>
          </CardContent>
        </Card>

        {/* App Installation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Download className="h-5 w-5" />
              <span>App Installation</span>
            </CardTitle>
            <CardDescription>Install as native app</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">
                    {canInstall ? 'Available' : 'Not Available'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {canInstall ? 'Ready to install' : 'Already installed or not supported'}
                  </p>
                </div>
                {canInstall ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-gray-500" />
                )}
              </div>
              {canInstall && (
                <Button onClick={installApp} className="w-full">
                  Install XTask
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Notifications</span>
            </CardTitle>
            <CardDescription>Push notification support</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium capitalize">{notificationPermission}</p>
                  <p className="text-sm text-muted-foreground">
                    {notificationPermission === 'granted' && 'Notifications enabled'}
                    {notificationPermission === 'denied' && 'Notifications blocked'}
                    {notificationPermission === 'default' && 'Permission not requested'}
                  </p>
                </div>
                {notificationPermission === 'granted' ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-yellow-500" />
                )}
              </div>
              <div className="flex space-x-2">
                {notificationPermission !== 'granted' && (
                  <Button onClick={requestNotificationPermission} variant="outline" size="sm">
                    Enable
                  </Button>
                )}
                {notificationPermission === 'granted' && (
                  <Button onClick={testNotification} size="sm">
                    Test
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Offline Capabilities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>Offline Support</span>
            </CardTitle>
            <CardDescription>Cached content and offline functionality</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">
                    {serviceWorkerStatus === 'active' ? 'Enabled' : 'Disabled'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {serviceWorkerStatus === 'active' 
                      ? 'Pages and data cached for offline use' 
                      : 'Offline functionality not available'
                    }
                  </p>
                </div>
                {serviceWorkerStatus === 'active' ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-gray-500" />
                )}
              </div>
              <Button onClick={testOfflineMode} variant="outline" size="sm" className="w-full">
                Test Offline Mode
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* PWA Checklist */}
      <Card className="border-primary-500/20 bg-primary-500/5">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-primary" />
            <span>PWA Readiness Checklist</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Web App Manifest</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Service Worker</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>HTTPS (in production)</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Responsive Design</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>App Icons</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Offline Functionality</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Fast Loading</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>App-like Experience</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
