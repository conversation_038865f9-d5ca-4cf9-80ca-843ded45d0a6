/**
 * Twitter-specific utilities for character counting and text processing
 */

// Twitter's URL length constants (as of 2024)
const TWITTER_URL_LENGTH = 23 // All URLs are counted as 23 characters
const TWITTER_MEDIA_URL_LENGTH = 24 // Media URLs are counted as 24 characters

/**
 * Calculate Twitter character count according to Twitter's rules
 * - URLs are counted as 23 characters regardless of actual length
 * - Media URLs are counted as 24 characters
 * - Mentions and hashtags count as their full length
 */
export function getTwitterCharacterCount(text: string): number {
  if (!text) return 0

  let processedText = text

  // Replace all URLs with placeholder of correct length
  const urlRegex = /https?:\/\/[^\s]+/g
  const urls = text.match(urlRegex) || []
  
  urls.forEach(url => {
    // Check if it's a media URL (common image/video hosting domains)
    const isMediaUrl = /\.(jpg|jpeg|png|gif|webp|mp4|mov|avi)$/i.test(url) ||
                      /twitter\.com\/.*\/status\/.*\/photo/i.test(url) ||
                      /pic\.twitter\.com/i.test(url) ||
                      /instagram\.com\/p\//i.test(url) ||
                      /youtube\.com\/watch/i.test(url) ||
                      /youtu\.be\//i.test(url)
    
    const replacementLength = isMediaUrl ? TWITTER_MEDIA_URL_LENGTH : TWITTER_URL_LENGTH
    const replacement = 'x'.repeat(replacementLength)
    processedText = processedText.replace(url, replacement)
  })

  return processedText.length
}

/**
 * Extract mentions from text (@username)
 */
export function extractMentions(text: string): string[] {
  const mentionRegex = /@([a-zA-Z0-9_]+)/g
  const mentions = []
  let match

  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1])
  }

  return mentions
}

/**
 * Extract hashtags from text (#hashtag)
 */
export function extractHashtags(text: string): string[] {
  const hashtagRegex = /#([a-zA-Z0-9_]+)/g
  const hashtags = []
  let match

  while ((match = hashtagRegex.exec(text)) !== null) {
    hashtags.push(match[1])
  }

  return hashtags
}

/**
 * Extract URLs from text
 */
export function extractUrls(text: string): string[] {
  const urlRegex = /https?:\/\/[^\s]+/g
  return text.match(urlRegex) || []
}

/**
 * Check if text is a valid tweet length
 */
export function isValidTweetLength(text: string, maxLength: number = 280): boolean {
  return getTwitterCharacterCount(text) <= maxLength
}

/**
 * Get remaining characters for a tweet
 */
export function getRemainingCharacters(text: string, maxLength: number = 280): number {
  return maxLength - getTwitterCharacterCount(text)
}

/**
 * Truncate text to fit Twitter character limit while preserving words
 */
export function truncateToTwitterLimit(text: string, maxLength: number = 280): string {
  if (getTwitterCharacterCount(text) <= maxLength) {
    return text
  }

  // Simple word-boundary truncation
  const words = text.split(' ')
  let truncated = ''
  
  for (const word of words) {
    const testText = truncated ? `${truncated} ${word}` : word
    if (getTwitterCharacterCount(testText) <= maxLength) {
      truncated = testText
    } else {
      break
    }
  }

  return truncated
}

/**
 * Format text for Twitter display (highlight mentions, hashtags, links)
 */
export function formatTwitterText(text: string): string {
  let formatted = text

  // Highlight mentions
  formatted = formatted.replace(
    /@([a-zA-Z0-9_]+)/g,
    '<span class="text-blue-500 font-medium">@$1</span>'
  )

  // Highlight hashtags
  formatted = formatted.replace(
    /#([a-zA-Z0-9_]+)/g,
    '<span class="text-blue-500 font-medium">#$1</span>'
  )

  // Highlight URLs
  formatted = formatted.replace(
    /(https?:\/\/[^\s]+)/g,
    '<a href="$1" class="text-blue-500 underline" target="_blank" rel="noopener noreferrer">$1</a>'
  )

  return formatted
}

/**
 * Validate tweet content
 */
export interface TweetValidation {
  isValid: boolean
  characterCount: number
  remainingCharacters: number
  isOverLimit: boolean
  isNearLimit: boolean
  errors: string[]
  warnings: string[]
}

export function validateTweet(text: string, maxLength: number = 280): TweetValidation {
  const characterCount = getTwitterCharacterCount(text)
  const remainingCharacters = maxLength - characterCount
  const isOverLimit = characterCount > maxLength
  const isNearLimit = characterCount > maxLength * 0.8
  const errors: string[] = []
  const warnings: string[] = []

  // Check for empty content
  if (!text.trim()) {
    errors.push('Tweet cannot be empty')
  }

  // Check character limit
  if (isOverLimit) {
    errors.push(`Tweet exceeds character limit by ${Math.abs(remainingCharacters)} characters`)
  }

  // Check for near limit
  if (isNearLimit && !isOverLimit) {
    warnings.push('Tweet is approaching character limit')
  }

  // Check for excessive mentions (Twitter limits to 50 mentions per tweet)
  const mentions = extractMentions(text)
  if (mentions.length > 50) {
    errors.push('Too many mentions (maximum 50 per tweet)')
  }

  // Check for excessive hashtags (best practice is 1-2 hashtags)
  const hashtags = extractHashtags(text)
  if (hashtags.length > 5) {
    warnings.push('Consider using fewer hashtags for better engagement')
  }

  return {
    isValid: errors.length === 0,
    characterCount,
    remainingCharacters,
    isOverLimit,
    isNearLimit,
    errors,
    warnings,
  }
}

/**
 * Twitter character limits and constants
 */
export const TWITTER_LIMITS = {
  TWEET_MAX_LENGTH: 280,
  URL_LENGTH: TWITTER_URL_LENGTH,
  MEDIA_URL_LENGTH: TWITTER_MEDIA_URL_LENGTH,
  MAX_MENTIONS: 50,
  MAX_HASHTAGS_RECOMMENDED: 2,
  MAX_HASHTAGS_ABSOLUTE: 5,
  THREAD_MAX_TWEETS: 25,
} as const
