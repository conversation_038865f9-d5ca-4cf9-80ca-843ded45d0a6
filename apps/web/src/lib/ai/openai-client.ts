import OpenAI from 'openai'

// OpenAI-compatible client configuration
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
})

export interface GenerateContentOptions {
  model?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  systemPrompt?: string
  userPrompt: string
  context?: string[]
}

export interface GenerateContentResponse {
  content: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model: string
  finishReason: string
}

export interface ModelInfo {
  id: string
  name: string
  description?: string
  contextLength?: number
  provider?: string
}

/**
 * Get available models from the OpenAI-compatible endpoint
 */
export async function getAvailableModels(): Promise<ModelInfo[]> {
  try {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key is not configured')
    }

    const models = await openai.models.list()
    
    return models.data
      .filter(model => model.id.includes('gpt') || model.id.includes('claude') || model.id.includes('llama') || model.id.includes('mistral'))
      .map(model => ({
        id: model.id,
        name: model.id,
        description: `Model: ${model.id}`,
        provider: getProviderFromBaseURL(),
      }))
      .sort((a, b) => a.name.localeCompare(b.name))
  } catch (error) {
    console.error('Failed to fetch models:', error)
    
    // Fallback to common models if API call fails
    return getDefaultModels()
  }
}

/**
 * Get default models based on the base URL
 */
function getDefaultModels(): ModelInfo[] {
  const baseURL = process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1'
  const provider = getProviderFromBaseURL()

  if (baseURL.includes('openai.com')) {
    return [
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Most capable GPT-4 model', provider: 'OpenAI' },
      { id: 'gpt-4', name: 'GPT-4', description: 'High-quality reasoning model', provider: 'OpenAI' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and efficient model', provider: 'OpenAI' },
    ]
  } else if (baseURL.includes('anthropic.com')) {
    return [
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful Claude model', provider: 'Anthropic' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced Claude model', provider: 'Anthropic' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: 'Fast Claude model', provider: 'Anthropic' },
    ]
  } else if (baseURL.includes('together.xyz')) {
    return [
      { id: 'meta-llama/Llama-2-70b-chat-hf', name: 'Llama 2 70B', description: 'Large Llama model', provider: 'Together AI' },
      { id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', name: 'Mixtral 8x7B', description: 'Mixture of experts model', provider: 'Together AI' },
      { id: 'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO', name: 'Nous Hermes 2', description: 'Fine-tuned Mixtral', provider: 'Together AI' },
    ]
  } else if (baseURL.includes('groq.com')) {
    return [
      { id: 'llama2-70b-4096', name: 'Llama 2 70B', description: 'Fast Llama inference', provider: 'Groq' },
      { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', description: 'Fast Mixtral inference', provider: 'Groq' },
      { id: 'gemma-7b-it', name: 'Gemma 7B', description: 'Google Gemma model', provider: 'Groq' },
    ]
  } else {
    // Generic fallback
    return [
      { id: process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo', name: 'Default Model', description: 'Default model', provider: 'Custom' },
    ]
  }
}

/**
 * Get provider name from base URL
 */
function getProviderFromBaseURL(): string {
  const baseURL = process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1'
  
  if (baseURL.includes('openai.com')) return 'OpenAI'
  if (baseURL.includes('anthropic.com')) return 'Anthropic'
  if (baseURL.includes('together.xyz')) return 'Together AI'
  if (baseURL.includes('groq.com')) return 'Groq'
  if (baseURL.includes('huggingface.co')) return 'Hugging Face'
  if (baseURL.includes('replicate.com')) return 'Replicate'
  
  return 'Custom Provider'
}

/**
 * Generate content using OpenAI-compatible API
 */
export async function generateContent(options: GenerateContentOptions): Promise<GenerateContentResponse> {
  try {
    const {
      model = process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
      maxTokens = parseInt(process.env.OPENAI_MAX_TOKENS || '1000'),
      temperature = parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
      topP = 1,
      systemPrompt,
      userPrompt,
      context = [],
    } = options

    // Validate API key
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('API key is not configured')
    }

    // Build messages array
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = []

    // Add system prompt if provided
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt,
      })
    }

    // Add context messages if provided
    context.forEach((contextMessage, index) => {
      messages.push({
        role: index % 2 === 0 ? 'user' : 'assistant',
        content: contextMessage,
      })
    })

    // Add user prompt
    messages.push({
      role: 'user',
      content: userPrompt,
    })

    // Make API call
    const completion = await openai.chat.completions.create({
      model,
      messages,
      max_tokens: maxTokens,
      temperature,
      top_p: topP,
      stream: false,
    })

    // Extract response
    const choice = completion.choices[0]
    if (!choice || !choice.message.content) {
      throw new Error('No content generated from API')
    }

    return {
      content: choice.message.content,
      usage: {
        promptTokens: completion.usage?.prompt_tokens || 0,
        completionTokens: completion.usage?.completion_tokens || 0,
        totalTokens: completion.usage?.total_tokens || 0,
      },
      model: completion.model,
      finishReason: choice.finish_reason || 'unknown',
    }
  } catch (error) {
    console.error('OpenAI-compatible API error:', error)
    
    // Handle specific OpenAI errors
    if (error instanceof OpenAI.APIError) {
      if (error.status === 401) {
        throw new Error('Invalid API key')
      } else if (error.status === 429) {
        throw new Error('API rate limit exceeded. Please try again later.')
      } else if (error.status === 400) {
        throw new Error('Invalid request to API')
      } else if (error.status === 500) {
        throw new Error('API server error. Please try again later.')
      } else if (error.status === 404) {
        throw new Error('Model not found. Please check the model name.')
      }
    }

    if (error instanceof OpenAI.APITimeoutError) {
      throw new Error('API request timed out. Please try again.')
    }

    if (error instanceof OpenAI.APIConnectionError) {
      throw new Error('Failed to connect to API. Please check your internet connection.')
    }

    // Re-throw with generic message for unknown errors
    throw new Error(`Content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Generate tweet content based on agent persona and topic
 */
export async function generateTweetContent(
  agentPersona: any,
  topic?: string,
  context?: string[],
  model?: string
): Promise<GenerateContentResponse> {
  const systemPrompt = `You are an AI social media agent with the following persona:

Personality: ${agentPersona.personality}
Tone: ${agentPersona.tone}
Writing Style: ${agentPersona.writingStyle}
Topics of Interest: ${agentPersona.topics.join(', ')}
Communication Style: ${agentPersona.communicationStyle || 'Engaging and authentic'}
Target Audience: ${agentPersona.targetAudience || 'General audience'}

Content Restrictions:
${agentPersona.restrictions.map((r: string) => `- ${r}`).join('\n')}

${agentPersona.avoidTopics?.length ? `Topics to Avoid: ${agentPersona.avoidTopics.join(', ')}` : ''}

Generate engaging, authentic tweets that match this persona. Keep tweets under 280 characters. Be creative, informative, and true to the personality described.`

  const userPrompt = topic 
    ? `Create a tweet about: ${topic}`
    : `Create an engaging tweet about one of your topics of interest.`

  return generateContent({
    model,
    systemPrompt,
    userPrompt,
    context,
    maxTokens: 150, // Shorter for tweets
    temperature: 0.8, // More creative for social media
  })
}

/**
 * Generate thread content based on agent persona and topic
 */
export async function generateThreadContent(
  agentPersona: any,
  topic: string,
  tweetCount: number = 3,
  context?: string[],
  model?: string
): Promise<GenerateContentResponse> {
  const systemPrompt = `You are an AI social media agent with the following persona:

Personality: ${agentPersona.personality}
Tone: ${agentPersona.tone}
Writing Style: ${agentPersona.writingStyle}
Topics of Interest: ${agentPersona.topics.join(', ')}
Communication Style: ${agentPersona.communicationStyle || 'Engaging and authentic'}
Target Audience: ${agentPersona.targetAudience || 'General audience'}

Content Restrictions:
${agentPersona.restrictions.map((r: string) => `- ${r}`).join('\n')}

${agentPersona.avoidTopics?.length ? `Topics to Avoid: ${agentPersona.avoidTopics.join(', ')}` : ''}

Generate a Twitter thread with ${tweetCount} tweets. Each tweet should be under 280 characters and numbered (1/n, 2/n, etc.). The thread should flow naturally and provide value to readers.`

  const userPrompt = `Create a ${tweetCount}-tweet thread about: ${topic}`

  return generateContent({
    model,
    systemPrompt,
    userPrompt,
    context,
    maxTokens: 500 * tweetCount, // More tokens for threads
    temperature: 0.8,
  })
}

/**
 * Improve existing content based on agent persona
 */
export async function improveContent(
  content: string,
  agentPersona: any,
  improvementType: 'engagement' | 'clarity' | 'tone' | 'length' = 'engagement',
  model?: string
): Promise<GenerateContentResponse> {
  const systemPrompt = `You are an AI social media agent with the following persona:

Personality: ${agentPersona.personality}
Tone: ${agentPersona.tone}
Writing Style: ${agentPersona.writingStyle}

Improve the given content to better match this persona and optimize for ${improvementType}. Keep it under 280 characters for Twitter.`

  const userPrompt = `Improve this content: "${content}"`

  return generateContent({
    model,
    systemPrompt,
    userPrompt,
    maxTokens: 150,
    temperature: 0.6, // Less creative for improvements
  })
}

/**
 * Generate content suggestions based on trending topics
 */
export async function generateContentSuggestions(
  agentPersona: any,
  trendingTopics: string[],
  count: number = 5,
  model?: string
): Promise<string[]> {
  const systemPrompt = `You are an AI social media agent with the following persona:

Personality: ${agentPersona.personality}
Tone: ${agentPersona.tone}
Topics of Interest: ${agentPersona.topics.join(', ')}

Generate ${count} tweet ideas based on the trending topics provided. Each idea should be a brief description (not the full tweet) that matches your persona and interests.`

  const userPrompt = `Generate tweet ideas based on these trending topics: ${trendingTopics.join(', ')}`

  const response = await generateContent({
    model,
    systemPrompt,
    userPrompt,
    maxTokens: 300,
    temperature: 0.9, // Very creative for brainstorming
  })

  // Parse the response into individual suggestions
  return response.content
    .split('\n')
    .filter(line => line.trim())
    .map(line => line.replace(/^\d+\.\s*/, '').trim())
    .filter(suggestion => suggestion.length > 0)
    .slice(0, count)
}

export { openai }