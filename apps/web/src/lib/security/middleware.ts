import { NextRequest, NextResponse } from 'next/server'
import { edgeRateLimiter, RATE_LIMIT_CONFIGS, generateRateLimitKey, getClientIP } from './edge-rate-limiter'
import { requireCSRFToken } from './csrf'
import { validateSessionFromRequest } from '@/lib/auth/session'

// Security middleware configuration
interface SecurityConfig {
  rateLimit?: {
    enabled: boolean
    config: keyof typeof RATE_LIMIT_CONFIGS
    customConfig?: {
      windowMs: number
      maxRequests: number
    }
  }
  csrf?: {
    enabled: boolean
    skipPaths?: string[]
  }
  auth?: {
    required: boolean
  }
}

// Default security configurations for different route patterns
export const SECURITY_CONFIGS: Record<string, SecurityConfig> = {
  // Authentication endpoints
  '/api/auth/login': {
    rateLimit: { enabled: true, config: 'auth' },
    csrf: { enabled: true },
    auth: { required: false }
  },
  '/api/auth/register': {
    rateLimit: { enabled: true, config: 'auth' },
    csrf: { enabled: true },
    auth: { required: false }
  },
  '/api/auth/refresh': {
    rateLimit: { enabled: true, config: 'auth' },
    csrf: { enabled: false }, // Refresh doesn't need CSRF
    auth: { required: false }
  },
  
  // API endpoints
  '/api/agents': {
    rateLimit: { enabled: true, config: 'api' },
    csrf: { enabled: true },
    auth: { required: true }
  },
  '/api/tweets': {
    rateLimit: { enabled: true, config: 'content' },
    csrf: { enabled: true },
    auth: { required: true }
  },
  '/api/schedule': {
    rateLimit: { enabled: true, config: 'content' },
    csrf: { enabled: true },
    auth: { required: true }
  },
  '/api/upload': {
    rateLimit: { enabled: true, config: 'upload' },
    csrf: { enabled: true },
    auth: { required: true }
  },
  
  // Public endpoints
  '/api/health': {
    rateLimit: { enabled: true, config: 'public' },
    csrf: { enabled: false },
    auth: { required: false }
  },
  
  // Default for other API routes
  '/api/*': {
    rateLimit: { enabled: true, config: 'api' },
    csrf: { enabled: true },
    auth: { required: true }
  }
}

/**
 * Apply security middleware to request
 */
export async function applySecurityMiddleware(
  request: NextRequest,
  config?: SecurityConfig
): Promise<NextResponse | null> {
  const pathname = request.nextUrl.pathname
  
  // Get configuration for this route
  const routeConfig = config || getSecurityConfigForRoute(pathname)
  
  // Temporarily disable rate limiting and CSRF for debugging
  // TODO: Re-enable after fixing Edge Runtime issues

  // Apply rate limiting
  // if (routeConfig.rateLimit?.enabled) {
  //   const rateLimitResponse = applyRateLimit(request, routeConfig.rateLimit)
  //   if (rateLimitResponse) {
  //     return rateLimitResponse
  //   }
  // }

  // Apply CSRF protection
  // if (routeConfig.csrf?.enabled) {
  //   const csrfValid = await requireCSRFToken(request)
  //   if (!csrfValid) {
  //     return NextResponse.json(
  //       {
  //         error: 'CSRF token validation failed',
  //         code: 'CSRF_INVALID'
  //       },
  //       { status: 403 }
  //     )
  //   }
  // }
  
  // Apply authentication check
  if (routeConfig.auth?.required) {
    const session = await validateSessionFromRequest(request)
    if (!session) {
      return NextResponse.json(
        { 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }
  }
  
  return null // No security violations
}

/**
 * Apply rate limiting to request
 */
function applyRateLimit(
  request: NextRequest,
  rateLimitConfig: NonNullable<SecurityConfig['rateLimit']>
): NextResponse | null {
  try {
    const ip = getClientIP(request)
    // Note: We can't validate session here in Edge Runtime, so we'll use IP-based limiting
    const endpoint = request.nextUrl.pathname

    // Generate rate limit key (IP-based for Edge Runtime)
    const identifier = generateRateLimitKey(ip, undefined, endpoint)

    // Get rate limit configuration
    const config = rateLimitConfig.customConfig ||
      RATE_LIMIT_CONFIGS[rateLimitConfig.config]

    // Check rate limit (synchronous for Edge Runtime)
    const result = edgeRateLimiter.checkRateLimit(identifier, config)
    
    if (!result.allowed) {
      // Rate limit exceeded
      const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000)
      
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter,
          limit: config.maxRequests,
          remaining: result.remaining,
          resetTime: result.resetTime
        },
        {
          status: 429,
          headers: {
            'Retry-After': retryAfter.toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': result.resetTime.toString(),
          }
        }
      )
    }
    
    // Add rate limit headers to successful responses
    const response = NextResponse.next()
    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString())
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString())
    response.headers.set('X-RateLimit-Reset', result.resetTime.toString())
    
    return null // No rate limit violation
  } catch (error) {
    console.error('Rate limiting error:', error)
    // Don't block requests if rate limiting fails
    return null
  }
}

/**
 * Get security configuration for a route
 */
function getSecurityConfigForRoute(pathname: string): SecurityConfig {
  // Check for exact matches first
  if (SECURITY_CONFIGS[pathname]) {
    return SECURITY_CONFIGS[pathname]
  }
  
  // Check for pattern matches
  for (const [pattern, config] of Object.entries(SECURITY_CONFIGS)) {
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1)
      if (pathname.startsWith(prefix)) {
        return config
      }
    }
  }
  
  // Default configuration for API routes
  if (pathname.startsWith('/api/')) {
    return {
      rateLimit: { enabled: true, config: 'api' },
      csrf: { enabled: true },
      auth: { required: true }
    }
  }
  
  // Default configuration for other routes
  return {
    rateLimit: { enabled: false, config: 'public' },
    csrf: { enabled: false },
    auth: { required: false }
  }
}

/**
 * Create security headers for responses
 */
export function createSecurityHeaders(): Record<string, string> {
  return {
    // Prevent XSS attacks
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    
    // HTTPS enforcement
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    
    // Content Security Policy
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'none'",
    ].join('; '),
    
    // Referrer policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Permissions policy
    'Permissions-Policy': [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
    ].join(', '),
  }
}