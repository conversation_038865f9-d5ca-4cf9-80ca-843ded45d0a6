// Edge Runtime compatible rate limiter using in-memory storage
// This is used in middleware to avoid Redis compatibility issues

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  totalHits: number
}

// Default configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
  },
  
  // API endpoints - moderate limits
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
  
  // Content creation - moderate limits
  content: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 requests per minute
  },
  
  // File uploads - stricter limits
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
  
  // Public endpoints - more lenient
  public: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200, // 200 requests per minute
  }
} as const

class EdgeRateLimiter {
  private memoryStore: Map<string, { count: number; resetTime: number }> = new Map()

  /**
   * Check if request is within rate limit
   */
  checkRateLimit(
    identifier: string,
    config: RateLimitConfig
  ): RateLimitResult {
    const key = `rate_limit:${identifier}`
    const now = Date.now()
    
    // Clean up expired entries periodically
    this.cleanupMemoryStore(now)
    
    const entry = this.memoryStore.get(key)
    
    if (!entry || entry.resetTime <= now) {
      // First request or window expired
      this.memoryStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs,
        totalHits: 1
      }
    }
    
    const newCount = entry.count + 1
    const allowed = newCount <= config.maxRequests
    
    if (allowed) {
      entry.count = newCount
    }
    
    return {
      allowed,
      remaining: Math.max(0, config.maxRequests - newCount),
      resetTime: entry.resetTime,
      totalHits: allowed ? newCount : entry.count
    }
  }

  /**
   * Clean up expired entries from memory store
   */
  private cleanupMemoryStore(now: number) {
    for (const [key, entry] of this.memoryStore.entries()) {
      if (entry.resetTime <= now) {
        this.memoryStore.delete(key)
      }
    }
  }

  /**
   * Reset rate limit for a specific identifier
   */
  resetRateLimit(identifier: string): void {
    const key = `rate_limit:${identifier}`
    this.memoryStore.delete(key)
  }

  /**
   * Get current rate limit status without incrementing
   */
  getRateLimitStatus(
    identifier: string,
    config: RateLimitConfig
  ): Omit<RateLimitResult, 'allowed'> {
    const key = `rate_limit:${identifier}`
    const now = Date.now()
    
    const entry = this.memoryStore.get(key)
    if (!entry || entry.resetTime <= now) {
      return {
        remaining: config.maxRequests,
        resetTime: now + config.windowMs,
        totalHits: 0
      }
    }
    
    return {
      remaining: Math.max(0, config.maxRequests - entry.count),
      resetTime: entry.resetTime,
      totalHits: entry.count
    }
  }
}

// Singleton instance
export const edgeRateLimiter = new EdgeRateLimiter()

/**
 * Generate rate limit key based on IP and user ID
 */
export function generateRateLimitKey(
  ip: string,
  userId?: string,
  endpoint?: string
): string {
  const parts = [ip]
  if (userId) parts.push(userId)
  if (endpoint) parts.push(endpoint)
  return parts.join(':')
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: Request): string {
  const headers = request.headers
  
  // Check various headers for the real IP
  const forwardedFor = headers.get('x-forwarded-for')
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim()
  }
  
  const realIP = headers.get('x-real-ip')
  if (realIP) {
    return realIP
  }
  
  const cfConnectingIP = headers.get('cf-connecting-ip')
  if (cfConnectingIP) {
    return cfConnectingIP
  }
  
  // Fallback to a default IP for development
  return '127.0.0.1'
}
