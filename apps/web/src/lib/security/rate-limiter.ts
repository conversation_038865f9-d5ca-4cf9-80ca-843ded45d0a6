// Simple in-memory rate limiter for Edge Runtime compatibility
// Redis will be used in API routes, but middleware uses memory store

// Rate limiter configuration
interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (identifier: string) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

// Rate limit result
interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  totalHits: number
}

// Default configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
  },
  
  // API endpoints - moderate limits
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
  
  // Content creation - moderate limits
  content: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 requests per minute
  },
  
  // File uploads - stricter limits
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
  
  // Public endpoints - more lenient
  public: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200, // 200 requests per minute
  }
} as const

class RateLimiter {
  private redis: Redis | null = null
  private memoryStore: Map<string, { count: number; resetTime: number }> = new Map()
  private useRedis: boolean = false

  constructor() {
    this.initializeRedis()
  }

  private async initializeRedis() {
    try {
      // Skip Redis in Edge Runtime or if Redis is not available
      if (!Redis || typeof process === 'undefined' || !process.versions?.node) {
        console.log('Rate limiter: Using in-memory store (Edge Runtime or Redis not available)')
        return
      }

      // Try to connect to Redis if URL is provided
      const redisUrl = process.env.REDIS_URL
      if (redisUrl) {
        this.redis = new Redis(redisUrl, {
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
        })

        await this.redis.ping()
        this.useRedis = true
        console.log('Rate limiter: Connected to Redis')
      } else {
        console.log('Rate limiter: Using in-memory store (Redis URL not provided)')
      }
    } catch (error) {
      console.warn('Rate limiter: Failed to connect to Redis, falling back to memory store:', error)
      this.redis = null
      this.useRedis = false
    }
  }

  /**
   * Check if request is within rate limit
   */
  async checkRateLimit(
    identifier: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`
    const now = Date.now()
    const windowStart = now - config.windowMs
    
    if (this.useRedis && this.redis) {
      return this.checkRateLimitRedis(key, config, now, windowStart)
    } else {
      return this.checkRateLimitMemory(key, config, now, windowStart)
    }
  }

  /**
   * Redis-based rate limiting using sliding window
   */
  private async checkRateLimitRedis(
    key: string,
    config: RateLimitConfig,
    now: number,
    windowStart: number
  ): Promise<RateLimitResult> {
    const redis = this.redis!
    
    // Use Redis pipeline for atomic operations
    const pipeline = redis.pipeline()
    
    // Remove expired entries
    pipeline.zremrangebyscore(key, 0, windowStart)
    
    // Count current requests in window
    pipeline.zcard(key)
    
    // Add current request
    pipeline.zadd(key, now, `${now}-${Math.random()}`)
    
    // Set expiration
    pipeline.expire(key, Math.ceil(config.windowMs / 1000))
    
    const results = await pipeline.exec()
    
    if (!results) {
      throw new Error('Redis pipeline execution failed')
    }
    
    const currentCount = (results[1][1] as number) + 1 // +1 for the request we just added
    const allowed = currentCount <= config.maxRequests
    
    // If not allowed, remove the request we just added
    if (!allowed) {
      await redis.zpopmax(key)
    }
    
    return {
      allowed,
      remaining: Math.max(0, config.maxRequests - currentCount),
      resetTime: now + config.windowMs,
      totalHits: currentCount
    }
  }

  /**
   * Memory-based rate limiting (fallback)
   */
  private checkRateLimitMemory(
    key: string,
    config: RateLimitConfig,
    now: number,
    windowStart: number
  ): RateLimitResult {
    // Clean up expired entries periodically
    this.cleanupMemoryStore(now)
    
    const entry = this.memoryStore.get(key)
    
    if (!entry || entry.resetTime <= now) {
      // First request or window expired
      this.memoryStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs,
        totalHits: 1
      }
    }
    
    const newCount = entry.count + 1
    const allowed = newCount <= config.maxRequests
    
    if (allowed) {
      entry.count = newCount
    }
    
    return {
      allowed,
      remaining: Math.max(0, config.maxRequests - newCount),
      resetTime: entry.resetTime,
      totalHits: allowed ? newCount : entry.count
    }
  }

  /**
   * Clean up expired entries from memory store
   */
  private cleanupMemoryStore(now: number) {
    for (const [key, entry] of this.memoryStore.entries()) {
      if (entry.resetTime <= now) {
        this.memoryStore.delete(key)
      }
    }
  }

  /**
   * Reset rate limit for a specific identifier
   */
  async resetRateLimit(identifier: string): Promise<void> {
    const key = `rate_limit:${identifier}`
    
    if (this.useRedis && this.redis) {
      await this.redis.del(key)
    } else {
      this.memoryStore.delete(key)
    }
  }

  /**
   * Get current rate limit status without incrementing
   */
  async getRateLimitStatus(
    identifier: string,
    config: RateLimitConfig
  ): Promise<Omit<RateLimitResult, 'allowed'>> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`
    const now = Date.now()
    
    if (this.useRedis && this.redis) {
      const count = await this.redis.zcard(key)
      return {
        remaining: Math.max(0, config.maxRequests - count),
        resetTime: now + config.windowMs,
        totalHits: count
      }
    } else {
      const entry = this.memoryStore.get(key)
      if (!entry || entry.resetTime <= now) {
        return {
          remaining: config.maxRequests,
          resetTime: now + config.windowMs,
          totalHits: 0
        }
      }
      
      return {
        remaining: Math.max(0, config.maxRequests - entry.count),
        resetTime: entry.resetTime,
        totalHits: entry.count
      }
    }
  }

  /**
   * Close Redis connection
   */
  async close(): Promise<void> {
    if (this.redis) {
      await this.redis.quit()
    }
  }
}

// Singleton instance
export const rateLimiter = new RateLimiter()

/**
 * Generate rate limit key based on IP and user ID
 */
export function generateRateLimitKey(
  ip: string,
  userId?: string,
  endpoint?: string
): string {
  const parts = [ip]
  if (userId) parts.push(userId)
  if (endpoint) parts.push(endpoint)
  return parts.join(':')
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: Request): string {
  const headers = request.headers
  
  // Check various headers for the real IP
  const forwardedFor = headers.get('x-forwarded-for')
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim()
  }
  
  const realIP = headers.get('x-real-ip')
  if (realIP) {
    return realIP
  }
  
  const cfConnectingIP = headers.get('cf-connecting-ip')
  if (cfConnectingIP) {
    return cfConnectingIP
  }
  
  // Fallback to a default IP for development
  return '127.0.0.1'
}