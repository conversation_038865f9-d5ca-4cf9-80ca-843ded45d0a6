import { randomBytes, createHash, timingSafeEqual } from 'crypto'
import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32
const CSRF_SECRET_LENGTH = 32
const CSRF_TOKEN_LIFETIME = 60 * 60 * 1000 // 1 hour in milliseconds

interface CSRFTokenData {
  token: string
  secret: string
  timestamp: number
}

/**
 * Generate a cryptographically secure random token
 */
function generateSecureToken(length: number): string {
  return randomBytes(length).toString('hex')
}

/**
 * Create HMAC hash of token with secret
 */
function createTokenHash(token: string, secret: string): string {
  return createHash('sha256')
    .update(token + secret)
    .digest('hex')
}

/**
 * Generate a new CSRF token pair (token + secret)
 */
export function generateCSRFToken(): CSRFTokenData {
  const token = generateSecureToken(CSRF_TOKEN_LENGTH)
  const secret = generateSecureToken(CSRF_SECRET_LENGTH)
  const timestamp = Date.now()
  
  return {
    token,
    secret,
    timestamp
  }
}

/**
 * Store CSRF token in secure cookie
 */
export async function setCSRFCookie(tokenData: CSRFTokenData): Promise<void> {
  const cookieStore = await cookies()
  
  // Store the secret in a secure HTTP-only cookie
  cookieStore.set('csrf_secret', tokenData.secret, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_LIFETIME / 1000,
    path: '/',
  })
  
  // Store timestamp for validation
  cookieStore.set('csrf_timestamp', tokenData.timestamp.toString(), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_LIFETIME / 1000,
    path: '/',
  })
}

/**
 * Get CSRF token for client-side use
 */
export async function getCSRFToken(): Promise<string> {
  const tokenData = generateCSRFToken()
  await setCSRFCookie(tokenData)
  return tokenData.token
}

/**
 * Validate CSRF token from request
 */
export async function validateCSRFToken(
  request: NextRequest,
  submittedToken?: string
): Promise<boolean> {
  try {
    // Get token from header or body
    const token = submittedToken || 
      request.headers.get('x-csrf-token') ||
      request.headers.get('csrf-token')
    
    if (!token) {
      return false
    }
    
    // Get secret from cookie
    const secret = request.cookies.get('csrf_secret')?.value
    const timestampStr = request.cookies.get('csrf_timestamp')?.value
    
    if (!secret || !timestampStr) {
      return false
    }
    
    // Check token age
    const timestamp = parseInt(timestampStr, 10)
    const now = Date.now()
    
    if (now - timestamp > CSRF_TOKEN_LIFETIME) {
      return false
    }
    
    // Validate token using timing-safe comparison
    const expectedHash = createTokenHash(token, secret)
    const actualHash = createTokenHash(token, secret)
    
    // Use timing-safe comparison to prevent timing attacks
    const expectedBuffer = Buffer.from(expectedHash, 'hex')
    const actualBuffer = Buffer.from(actualHash, 'hex')
    
    if (expectedBuffer.length !== actualBuffer.length) {
      return false
    }
    
    return timingSafeEqual(expectedBuffer, actualBuffer)
  } catch (error) {
    console.error('CSRF token validation error:', error)
    return false
  }
}

/**
 * Clear CSRF cookies
 */
export async function clearCSRFCookies(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete('csrf_secret')
  cookieStore.delete('csrf_timestamp')
}

/**
 * Middleware helper to validate CSRF for state-changing requests
 */
export async function requireCSRFToken(request: NextRequest): Promise<boolean> {
  const method = request.method.toUpperCase()
  
  // Only validate CSRF for state-changing methods
  if (!['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    return true
  }
  
  // Skip CSRF validation for OAuth callbacks and certain auth endpoints
  const pathname = request.nextUrl.pathname
  if (
    pathname.includes('/api/auth/') &&
    (pathname.includes('/callback') || pathname.includes('/logout'))
  ) {
    return true
  }
  
  return await validateCSRFToken(request)
}

/**
 * Generate CSRF token for forms
 */
export async function getCSRFTokenForForm(): Promise<{
  token: string
  fieldName: string
  headerName: string
}> {
  const token = await getCSRFToken()
  
  return {
    token,
    fieldName: 'csrf_token',
    headerName: 'X-CSRF-Token'
  }
}

/**
 * CSRF protection configuration
 */
export const CSRF_CONFIG = {
  tokenLength: CSRF_TOKEN_LENGTH,
  secretLength: CSRF_SECRET_LENGTH,
  tokenLifetime: CSRF_TOKEN_LIFETIME,
  cookieName: 'csrf_secret',
  headerName: 'X-CSRF-Token',
  fieldName: 'csrf_token',
} as const