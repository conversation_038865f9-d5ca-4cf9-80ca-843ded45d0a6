import { NextRequest } from "next/server"
import crypto from "crypto"

/**
 * OAuth Security Utilities
 * Provides security functions for OAuth 2.0 flows including PKCE, state validation, and token encryption
 */

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

/**
 * Generate cryptographically secure random string
 */
export function generateSecureRandom(length: number = 32): string {
  return crypto.randomBytes(length).toString("base64url")
}

/**
 * Generate PKCE code verifier and challenge
 */
export function generatePKCE() {
  const codeVerifier = generateSecureRandom(32)
  const codeChallenge = crypto
    .createHash("sha256")
    .update(codeVerifier)
    .digest("base64url")

  return {
    codeVerifier,
    codeChallenge,
    codeChallengeMethod: "S256",
  }
}

/**
 * Generate OAuth state parameter with timestamp and nonce
 */
export function generateOAuthState(userId?: string): string {
  const timestamp = Date.now()
  const nonce = generateSecureRandom(16)
  const data = { timestamp, nonce, userId }
  
  return Buffer.from(JSON.stringify(data)).toString("base64url")
}

/**
 * Validate OAuth state parameter
 */
export function validateOAuthState(
  state: string,
  maxAge: number = 10 * 60 * 1000 // 10 minutes
): { valid: boolean; userId?: string; error?: string } {
  try {
    const decoded = JSON.parse(Buffer.from(state, "base64url").toString())
    const { timestamp, nonce, userId } = decoded

    if (!timestamp || !nonce) {
      return { valid: false, error: "Invalid state format" }
    }

    const age = Date.now() - timestamp
    if (age > maxAge) {
      return { valid: false, error: "State expired" }
    }

    return { valid: true, userId }
  } catch (error) {
    return { valid: false, error: "Invalid state encoding" }
  }
}

/**
 * Encrypt sensitive token data
 */
export function encryptToken(token: string): string {
  const algorithm = "aes-256-gcm"
  const key = crypto.scryptSync(process.env.NEXTAUTH_SECRET!, "salt", 32)
  const iv = crypto.randomBytes(16)
  
  const cipher = crypto.createCipher(algorithm, key)
  cipher.setAAD(Buffer.from("oauth-token"))
  
  let encrypted = cipher.update(token, "utf8", "hex")
  encrypted += cipher.final("hex")
  
  const authTag = cipher.getAuthTag()
  
  return `${iv.toString("hex")}:${authTag.toString("hex")}:${encrypted}`
}

/**
 * Decrypt sensitive token data
 */
export function decryptToken(encryptedToken: string): string {
  const algorithm = "aes-256-gcm"
  const key = crypto.scryptSync(process.env.NEXTAUTH_SECRET!, "salt", 32)
  
  const [ivHex, authTagHex, encrypted] = encryptedToken.split(":")
  const iv = Buffer.from(ivHex, "hex")
  const authTag = Buffer.from(authTagHex, "hex")
  
  const decipher = crypto.createDecipher(algorithm, key)
  decipher.setAAD(Buffer.from("oauth-token"))
  decipher.setAuthTag(authTag)
  
  let decrypted = decipher.update(encrypted, "hex", "utf8")
  decrypted += decipher.final("utf8")
  
  return decrypted
}

/**
 * Rate limiting for OAuth endpoints
 */
export function checkRateLimit(
  identifier: string,
  maxRequests: number = 5,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const windowStart = now - windowMs
  
  // Clean up expired entries
  for (const [key, value] of rateLimitStore.entries()) {
    if (value.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
  
  const current = rateLimitStore.get(identifier)
  
  if (!current || current.resetTime < now) {
    // First request or window expired
    const resetTime = now + windowMs
    rateLimitStore.set(identifier, { count: 1, resetTime })
    return { allowed: true, remaining: maxRequests - 1, resetTime }
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime }
  }
  
  // Increment count
  current.count++
  rateLimitStore.set(identifier, current)
  
  return { 
    allowed: true, 
    remaining: maxRequests - current.count, 
    resetTime: current.resetTime 
  }
}

/**
 * Validate OAuth callback parameters
 */
export function validateOAuthCallback(params: {
  code?: string
  state?: string
  error?: string
  error_description?: string
}): { valid: boolean; error?: string } {
  const { code, state, error, error_description } = params

  // Check for OAuth errors
  if (error) {
    return { 
      valid: false, 
      error: error_description || `OAuth error: ${error}` 
    }
  }

  // Validate required parameters
  if (!code) {
    return { valid: false, error: "Missing authorization code" }
  }

  if (!state) {
    return { valid: false, error: "Missing state parameter" }
  }

  return { valid: true }
}

/**
 * Get client IP address for rate limiting
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for")
  const realIP = request.headers.get("x-real-ip")
  const clientIP = request.headers.get("x-client-ip")
  
  if (forwarded) {
    return forwarded.split(",")[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  if (clientIP) {
    return clientIP
  }
  
  return "unknown"
}

/**
 * Validate redirect URI against allowed patterns
 */
export function validateRedirectURI(
  redirectURI: string,
  allowedPatterns: string[] = []
): boolean {
  try {
    const url = new URL(redirectURI)
    
    // Default allowed patterns
    const defaultPatterns = [
      `${process.env.NEXTAUTH_URL}/api/auth/callback`,
      `${process.env.FRONTEND_URL}/api/auth/callback`,
      "http://localhost:3000/api/auth/callback", // Development
    ]
    
    const patterns = [...defaultPatterns, ...allowedPatterns]
    
    return patterns.some(pattern => {
      try {
        const patternUrl = new URL(pattern)
        return (
          url.protocol === patternUrl.protocol &&
          url.hostname === patternUrl.hostname &&
          url.port === patternUrl.port &&
          url.pathname.startsWith(patternUrl.pathname)
        )
      } catch {
        return false
      }
    })
  } catch {
    return false
  }
}

/**
 * Security headers for OAuth responses
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Content-Security-Policy": "default-src 'self'",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
  }
}

/**
 * Log security events
 */
export function logSecurityEvent(
  event: string,
  details: Record<string, any>,
  level: "info" | "warn" | "error" = "info"
) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    level,
    details,
  }
  
  // In production, send to your logging service
  console.log(`[OAUTH-SECURITY] ${level.toUpperCase()}:`, logEntry)
}
