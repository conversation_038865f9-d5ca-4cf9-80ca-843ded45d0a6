import jwt from 'jsonwebtoken'
import { randomUUID } from 'crypto'
import { prisma } from '@/lib/database'

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = '24h'
const REFRESH_TOKEN_EXPIRES_IN = '7d'

export interface JWTPayload {
  sub: string // User ID
  email: string
  name?: string
  avatar?: string
  jti: string // JWT ID for revocation
  iat: number
  exp: number
  type: 'access' | 'refresh'
}

export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresAt: Date
}

/**
 * Generate a new JWT token pair (access + refresh)
 */
export async function generateTokenPair(
  userId: string,
  email: string,
  name?: string,
  avatar?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<TokenPair> {
  const jwtId = randomUUID()
  const now = Math.floor(Date.now() / 1000)
  
  // Calculate expiration times
  const accessTokenExp = now + (24 * 60 * 60) // 24 hours
  const refreshTokenExp = now + (7 * 24 * 60 * 60) // 7 days
  
  // Create access token payload
  const accessPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
    sub: userId,
    email,
    name,
    avatar,
    jti: jwtId,
    type: 'access'
  }
  
  // Create refresh token payload
  const refreshPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
    sub: userId,
    email,
    name,
    avatar,
    jti: jwtId + '_refresh',
    type: 'refresh'
  }
  
  // Generate tokens
  const accessToken = jwt.sign(accessPayload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'xtask',
    audience: 'xtask-users'
  })
  
  const refreshToken = jwt.sign(refreshPayload, JWT_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'xtask',
    audience: 'xtask-users'
  })
  
  // Store session in database
  const expiresAt = new Date(accessTokenExp * 1000)
  await prisma.session.create({
    data: {
      jwtId,
      userId,
      ipAddress,
      userAgent,
      expiresAt,
      isRevoked: false
    }
  })
  
  return {
    accessToken,
    refreshToken,
    expiresAt
  }
}

/**
 * Verify and decode a JWT token
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    // Verify JWT signature and decode
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'xtask',
      audience: 'xtask-users'
    }) as JWTPayload
    
    // For access tokens, check if session is revoked
    if (decoded.type === 'access') {
      const session = await prisma.session.findUnique({
        where: { jwtId: decoded.jti }
      })
      
      if (!session || session.isRevoked || session.expiresAt < new Date()) {
        return null
      }
    }
    
    return decoded
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

/**
 * Refresh an access token using a refresh token
 */
export async function refreshAccessToken(
  refreshToken: string,
  ipAddress?: string,
  userAgent?: string
): Promise<TokenPair | null> {
  try {
    const decoded = jwt.verify(refreshToken, JWT_SECRET, {
      issuer: 'xtask',
      audience: 'xtask-users'
    }) as JWTPayload
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type')
    }
    
    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: decoded.sub },
      select: { id: true, email: true, name: true, avatar: true }
    })
    
    if (!user) {
      throw new Error('User not found')
    }
    
    // Generate new token pair
    return await generateTokenPair(
      user.id,
      user.email,
      user.name,
      user.avatar,
      ipAddress,
      userAgent
    )
  } catch (error) {
    console.error('Token refresh failed:', error)
    return null
  }
}

/**
 * Revoke a session by JWT ID
 */
export async function revokeSession(jwtId: string): Promise<boolean> {
  try {
    await prisma.session.update({
      where: { jwtId },
      data: { isRevoked: true }
    })
    return true
  } catch (error) {
    console.error('Session revocation failed:', error)
    return false
  }
}

/**
 * Revoke all sessions for a user
 */
export async function revokeAllUserSessions(userId: string): Promise<boolean> {
  try {
    await prisma.session.updateMany({
      where: { userId },
      data: { isRevoked: true }
    })
    return true
  } catch (error) {
    console.error('User session revocation failed:', error)
    return false
  }
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const result = await prisma.session.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { isRevoked: true }
        ]
      }
    })
    return result.count
  } catch (error) {
    console.error('Session cleanup failed:', error)
    return 0
  }
}

/**
 * Get active sessions for a user
 */
export async function getUserSessions(userId: string) {
  return await prisma.session.findMany({
    where: {
      userId,
      isRevoked: false,
      expiresAt: { gt: new Date() }
    },
    orderBy: { createdAt: 'desc' }
  })
}