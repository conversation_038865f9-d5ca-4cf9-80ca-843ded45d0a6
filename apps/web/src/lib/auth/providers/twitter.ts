import { generateState, generateCodeVerifier, generateCodeChallenge } from '../crypto'

export interface TwitterTokenResponse {
  access_token: string
  refresh_token?: string
  expires_in: number
  token_type: string
  scope: string
}

export interface TwitterUserInfo {
  id: string
  username: string
  name: string
  profile_image_url?: string
  verified: boolean
  email?: string
}

export interface TwitterPKCEData {
  codeVerifier: string
  codeChallenge: string
  state: string
}

export class TwitterOAuthProvider {
  private clientId: string
  private clientSecret: string
  private redirectUri: string

  constructor() {
    this.clientId = process.env.TWITTER_CLIENT_ID!
    this.clientSecret = process.env.TWITTER_CLIENT_SECRET!
    this.redirectUri = `${process.env.FRONTEND_URL}/api/auth/twitter/callback`

    if (!this.clientId || !this.clientSecret) {
      throw new Error('Twitter OAuth credentials not configured')
    }
  }

  async generateAuthUrl(): Promise<{ url: string; pkceData: TwitterPKCEData }> {
    const state = generateState()
    const codeVerifier = generateCodeVerifier()
    const codeChallenge = await generateCodeChallenge(codeVerifier)
    
    const params = new URLSearchParams({
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      response_type: 'code',
      scope: 'tweet.read tweet.write users.read offline.access',
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    })

    const url = `https://twitter.com/i/oauth2/authorize?${params.toString()}`
    
    return {
      url,
      pkceData: {
        codeVerifier,
        codeChallenge,
        state,
      },
    }
  }

  async exchangeCodeForTokens(
    code: string,
    codeVerifier: string
  ): Promise<TwitterTokenResponse> {
    const response = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64')}`,
      },
      body: new URLSearchParams({
        code,
        grant_type: 'authorization_code',
        redirect_uri: this.redirectUri,
        code_verifier: codeVerifier,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Twitter token exchange failed: ${error}`)
    }

    return response.json()
  }

  async getUserInfo(accessToken: string): Promise<TwitterUserInfo> {
    const response = await fetch(
      'https://api.twitter.com/2/users/me?user.fields=id,username,name,profile_image_url,verified',
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Twitter user info fetch failed: ${error}`)
    }

    const data = await response.json()
    return data.data
  }

  async refreshAccessToken(refreshToken: string): Promise<TwitterTokenResponse> {
    const response = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64')}`,
      },
      body: new URLSearchParams({
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Twitter token refresh failed: ${error}`)
    }

    return response.json()
  }
}