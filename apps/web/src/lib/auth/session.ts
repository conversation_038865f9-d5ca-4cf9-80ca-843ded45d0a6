import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/database'
import { generateTokenPair, verifyToken, revokeSession } from './jwt'

export interface SessionData {
  userId: string
  email: string
  name?: string
  avatar?: string
  jwtId: string
}

/**
 * Create a new session with JWT tokens
 */
export async function createSession(
  userData: {
    userId: string
    email: string
    name?: string
    avatar?: string
  },
  request?: NextRequest
): Promise<string> {
  // Extract client information
  let ipAddress = 'unknown'
  let userAgent = 'unknown'

  if (request) {
    try {
      ipAddress = request.ip ||
        request.headers?.get('x-forwarded-for')?.split(',')[0] ||
        request.headers?.get('x-real-ip') ||
        'unknown'

      userAgent = request.headers?.get('user-agent') || 'unknown'
    } catch (error) {
      console.warn('Failed to extract client information:', error)
    }
  }
  
  // Generate JWT token pair
  const { accessToken, refreshToken, expiresAt } = await generateTokenPair(
    userData.userId,
    userData.email,
    userData.name,
    userData.avatar,
    ipAddress,
    userAgent
  )
  
  // Set secure HTTP-only cookies
  const cookieStore = await cookies()
  
  // Access token cookie (shorter expiry)
  cookieStore.set('access_token', accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24, // 24 hours
    path: '/',
  })
  
  // Refresh token cookie (longer expiry)
  cookieStore.set('refresh_token', refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: '/',
  })
  
  return accessToken
}

/**
 * Get current session from cookies
 */
export async function getSession(): Promise<SessionData | null> {
  try {
    const cookieStore = await cookies()
    const accessToken = cookieStore.get('access_token')?.value
    
    if (!accessToken) {
      return null
    }
    
    // Verify and decode JWT
    const payload = await verifyToken(accessToken)
    if (!payload || payload.type !== 'access') {
      return null
    }
    
    // Verify user still exists in database
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: { id: true, email: true, name: true, avatar: true }
    })
    
    if (!user) {
      await destroySession()
      return null
    }
    
    return {
      userId: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      jwtId: payload.jti
    }
  } catch (error) {
    console.error('Session validation error:', error)
    await destroySession()
    return null
  }
}

/**
 * Destroy current session
 */
export async function destroySession(): Promise<void> {
  try {
    const cookieStore = await cookies()
    const accessToken = cookieStore.get('access_token')?.value
    
    // Revoke session in database if token exists
    if (accessToken) {
      const payload = await verifyToken(accessToken)
      if (payload?.jti) {
        await revokeSession(payload.jti)
      }
    }
    
    // Clear cookies
    cookieStore.delete('access_token')
    cookieStore.delete('refresh_token')
  } catch (error) {
    console.error('Session destruction error:', error)
    // Still clear cookies even if database operation fails
    const cookieStore = await cookies()
    cookieStore.delete('access_token')
    cookieStore.delete('refresh_token')
  }
}

/**
 * Require authentication - throws if not authenticated
 */
export async function requireAuth(): Promise<SessionData> {
  const session = await getSession()
  if (!session) {
    throw new Error('Authentication required')
  }
  return session
}

/**
 * Validate session from request (for API middleware)
 */
export async function validateSessionFromRequest(request: NextRequest): Promise<SessionData | null> {
  try {
    const accessToken = request.cookies.get('access_token')?.value
    
    if (!accessToken) {
      return null
    }
    
    // Verify and decode JWT
    const payload = await verifyToken(accessToken)
    if (!payload || payload.type !== 'access') {
      return null
    }
    
    // Verify user still exists
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: { id: true, email: true, name: true, avatar: true }
    })
    
    if (!user) {
      return null
    }
    
    return {
      userId: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      jwtId: payload.jti
    }
  } catch (error) {
    console.error('Request session validation error:', error)
    return null
  }
}

/**
 * Refresh session tokens
 */
export async function refreshSession(request?: NextRequest): Promise<boolean> {
  try {
    const cookieStore = await cookies()
    const refreshToken = cookieStore.get('refresh_token')?.value
    
    if (!refreshToken) {
      return false
    }
    
    // Extract client information
    let ipAddress = 'unknown'
    let userAgent = 'unknown'

    if (request) {
      try {
        ipAddress = request.ip ||
          request.headers?.get('x-forwarded-for')?.split(',')[0] ||
          request.headers?.get('x-real-ip') ||
          'unknown'

        userAgent = request.headers?.get('user-agent') || 'unknown'
      } catch (error) {
        console.warn('Failed to extract client information:', error)
      }
    }
    
    // Refresh tokens
    const { refreshAccessToken } = await import('./jwt')
    const newTokens = await refreshAccessToken(refreshToken, ipAddress, userAgent)
    
    if (!newTokens) {
      return false
    }
    
    // Update cookies with new tokens
    cookieStore.set('access_token', newTokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24, // 24 hours
      path: '/',
    })
    
    cookieStore.set('refresh_token', newTokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/',
    })
    
    return true
  } catch (error) {
    console.error('Session refresh error:', error)
    return false
  }
}