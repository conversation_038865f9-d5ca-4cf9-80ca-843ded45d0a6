import { cleanupExpiredSessions } from './jwt'

/**
 * Cleanup expired sessions - should be run periodically
 */
export async function runSessionCleanup(): Promise<void> {
  try {
    const deletedCount = await cleanupExpiredSessions()
    console.log(`Cleaned up ${deletedCount} expired sessions`)
  } catch (error) {
    console.error('Session cleanup failed:', error)
  }
}

/**
 * Start periodic session cleanup (every hour)
 */
export function startSessionCleanup(): void {
  // Run cleanup immediately
  runSessionCleanup()
  
  // Then run every hour
  setInterval(runSessionCleanup, 60 * 60 * 1000)
}