import { z } from 'zod'

export const scheduleSchema = z.object({
  draftId: z.string().min(1, 'Draft ID is required'),
  scheduledFor: z.string().datetime('Invalid date format'),
  twitterAccountId: z.string().min(1, 'Twitter account is required'),
})

export const rescheduleSchema = z.object({
  scheduledFor: z.string().datetime('Invalid date format').optional(),
  twitterAccountId: z.string().optional(),
})

export type ScheduleData = z.infer<typeof scheduleSchema>
export type RescheduleData = z.infer<typeof rescheduleSchema>