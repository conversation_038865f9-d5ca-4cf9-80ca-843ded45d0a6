import { z } from 'zod';

export const createDraftSchema = z.object({
  content: z.string().min(1, 'Content is required').max(280, 'Content too long'),
  mediaUrls: z.array(z.string().url()).max(4, 'Too many media files').optional(),
  agentId: z.string().min(1, 'Agent is required'),
  threadId: z.string().optional(),
  threadOrder: z.number().min(0).optional(),
  isThreadStart: z.boolean().default(false),
});

export const updateDraftSchema = z.object({
  content: z.string().min(1, 'Content is required').max(280, 'Content too long').optional(),
  mediaUrls: z.array(z.string().url()).max(4, 'Too many media files').optional(),
  threadOrder: z.number().min(0).optional(),
});

export const createThreadSchema = z.object({
  tweets: z.array(z.object({
    content: z.string().min(1, 'Content is required').max(280, 'Content too long'),
    mediaUrls: z.array(z.string().url()).max(4, 'Too many media files').optional(),
  })).min(2, 'Thread must have at least 2 tweets').max(25, 'Thread too long'),
  agentId: z.string().min(1, 'Agent is required'),
});

export const draftQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(50).default(10),
  search: z.string().optional(),
  agentId: z.string().optional(),
  threadId: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'threadOrder']).default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type CreateDraftData = z.infer<typeof createDraftSchema>;
export type UpdateDraftData = z.infer<typeof updateDraftSchema>;
export type CreateThreadData = z.infer<typeof createThreadSchema>;
export type DraftQueryParams = z.infer<typeof draftQuerySchema>;