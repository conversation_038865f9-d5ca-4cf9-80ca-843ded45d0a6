import { z } from 'zod'

export const tweetSchema = z.object({
  text: z.string().min(1, 'Tweet text is required').max(280, 'Tweet too long'),
  mediaUrls: z.array(z.string().url()).max(4, 'Too many media files').optional(),
  replyToTweetId: z.string().optional(),
  quoteTweetId: z.string().optional(),
})

export const threadSchema = z.object({
  tweets: z.array(z.object({
    text: z.string().min(1, 'Tweet text is required').max(280, 'Tweet too long'),
    mediaUrls: z.array(z.string().url()).max(4, 'Too many media files').optional(),
  })).min(2, 'Thread must have at least 2 tweets').max(25, 'Thread too long'),
})

export const mediaUploadSchema = z.object({
  file: z.instanceof(File).optional(),
  mediaUrl: z.string().url().optional(),
}).refine(data => data.file || data.mediaUrl, {
  message: 'Either file or mediaUrl is required',
})

export type TweetData = z.infer<typeof tweetSchema>
export type ThreadData = z.infer<typeof threadSchema>
export type MediaUploadData = z.infer<typeof mediaUploadSchema>