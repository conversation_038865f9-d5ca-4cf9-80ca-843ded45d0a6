import { z } from 'zod'

export const updateUserProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  email: z.string().email('Invalid email format').optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  preferences: z.object({
    timezone: z.string().optional(),
    notifications: z.object({
      email: z.boolean().default(true),
      push: z.boolean().default(true),
      marketing: z.boolean().default(false),
    }).optional(),
    theme: z.enum(['light', 'dark', 'system']).default('system').optional(),
    language: z.string().default('en').optional(),
  }).optional(),
})

export const userPreferencesSchema = z.object({
  timezone: z.string().optional(),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    marketing: z.boolean().default(false),
  }).optional(),
  theme: z.enum(['light', 'dark', 'system']).default('system').optional(),
  language: z.string().default('en').optional(),
})

export const connectedAccountSchema = z.object({
  id: z.string(),
  provider: z.enum(['twitter', 'google']),
  providerAccountId: z.string(),
  username: z.string().optional(),
  displayName: z.string().optional(),
  profileImageUrl: z.string().url().optional(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export type UpdateUserProfileData = z.infer<typeof updateUserProfileSchema>
export type UserPreferences = z.infer<typeof userPreferencesSchema>
export type ConnectedAccount = z.infer<typeof connectedAccountSchema>
