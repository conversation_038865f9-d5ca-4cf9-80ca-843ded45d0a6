import { <PERSON><PERSON><PERSON>, TwitterA<PERSON>ReadWrite } from 'twitter-api-v2'
import { decryptToken } from '@/lib/auth/crypto'

export interface TwitterCredentials {
  accessToken: string
  refreshToken?: string
  tokenSecret?: string
}

export interface TweetData {
  text: string
  mediaIds?: string[]
  replyToTweetId?: string
  quoteTweetId?: string
}

export interface MediaUploadResult {
  mediaId: string
  mediaKey: string
  size: number
  expiresAfterSecs: number
}

export interface TweetResult {
  id: string
  text: string
  createdAt: string
  publicMetrics?: {
    retweetCount: number
    likeCount: number
    replyCount: number
    quoteCount: number
  }
}

export class TwitterClientV2 {
  private client: TwitterApiReadWrite

  constructor(credentials: TwitterCredentials) {
    // Initialize with OAuth 2.0 Bearer Token for app-only auth
    const bearerToken = process.env.TWITTER_BEARER_TOKEN
    
    if (!bearerToken) {
      throw new Error('Twitter Bearer Token is not configured')
    }

    // Create client with user context using OAuth 2.0
    this.client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
    }).withOAuth2({
      accessToken: credentials.accessToken,
      refreshToken: credentials.refreshToken,
    }).readWrite
  }

  /**
   * Post a single tweet
   */
  async postTweet(tweetData: TweetData): Promise<TweetResult> {
    try {
      const tweetPayload: any = {
        text: tweetData.text,
      }

      // Add media if provided
      if (tweetData.mediaIds && tweetData.mediaIds.length > 0) {
        tweetPayload.media = {
          media_ids: tweetData.mediaIds,
        }
      }

      // Add reply context if provided
      if (tweetData.replyToTweetId) {
        tweetPayload.reply = {
          in_reply_to_tweet_id: tweetData.replyToTweetId,
        }
      }

      // Add quote tweet if provided
      if (tweetData.quoteTweetId) {
        tweetPayload.quote_tweet_id = tweetData.quoteTweetId
      }

      const response = await this.client.v2.tweet(tweetPayload)

      return {
        id: response.data.id,
        text: response.data.text,
        createdAt: new Date().toISOString(),
      }
    } catch (error) {
      console.error('Twitter API v2 tweet error:', error)
      throw this.handleTwitterError(error)
    }
  }

  /**
   * Post a thread of tweets
   */
  async postThread(tweets: Array<{ text: string; mediaIds?: string[] }>): Promise<TweetResult[]> {
    const results: TweetResult[] = []
    let replyToId: string | undefined

    for (let i = 0; i < tweets.length; i++) {
      try {
        const tweet = tweets[i]
        const tweetData: TweetData = {
          text: tweet.text,
          mediaIds: tweet.mediaIds,
          replyToTweetId: replyToId,
        }

        const result = await this.postTweet(tweetData)
        results.push(result)
        replyToId = result.id

        // Add small delay between tweets to avoid rate limits
        if (i < tweets.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      } catch (error) {
        console.error(`Failed to post tweet ${i + 1} in thread:`, error)
        throw new Error(`Failed to post tweet ${i + 1} in thread: ${error}`)
      }
    }

    return results
  }

  /**
   * Upload media to Twitter
   */
  async uploadMedia(mediaBuffer: Buffer, mimeType: string): Promise<MediaUploadResult> {
    try {
      // Use v1.1 API for media upload (v2 doesn't support media upload yet)
      const mediaId = await this.client.v1.uploadMedia(mediaBuffer, {
        mimeType,
        target: 'tweet',
      })

      return {
        mediaId,
        mediaKey: mediaId, // v1.1 returns media_id, v2 uses media_key
        size: mediaBuffer.length,
        expiresAfterSecs: 86400, // 24 hours default
      }
    } catch (error) {
      console.error('Twitter media upload error:', error)
      throw this.handleTwitterError(error)
    }
  }

  /**
   * Upload media from URL
   */
  async uploadMediaFromUrl(mediaUrl: string): Promise<MediaUploadResult> {
    try {
      // Download media from URL
      const response = await fetch(mediaUrl)
      if (!response.ok) {
        throw new Error(`Failed to download media: ${response.statusText}`)
      }

      const buffer = Buffer.from(await response.arrayBuffer())
      const mimeType = response.headers.get('content-type') || 'image/jpeg'

      return await this.uploadMedia(buffer, mimeType)
    } catch (error) {
      console.error('Media download/upload error:', error)
      throw new Error(`Failed to upload media from URL: ${error}`)
    }
  }

  /**
   * Get tweet by ID
   */
  async getTweet(tweetId: string): Promise<TweetResult | null> {
    try {
      const response = await this.client.v2.singleTweet(tweetId, {
        'tweet.fields': ['created_at', 'public_metrics'],
      })

      if (!response.data) {
        return null
      }

      return {
        id: response.data.id,
        text: response.data.text,
        createdAt: response.data.created_at || new Date().toISOString(),
        publicMetrics: response.data.public_metrics,
      }
    } catch (error) {
      console.error('Get tweet error:', error)
      return null
    }
  }

  /**
   * Delete a tweet
   */
  async deleteTweet(tweetId: string): Promise<boolean> {
    try {
      const response = await this.client.v2.deleteTweet(tweetId)
      return response.data.deleted
    } catch (error) {
      console.error('Delete tweet error:', error)
      throw this.handleTwitterError(error)
    }
  }

  /**
   * Get user's own tweets
   */
  async getUserTweets(userId: string, maxResults: number = 10): Promise<TweetResult[]> {
    try {
      const response = await this.client.v2.userTimeline(userId, {
        max_results: maxResults,
        'tweet.fields': ['created_at', 'public_metrics'],
      })

      return response.data.data?.map(tweet => ({
        id: tweet.id,
        text: tweet.text,
        createdAt: tweet.created_at || new Date().toISOString(),
        publicMetrics: tweet.public_metrics,
      })) || []
    } catch (error) {
      console.error('Get user tweets error:', error)
      throw this.handleTwitterError(error)
    }
  }

  /**
   * Verify credentials
   */
  async verifyCredentials(): Promise<{ id: string; username: string; name: string } | null> {
    try {
      const response = await this.client.v2.me({
        'user.fields': ['username', 'name'],
      })

      return {
        id: response.data.id,
        username: response.data.username,
        name: response.data.name,
      }
    } catch (error) {
      console.error('Verify credentials error:', error)
      return null
    }
  }

  /**
   * Refresh OAuth 2.0 access token
   */
  async refreshAccessToken(): Promise<{ accessToken: string; refreshToken?: string } | null> {
    try {
      const refreshedTokens = await this.client.refreshOAuth2Token()
      
      return {
        accessToken: refreshedTokens.accessToken,
        refreshToken: refreshedTokens.refreshToken,
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      return null
    }
  }

  /**
   * Handle Twitter API errors
   */
  private handleTwitterError(error: any): Error {
    if (error?.code) {
      switch (error.code) {
        case 401:
          return new Error('Twitter authentication failed. Please reconnect your account.')
        case 403:
          return new Error('Twitter API access forbidden. Check your permissions.')
        case 429:
          return new Error('Twitter API rate limit exceeded. Please try again later.')
        case 400:
          return new Error('Invalid request to Twitter API.')
        case 404:
          return new Error('Tweet or user not found.')
        case 500:
        case 502:
        case 503:
          return new Error('Twitter API server error. Please try again later.')
        default:
          return new Error(`Twitter API error (${error.code}): ${error.message || 'Unknown error'}`)
      }
    }

    if (error?.errors) {
      const errorMessages = error.errors.map((e: any) => e.message || e.detail).join(', ')
      return new Error(`Twitter API error: ${errorMessages}`)
    }

    return new Error(`Twitter API error: ${error?.message || 'Unknown error'}`)
  }
}

/**
 * Create Twitter client for a specific user account
 */
export async function createTwitterClient(twitterAccount: {
  accessToken: string
  refreshToken?: string
}): Promise<TwitterClientV2> {
  try {
    // Decrypt tokens
    const accessTokenData = await decryptToken(twitterAccount.accessToken)
    const refreshTokenData = twitterAccount.refreshToken 
      ? await decryptToken(twitterAccount.refreshToken)
      : null

    const credentials: TwitterCredentials = {
      accessToken: accessTokenData.token || accessTokenData,
      refreshToken: refreshTokenData?.token || refreshTokenData,
    }

    return new TwitterClientV2(credentials)
  } catch (error) {
    console.error('Failed to create Twitter client:', error)
    throw new Error('Failed to initialize Twitter client')
  }
}

/**
 * Validate Twitter API configuration
 */
export function validateTwitterConfig(): boolean {
  const requiredEnvVars = [
    'TWITTER_CLIENT_ID',
    'TWITTER_CLIENT_SECRET',
    'TWITTER_BEARER_TOKEN',
  ]

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar])
  
  if (missing.length > 0) {
    console.error('Missing Twitter API configuration:', missing)
    return false
  }

  return true
}

/**
 * Get Twitter OAuth 2.0 authorization URL
 */
export function getTwitterAuthUrl(): { url: string; codeVerifier: string; state: string } {
  if (!validateTwitterConfig()) {
    throw new Error('Twitter API configuration is incomplete')
  }

  const client = new TwitterApi({
    clientId: process.env.TWITTER_CLIENT_ID!,
    clientSecret: process.env.TWITTER_CLIENT_SECRET!,
  })

  const { url, codeVerifier, state } = client.generateOAuth2AuthLink(
    process.env.TWITTER_CALLBACK_URL || 'http://localhost:3000/api/auth/twitter/callback',
    {
      scope: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    }
  )

  return { url, codeVerifier, state }
}

/**
 * Exchange OAuth 2.0 code for tokens
 */
export async function exchangeTwitterCode(
  code: string,
  codeVerifier: string
): Promise<{ accessToken: string; refreshToken?: string; expiresIn?: number }> {
  if (!validateTwitterConfig()) {
    throw new Error('Twitter API configuration is incomplete')
  }

  const client = new TwitterApi({
    clientId: process.env.TWITTER_CLIENT_ID!,
    clientSecret: process.env.TWITTER_CLIENT_SECRET!,
  })

  try {
    const { accessToken, refreshToken, expiresIn } = await client.loginWithOAuth2({
      code,
      codeVerifier,
      redirectUri: process.env.TWITTER_CALLBACK_URL || 'http://localhost:3000/api/auth/twitter/callback',
    })

    return {
      accessToken,
      refreshToken,
      expiresIn,
    }
  } catch (error) {
    console.error('Twitter OAuth token exchange error:', error)
    throw new Error('Failed to exchange Twitter authorization code')
  }
}