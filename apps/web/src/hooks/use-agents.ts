import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import type { Agent, CreateAgentData, UpdateAgentData, AgentStats, AgentsListResponse } from '@/types/agent'

interface UseAgentsParams {
  page?: number
  limit?: number
  search?: string
  status?: 'active' | 'inactive' | 'all'
  sortBy?: 'createdAt' | 'name' | 'tweetsGenerated' | 'engagementRate'
  sortOrder?: 'asc' | 'desc'
}

export function useAgents(params: UseAgentsParams = {}) {
  const queryParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return useQuery<AgentsListResponse>({
    queryKey: ['agents', params],
    queryFn: async () => {
      const response = await fetch(`/api/agents?${queryParams.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch agents')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useAgent(id: string) {
  return useQuery<Agent>({
    queryKey: ['agents', id],
    queryFn: async () => {
      const response = await fetch(`/api/agents/${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch agent')
      }
      return response.json()
    },
    enabled: !!id,
  })
}

export function useCreateAgent() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateAgentData): Promise<Agent> => {
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create agent')
      }

      return response.json()
    },
    onSuccess: (newAgent) => {
      // Invalidate and refetch agents list
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      queryClient.invalidateQueries({ queryKey: ['agent-stats'] })
      
      toast({
        variant: 'success',
        title: 'Agent Created',
        description: `${newAgent.name} has been created successfully.`,
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Create Agent',
        description: error.message,
      })
    },
  })
}

export function useUpdateAgent() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateAgentData }): Promise<Agent> => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update agent')
      }

      return response.json()
    },
    onSuccess: (updatedAgent) => {
      // Update cache
      queryClient.setQueryData(['agents', updatedAgent.id], updatedAgent)
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      queryClient.invalidateQueries({ queryKey: ['agent-stats'] })
      
      toast({
        variant: 'success',
        title: 'Agent Updated',
        description: `${updatedAgent.name} has been updated successfully.`,
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Update Agent',
        description: error.message,
      })
    },
  })
}

export function useDeleteAgent() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete agent')
      }
    },
    onSuccess: () => {
      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      queryClient.invalidateQueries({ queryKey: ['agent-stats'] })
      
      toast({
        variant: 'success',
        title: 'Agent Deleted',
        description: 'Agent has been deleted successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Delete Agent',
        description: error.message,
      })
    },
  })
}

export function useToggleAgentStatus() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }): Promise<Agent> => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update agent status')
      }

      return response.json()
    },
    onMutate: async ({ id, isActive }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['agents'] })

      // Snapshot previous value
      const previousAgents = queryClient.getQueryData(['agents'])

      // Optimistically update
      queryClient.setQueriesData({ queryKey: ['agents'] }, (old: any) => {
        if (!old) return old
        
        return {
          ...old,
          agents: old.agents.map((agent: Agent) =>
            agent.id === id ? { ...agent, isActive } : agent
          ),
        }
      })

      return { previousAgents }
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousAgents) {
        queryClient.setQueryData(['agents'], context.previousAgents)
      }
      
      toast({
        variant: 'error',
        title: 'Failed to Update Status',
        description: error.message,
      })
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      queryClient.invalidateQueries({ queryKey: ['agent-stats'] })
    },
  })
}

export function useAgentStats() {
  return useQuery<AgentStats>({
    queryKey: ['agent-stats'],
    queryFn: async () => {
      const response = await fetch('/api/agents/stats')
      if (!response.ok) {
        throw new Error('Failed to fetch agent statistics')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}