import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

interface ModelInfo {
  id: string
  name: string
  description?: string
  provider: string
  contextLength?: number
  capabilities?: string[]
}

interface ModelsResponse {
  success: boolean
  data: {
    models: Record<string, ModelInfo[]>
    summary: {
      total: number
      byProvider: Record<string, number>
      capabilities: {
        openaiAvailable: boolean
        geminiAvailable: boolean
      }
    }
    provider: string
    defaultModel: string
  }
}

interface ModelTestResponse {
  success: boolean
  data: {
    modelId: string
    provider: string
    testPrompt: string
    result?: any
    error?: string
    timestamp: string
  }
}

/**
 * Hook to fetch available AI models from all providers
 */
export function useModels(provider?: 'openai' | 'gemini' | 'all') {
  return useQuery({
    queryKey: ['models', provider],
    queryFn: async (): Promise<ModelsResponse> => {
      const url = new URL('/api/ai/models', window.location.origin)
      if (provider && provider !== 'all') {
        url.searchParams.set('provider', provider)
      }

      const response = await fetch(url.toString())
      if (!response.ok) {
        throw new Error('Failed to fetch models')
      }

      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to test a specific model
 */
export function useTestModel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      modelId,
      provider,
      testPrompt = "Hello, how are you?",
    }: {
      modelId: string
      provider: string
      testPrompt?: string
    }): Promise<ModelTestResponse> => {
      const response = await fetch('/api/ai/models/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modelId,
          provider,
          testPrompt,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to test model')
      }

      return response.json()
    },
    onSuccess: () => {
      // Optionally invalidate models query to refresh status
      queryClient.invalidateQueries({ queryKey: ['models'] })
    },
  })
}

/**
 * Hook to get flattened list of all models
 */
export function useAllModels() {
  const { data, ...rest } = useModels('all')

  const flatModels = data?.data.models 
    ? Object.entries(data.data.models).flatMap(([provider, models]) =>
        models.map(model => ({ ...model, provider }))
      )
    : []

  return {
    ...rest,
    data: data ? {
      ...data,
      flatModels,
    } : undefined,
  }
}

/**
 * Hook to get models grouped by provider with additional utilities
 */
export function useModelsByProvider() {
  const { data, ...rest } = useModels('all')

  const getModelsByProvider = (provider: string) => {
    return data?.data.models[provider] || []
  }

  const getModelById = (modelId: string) => {
    if (!data?.data.models) return null

    for (const [provider, models] of Object.entries(data.data.models)) {
      const model = models.find(m => m.id === modelId)
      if (model) {
        return { ...model, provider }
      }
    }
    return null
  }

  const getProviders = () => {
    return data?.data.models ? Object.keys(data.data.models) : []
  }

  const isProviderAvailable = (provider: string) => {
    if (provider === 'openai') return data?.data.summary.capabilities.openaiAvailable
    if (provider === 'gemini') return data?.data.summary.capabilities.geminiAvailable
    return false
  }

  return {
    ...rest,
    data,
    getModelsByProvider,
    getModelById,
    getProviders,
    isProviderAvailable,
  }
}

/**
 * Hook to get enhanced generation capabilities
 */
export function useEnhancedCapabilities() {
  return useQuery({
    queryKey: ['enhanced-capabilities'],
    queryFn: async () => {
      const response = await fetch('/api/ai/generate-enhanced')
      if (!response.ok) {
        throw new Error('Failed to fetch enhanced capabilities')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Utility function to get default model for a provider
 */
export function getDefaultModelForProvider(provider: string, models: ModelInfo[]): string | null {
  if (!models.length) return null

  // Provider-specific defaults
  if (provider === 'openai') {
    // Prefer GPT-4 models, then GPT-3.5
    const gpt4 = models.find(m => m.id.includes('gpt-4') && !m.id.includes('vision'))
    if (gpt4) return gpt4.id

    const gpt35 = models.find(m => m.id.includes('gpt-3.5'))
    if (gpt35) return gpt35.id
  }

  if (provider === 'gemini') {
    // Prefer Flash for speed, then Pro for quality
    const flash = models.find(m => m.id.includes('flash'))
    if (flash) return flash.id

    const pro = models.find(m => m.id.includes('pro'))
    if (pro) return pro.id
  }

  // Fallback to first model
  return models[0]?.id || null
}

/**
 * Utility function to format model name for display
 */
export function formatModelName(model: ModelInfo): string {
  if (model.name && model.name !== model.id) {
    return model.name
  }

  // Format common model IDs
  if (model.id.startsWith('gpt-')) {
    return model.id.toUpperCase().replace('-', ' ')
  }

  if (model.id.startsWith('gemini-')) {
    return model.id
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ')
  }

  return model.id
}

/**
 * Utility function to get model capabilities as readable strings
 */
export function getModelCapabilitiesText(model: ModelInfo): string[] {
  const capabilities = model.capabilities || []
  const readable: string[] = []

  if (capabilities.includes('text-generation')) readable.push('Text Generation')
  if (capabilities.includes('multimodal')) readable.push('Multimodal')
  if (capabilities.includes('function-calling')) readable.push('Function Calling')
  if (capabilities.includes('embeddings')) readable.push('Embeddings')
  if (capabilities.includes('generateContent')) readable.push('Content Generation')

  return readable
}
