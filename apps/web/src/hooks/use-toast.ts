"use client"

import { toast as sonnerToast } from "sonner"

type ToastVariant = "default" | "success" | "error" | "warning" | "info"

interface ToastOptions {
  title?: string
  description?: string
  variant?: ToastVariant
  duration?: number
}

function toast({ title, description, variant = "default", duration }: ToastOptions) {
  const message = title || description || ""
  const options = {
    duration,
    description: title && description ? description : undefined,
  }

  switch (variant) {
    case "success":
      return sonnerToast.success(message, options)
    case "error":
      return sonnerToast.error(message, options)
    case "warning":
      return sonnerToast.warning(message, options)
    case "info":
      return sonnerToast.info(message, options)
    default:
      return sonnerToast(message, options)
  }
}

function useToast() {
  return {
    toast,
    dismiss: sonnerToast.dismiss,
  }
}

export { useToast, toast }