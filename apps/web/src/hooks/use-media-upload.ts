import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface MediaFile {
  url: string;
  fileId: string;
  fileType?: string;
  filename?: string;
}

interface UseMediaUploadOptions {
  maxFiles?: number;
  onUploadComplete?: (files: MediaFile[]) => void;
  onUploadError?: (error: Error) => void;
}

export function useMediaUpload(options: UseMediaUploadOptions = {}) {
  const { maxFiles = 10, onUploadComplete, onUploadError } = options;
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  const addFiles = useCallback((newFiles: Array<{ url: string; fileId: string; fileType?: string }>) => {
    const mediaFiles = newFiles.map((file) => ({
      ...file,
      filename: file.url.split('/').pop() || 'Unknown file',
    }));

    setFiles((prev) => {
      const combined = [...prev, ...mediaFiles];
      const limited = combined.slice(0, maxFiles);
      
      if (combined.length > maxFiles) {
        toast({
          variant: 'warning',
          title: 'File Limit Reached',
          description: `Only the first ${maxFiles} files were kept.`,
        });
      }
      
      return limited;
    });

    onUploadComplete?.(mediaFiles);
  }, [maxFiles, onUploadComplete, toast]);

  const removeFile = useCallback((fileId: string) => {
    setFiles((prev) => prev.filter((file) => file.fileId !== fileId));
  }, []);

  const clearFiles = useCallback(() => {
    setFiles([]);
  }, []);

  const handleUploadStart = useCallback(() => {
    setIsUploading(true);
  }, []);

  const handleUploadComplete = useCallback((uploadedFiles: Array<{ url: string; fileId: string; fileType?: string }>) => {
    setIsUploading(false);
    addFiles(uploadedFiles);
  }, [addFiles]);

  const handleUploadError = useCallback((error: Error) => {
    setIsUploading(false);
    onUploadError?.(error);
    
    toast({
      variant: 'error',
      title: 'Upload Failed',
      description: error.message || 'Failed to upload file',
    });
  }, [onUploadError, toast]);

  return {
    files,
    isUploading,
    addFiles,
    removeFile,
    clearFiles,
    handleUploadStart,
    handleUploadComplete,
    handleUploadError,
    canUploadMore: files.length < maxFiles,
    remainingSlots: Math.max(0, maxFiles - files.length),
  };
}