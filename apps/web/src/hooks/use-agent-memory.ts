import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'

export interface AgentMemory {
  id: string
  content: string
  context?: string
  agentId: string
  createdAt: Date
  updatedAt: Date
  similarity?: number
}

export interface MemoryStats {
  totalMemories: number
  averageContentLength: number
  oldestMemory?: Date
  newestMemory?: Date
}

export interface CreateMemoryData {
  content: string
  context?: string
}

export interface SearchMemoryParams {
  query: string
  limit?: number
  threshold?: number
}

export function useAgentMemories(agentId: string, limit: number = 50, offset: number = 0) {
  return useQuery({
    queryKey: ['agent-memories', agentId, limit, offset],
    queryFn: async () => {
      const response = await fetch(`/api/agents/${agentId}/memories?limit=${limit}&offset=${offset}`)
      if (!response.ok) {
        throw new Error('Failed to fetch agent memories')
      }
      return response.json()
    },
    enabled: !!agentId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useSearchMemories(agentId: string) {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (params: SearchMemoryParams) => {
      const searchParams = new URLSearchParams({
        query: params.query,
        limit: params.limit?.toString() || '5',
        threshold: params.threshold?.toString() || '0.7',
      })

      const response = await fetch(`/api/agents/${agentId}/memories?${searchParams.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to search memories')
      }
      return response.json()
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Search Failed',
        description: error.message,
      })
    },
  })
}

export function useCreateMemory(agentId: string) {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateMemoryData) => {
      const response = await fetch(`/api/agents/${agentId}/memories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create memory')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agent-memories', agentId] })
      toast({
        variant: 'success',
        title: 'Memory Created',
        description: 'Agent memory has been created successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Create Memory',
        description: error.message,
      })
    },
  })
}

export function useUpdateMemory(agentId: string) {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ memoryId, data }: { memoryId: string; data: CreateMemoryData }) => {
      const response = await fetch(`/api/agents/${agentId}/memories/${memoryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update memory')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agent-memories', agentId] })
      toast({
        variant: 'success',
        title: 'Memory Updated',
        description: 'Agent memory has been updated successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Update Memory',
        description: error.message,
      })
    },
  })
}

export function useDeleteMemory(agentId: string) {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (memoryId: string) => {
      const response = await fetch(`/api/agents/${agentId}/memories/${memoryId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete memory')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agent-memories', agentId] })
      toast({
        variant: 'success',
        title: 'Memory Deleted',
        description: 'Agent memory has been deleted successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Delete Memory',
        description: error.message,
      })
    },
  })
}

export function useEnhancedGeneration() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (request: {
      type: 'tweet' | 'thread' | 'improve' | 'custom' | 'contextual'
      agentId: string
      model?: string
      topic?: string
      content?: string
      tweetCount?: number
      improvementType?: 'engagement' | 'clarity' | 'tone' | 'length'
      customPrompt?: string
      useMemory?: boolean
      storeMemory?: boolean
    }) => {
      const response = await fetch('/api/ai/generate-enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || error.error || 'Failed to generate content')
      }

      return response.json()
    },
    onSuccess: (data) => {
      toast({
        variant: 'success',
        title: 'Content Generated',
        description: `Successfully generated ${data.data.type} content using ${data.data.memoriesUsed} memories.`,
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Generation Failed',
        description: error.message,
      })
    },
  })
}

export function useEnhancedCapabilities() {
  return useQuery({
    queryKey: ['enhanced-capabilities'],
    queryFn: async () => {
      const response = await fetch('/api/ai/generate-enhanced')
      if (!response.ok) {
        throw new Error('Failed to fetch capabilities')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}