import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import type { Thread, CreateThreadData } from '@/types/draft';

export function useThread(threadId: string) {
  return useQuery<Thread>({
    queryKey: ['threads', threadId],
    queryFn: async () => {
      const response = await fetch(`/api/threads?threadId=${threadId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch thread');
      }
      return response.json();
    },
    enabled: !!threadId,
  });
}

export function useCreateThread() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateThreadData): Promise<Thread> => {
      const response = await fetch('/api/threads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create thread');
      }

      return response.json();
    },
    onSuccess: (newThread) => {
      queryClient.invalidateQueries({ queryKey: ['drafts'] });
      queryClient.setQueryData(['threads', newThread.id], newThread);
      
      toast({
        variant: 'success',
        title: 'Thread Created',
        description: `Thread with ${newThread.totalTweets} tweets has been created.`,
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Create Thread',
        description: error.message,
      });
    },
  });
}