import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import type { ScheduledTweetListResponse, ScheduleData, RescheduleData } from '@/types/schedule'

interface UseScheduledTweetsParams {
  page?: number
  limit?: number
  status?: 'scheduled' | 'published' | 'failed'
}

export function useScheduledTweets(params: UseScheduledTweetsParams = {}) {
  const queryParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString())
    }
  })

  return useQuery<ScheduledTweetListResponse>({
    queryKey: ['scheduled-tweets', params],
    queryFn: async () => {
      const response = await fetch(`/api/tweets/schedule?${queryParams.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch scheduled tweets')
      }
      return response.json()
    },
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useScheduleTweet() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ScheduleData) => {
      const response = await fetch('/api/tweets/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to schedule tweet')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scheduled-tweets'] })
      queryClient.invalidateQueries({ queryKey: ['drafts'] })
      
      toast({
        variant: 'success',
        title: 'Tweet Scheduled',
        description: 'Your tweet has been scheduled successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Schedule Tweet',
        description: error.message,
      })
    },
  })
}

export function useUnscheduleTweet() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/tweets/schedule/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to unschedule tweet')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scheduled-tweets'] })
      queryClient.invalidateQueries({ queryKey: ['drafts'] })
      
      toast({
        variant: 'success',
        title: 'Tweet Unscheduled',
        description: 'Your tweet has been moved back to drafts.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Unschedule Tweet',
        description: error.message,
      })
    },
  })
}

export function useRescheduleTweet() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: RescheduleData }) => {
      const response = await fetch(`/api/tweets/schedule/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to reschedule tweet')
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scheduled-tweets'] })
      
      toast({
        variant: 'success',
        title: 'Tweet Rescheduled',
        description: 'Your tweet schedule has been updated.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Reschedule Tweet',
        description: error.message,
      })
    },
  })
}