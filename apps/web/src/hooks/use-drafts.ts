import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import type { Draft, CreateDraftData, UpdateDraftData, DraftListResponse } from '@/types/draft';

interface UseDraftsParams {
  page?: number;
  limit?: number;
  search?: string;
  agentId?: string;
  threadId?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'threadOrder';
  sortOrder?: 'asc' | 'desc';
}

export function useDrafts(params: UseDraftsParams = {}) {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });

  return useQuery<DraftListResponse>({
    queryKey: ['drafts', params],
    queryFn: async () => {
      const response = await fetch(`/api/drafts?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch drafts');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useDraft(id: string) {
  return useQuery<Draft>({
    queryKey: ['drafts', id],
    queryFn: async () => {
      const response = await fetch(`/api/drafts/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch draft');
      }
      return response.json();
    },
    enabled: !!id,
  });
}

export function useCreateDraft() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateDraftData): Promise<Draft> => {
      const response = await fetch('/api/drafts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create draft');
      }

      return response.json();
    },
    onSuccess: (newDraft) => {
      queryClient.invalidateQueries({ queryKey: ['drafts'] });
      
      toast({
        variant: 'success',
        title: 'Draft Saved',
        description: 'Your draft has been saved successfully.',
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Save Draft',
        description: error.message,
      });
    },
  });
}

export function useUpdateDraft() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateDraftData }): Promise<Draft> => {
      const response = await fetch(`/api/drafts/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update draft');
      }

      return response.json();
    },
    onSuccess: (updatedDraft) => {
      queryClient.setQueryData(['drafts', updatedDraft.id], updatedDraft);
      queryClient.invalidateQueries({ queryKey: ['drafts'] });
      
      toast({
        variant: 'success',
        title: 'Draft Updated',
        description: 'Your draft has been updated successfully.',
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Update Draft',
        description: error.message,
      });
    },
  });
}

export function useDeleteDraft() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/drafts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete draft');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drafts'] });
      
      toast({
        variant: 'success',
        title: 'Draft Deleted',
        description: 'Draft has been deleted successfully.',
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'error',
        title: 'Failed to Delete Draft',
        description: error.message,
      });
    },
  });
}