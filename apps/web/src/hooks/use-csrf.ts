import { useQuery } from '@tanstack/react-query'

interface CSRFTokenData {
  token: string
  fieldName: string
  headerName: string
}

/**
 * Hook to get CSRF token for forms and API requests
 */
export function useCSRF() {
  const { data, isLoading, error, refetch } = useQuery<CSRFTokenData>({
    queryKey: ['csrf-token'],
    queryFn: async () => {
      const response = await fetch('/api/csrf-token')
      if (!response.ok) {
        throw new Error('Failed to fetch CSRF token')
      }
      return response.json()
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    retry: 3,
  })

  /**
   * Get headers with CSRF token for API requests
   */
  const getCSRFHeaders = (): Record<string, string> => {
    if (!data?.token) return {}
    
    return {
      [data.headerName]: data.token,
    }
  }

  /**
   * Get form field data for CSRF token
   */
  const getCSRFField = (): { name: string; value: string } | null => {
    if (!data?.token) return null
    
    return {
      name: data.fieldName,
      value: data.token,
    }
  }

  /**
   * Create fetch options with CSRF headers
   */
  const createFetchOptions = (options: RequestInit = {}): RequestInit => {
    const csrfHeaders = getCSRFHeaders()
    
    return {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...csrfHeaders,
        ...options.headers,
      },
    }
  }

  return {
    token: data?.token,
    fieldName: data?.fieldName,
    headerName: data?.headerName,
    isLoading,
    error,
    refetch,
    getCSRFHeaders,
    getCSRFField,
    createFetchOptions,
  }
}

/**
 * Higher-order function to wrap fetch with CSRF protection
 */
export function createCSRFProtectedFetch() {
  return async (url: string, options: RequestInit = {}) => {
    // Get CSRF token
    const csrfResponse = await fetch('/api/csrf-token')
    if (!csrfResponse.ok) {
      throw new Error('Failed to get CSRF token')
    }
    
    const csrfData: CSRFTokenData = await csrfResponse.json()
    
    // Add CSRF header to request
    const headers = {
      'Content-Type': 'application/json',
      [csrfData.headerName]: csrfData.token,
      ...options.headers,
    }
    
    return fetch(url, {
      ...options,
      headers,
    })
  }
}