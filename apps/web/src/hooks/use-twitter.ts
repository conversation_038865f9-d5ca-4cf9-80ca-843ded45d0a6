import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'

export interface TwitterStatus {
  connected: boolean
  accounts: Array<{
    id: string
    username: string
    displayName: string
    isActive: boolean
  }>
  activeAccount?: {
    username: string
    displayName: string
    verified: boolean
    twitterId: string
  }
  recentTweets?: Array<{
    id: string
    text: string
    createdAt: string
    publicMetrics?: {
      retweetCount: number
      likeCount: number
      replyCount: number
      quoteCount: number
    }
  }>
  message: string
}

export interface TweetResult {
  tweetId: string
  text: string
  createdAt: string
  mediaCount: number
  twitterUrl: string
}

export interface ThreadResult {
  threadId: string
  tweets: Array<{
    tweetId: string
    text: string
    createdAt: string
    order: number
    url: string
  }>
  threadUrl: string
  totalTweets: number
}

export function useTwitterStatus() {
  return useQuery<TwitterStatus>({
    queryKey: ['twitter-status'],
    queryFn: async () => {
      const response = await fetch('/api/twitter/test')
      if (!response.ok) {
        throw new Error('Failed to fetch Twitter status')
      }
      return response.json()
    },
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function usePostTweet() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation<TweetResult, Error, {
    tweetText: string
    mediaUrls?: string[]
  }>({
    mutationFn: async ({ tweetText, mediaUrls }) => {
      const response = await fetch('/api/twitter/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tweetText,
          mediaUrls,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || error.error || 'Failed to post tweet')
      }

      const result = await response.json()
      return result.data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['twitter-status'] })
      
      toast({
        variant: 'success',
        title: 'Tweet Posted!',
        description: `Successfully posted tweet: ${data.tweetId}`,
      })
    },
    onError: (error) => {
      toast({
        variant: 'error',
        title: 'Failed to Post Tweet',
        description: error.message,
      })
    },
  })
}

export function usePostThread() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation<ThreadResult, Error, {
    tweets: Array<{
      text: string
      mediaUrls?: string[]
    }>
  }>({
    mutationFn: async ({ tweets }) => {
      const response = await fetch('/api/twitter/thread', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tweets }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || error.error || 'Failed to post thread')
      }

      const result = await response.json()
      return result.data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['twitter-status'] })
      
      toast({
        variant: 'success',
        title: 'Thread Posted!',
        description: `Successfully posted ${data.totalTweets} tweets`,
      })
    },
    onError: (error) => {
      toast({
        variant: 'error',
        title: 'Failed to Post Thread',
        description: error.message,
      })
    },
  })
}

export function useUploadMedia() {
  const { toast } = useToast()

  return useMutation<{
    mediaId: string
    mediaKey: string
    size: number
    expiresAfterSecs: number
  }, Error, {
    file?: File
    mediaUrl?: string
  }>({
    mutationFn: async ({ file, mediaUrl }) => {
      const formData = new FormData()
      
      if (file) {
        formData.append('file', file)
      }
      if (mediaUrl) {
        formData.append('mediaUrl', mediaUrl)
      }

      const response = await fetch('/api/twitter/media', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || error.error || 'Failed to upload media')
      }

      const result = await response.json()
      return result.data
    },
    onSuccess: (data) => {
      toast({
        variant: 'success',
        title: 'Media Uploaded!',
        description: `Media uploaded successfully: ${data.mediaId}`,
      })
    },
    onError: (error) => {
      toast({
        variant: 'error',
        title: 'Upload Failed',
        description: error.message,
      })
    },
  })
}

export function useTwitterMediaInfo() {
  return useQuery<{
    limits: {
      image: {
        maxSize: number
        maxCount: number
        supportedFormats: string[]
      }
      video: {
        maxSize: number
        maxCount: number
        maxDuration: number
        supportedFormats: string[]
      }
    }
    guidelines: string[]
  }>({
    queryKey: ['twitter-media-info'],
    queryFn: async () => {
      const response = await fetch('/api/twitter/media')
      if (!response.ok) {
        throw new Error('Failed to fetch media info')
      }
      const result = await response.json()
      return result.data
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}