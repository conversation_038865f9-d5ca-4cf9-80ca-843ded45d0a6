import { useState, useCallback } from 'react'
import { useAuth } from './use-auth'
import { UpdateUserProfileData, UserPreferences, ConnectedAccount } from '@/lib/validations/user'

interface UserProfile {
  id: string
  email: string
  name: string | null
  avatar: string | null
  preferences: UserPreferences | null
  createdAt: Date
  updatedAt: Date
  connectedAccounts: ConnectedAccount[]
}

interface UseProfileReturn {
  profile: UserProfile | null
  isLoading: boolean
  error: string | null
  updateProfile: (data: UpdateUserProfileData) => Promise<boolean>
  refreshProfile: () => Promise<void>
}

export function useProfile(): UseProfileReturn {
  const { user, refreshUser } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateProfile = useCallback(async (data: UpdateUserProfileData): Promise<boolean> => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/auth/me', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update profile')
      }

      // Refresh user data in auth context
      await refreshUser()
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile'
      setError(errorMessage)
      return false
    } finally {
      setIsLoading(false)
    }
  }, [refreshUser])

  const refreshProfile = useCallback(async () => {
    await refreshUser()
  }, [refreshUser])

  // Convert user data to profile format
  const profile: UserProfile | null = user ? {
    id: user.id,
    email: user.email,
    name: user.name || null,
    avatar: user.avatar || null,
    preferences: (user as any).preferences || null,
    createdAt: (user as any).createdAt || new Date(),
    updatedAt: (user as any).updatedAt || new Date(),
    connectedAccounts: (user as any).connectedAccounts || [],
  } : null

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    refreshProfile,
  }
}
