import * as React from 'react'
import { create } from 'zustand'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { UserPreferences, ConnectedAccount } from '@/lib/validations/user'

interface User {
  id: string
  email: string
  name?: string
  avatar?: string
  preferences?: UserPreferences
  createdAt?: Date
  updatedAt?: Date
  connectedAccounts?: ConnectedAccount[]
}

interface AuthStore {
  user: User | null
  isLoading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthStore>((set) => ({
  user: null,
  isLoading: true,
  setUser: (user) => set({ user }),
  setLoading: (isLoading) => set({ isLoading }),
}))

export function useAuth() {
  const { user, isLoading, setUser, setLoading } = useAuthStore()
  const queryClient = useQueryClient()

  // Query to get current user
  const { data: userData, isLoading: queryLoading } = useQuery({
    queryKey: ['auth', 'me'],
    queryFn: async () => {
      const response = await fetch('/api/auth/me')
      if (!response.ok) {
        if (response.status === 401) {
          return null
        }
        throw new Error('Failed to fetch user')
      }
      const data = await response.json()
      return data.user
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Update store when query data changes
  React.useEffect(() => {
    setUser(userData || null)
    setLoading(queryLoading)
  }, [userData, queryLoading, setUser, setLoading])

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      })
      if (!response.ok) {
        throw new Error('Logout failed')
      }
    },
    onSuccess: () => {
      setUser(null)
      queryClient.clear()
      window.location.href = '/login'
    },
  })

  const login = (provider: 'google' | 'twitter') => {
    window.location.href = `/api/auth/${provider}`
  }

  const logout = () => {
    logoutMutation.mutate()
  }

  const refreshUser = async () => {
    await queryClient.invalidateQueries({ queryKey: ['auth', 'me'] })
  }

  return {
    user,
    isLoading: isLoading || queryLoading,
    isAuthenticated: !!user,
    login,
    logout,
    refreshUser,
    isLoggingOut: logoutMutation.isPending,
  }
}