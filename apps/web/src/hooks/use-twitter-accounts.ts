import { useQuery } from '@tanstack/react-query'

export interface TwitterAccount {
  id: string
  username: string
  displayName: string
  profileImageUrl?: string
  isActive: boolean
}

export function useTwitterAccounts() {
  return useQuery<TwitterAccount[]>({
    queryKey: ['twitter-accounts'],
    queryFn: async () => {
      const response = await fetch('/api/auth/twitter/accounts')
      if (!response.ok) {
        throw new Error('Failed to fetch Twitter accounts')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}