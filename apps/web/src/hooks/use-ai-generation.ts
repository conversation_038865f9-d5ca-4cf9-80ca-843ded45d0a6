import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'

export interface GenerateContentRequest {
  type: 'tweet' | 'thread' | 'improve' | 'custom'
  agentId: string
  model?: string
  provider?: string
  topic?: string
  content?: string
  tweetCount?: number
  improvementType?: 'engagement' | 'clarity' | 'tone' | 'length'
  customPrompt?: string
}

export interface GenerateContentResponse {
  success: boolean
  data: {
    content: string
    usage: {
      promptTokens: number
      completionTokens: number
      totalTokens: number
    }
    model: string
    type: string
    agentId: string
  }
}

export interface GenerationHistory {
  agent: {
    id: string
    name: string
    tweetsGenerated: number
  }
  recentContent: Array<{
    id: string
    content: string
    status: string
    createdAt: string
    threadId?: string
    isThreadStart: boolean
  }>
}

export interface ContentSuggestion {
  suggestions: string[]
  agentId: string
  topics: string[]
  count: number
}

export function useGenerateContent() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation<GenerateContentResponse, <PERSON>rror, GenerateContentRequest>({
    mutationFn: async (request) => {
      // Use enhanced generation endpoint for better model support
      const response = await fetch('/api/ai/generate-enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || error.error || 'Failed to generate content')
      }

      return response.json()
    },
    onSuccess: (data, variables) => {
      // Invalidate generation history
      queryClient.invalidateQueries({ 
        queryKey: ['generation-history', variables.agentId] 
      })
      
      // Invalidate agent data to update metrics
      queryClient.invalidateQueries({ 
        queryKey: ['agents'] 
      })

      toast({
        variant: 'success',
        title: 'Content Generated',
        description: `Successfully generated ${variables.type} content using AI.`,
      })
    },
    onError: (error) => {
      console.error('Content generation error:', error)
      
      toast({
        variant: 'error',
        title: 'Generation Failed',
        description: error.message || 'Failed to generate content. Please try again.',
      })
    },
  })
}

export function useGenerationHistory(agentId: string) {
  return useQuery<GenerationHistory>({
    queryKey: ['generation-history', agentId],
    queryFn: async () => {
      const response = await fetch(`/api/ai/generate?agentId=${agentId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch generation history')
      }
      return response.json()
    },
    enabled: !!agentId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useContentSuggestions() {
  const { toast } = useToast()

  return useMutation<ContentSuggestion, Error, {
    agentId: string
    model?: string
    trendingTopics?: string[]
    count?: number
  }>({
    mutationFn: async (request) => {
      const response = await fetch('/api/ai/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || error.error || 'Failed to generate suggestions')
      }

      const result = await response.json()
      return result.data
    },
    onError: (error) => {
      console.error('Content suggestions error:', error)
      
      toast({
        variant: 'error',
        title: 'Suggestions Failed',
        description: error.message || 'Failed to generate content suggestions.',
      })
    },
  })
}

// Convenience hooks for specific generation types
export function useGenerateTweet() {
  const generateContent = useGenerateContent()

  return {
    ...generateContent,
    generateTweet: (agentId: string, topic?: string, model?: string, provider?: string) =>
      generateContent.mutate({ type: 'tweet', agentId, topic, model, provider }),
  }
}

export function useGenerateThread() {
  const generateContent = useGenerateContent()
  
  return {
    ...generateContent,
    generateThread: (agentId: string, topic: string, tweetCount?: number, model?: string) =>
      generateContent.mutate({ type: 'thread', agentId, topic, tweetCount, model }),
  }
}

export function useImproveContent() {
  const generateContent = useGenerateContent()
  
  return {
    ...generateContent,
    improveContent: (
      agentId: string, 
      content: string, 
      improvementType?: 'engagement' | 'clarity' | 'tone' | 'length',
      model?: string
    ) =>
      generateContent.mutate({ type: 'improve', agentId, content, improvementType, model }),
  }
}

// Hook to get available models
export function useAvailableModels() {
  return useQuery<{
    models: Array<{
      id: string
      name: string
      description?: string
      provider?: string
    }>
    provider: string
    defaultModel: string
  }>({
    queryKey: ['ai-models'],
    queryFn: async () => {
      const response = await fetch('/api/ai/models')
      if (!response.ok) {
        throw new Error('Failed to fetch models')
      }
      const result = await response.json()
      return result.data
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}