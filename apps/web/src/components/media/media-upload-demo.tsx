import * as React from "react";
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { UploadButton } from "@/components/ui/upload-button";
import { UploadDropzone } from "@/components/ui/upload-dropzone";
import { MediaPreview } from "@/components/media/media-preview";
import { Button } from "@/components/ui/button";
import { <PERSON>bs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Trash2 } from "lucide-react";

interface MediaFile {
  url: string;
  fileId: string;
  fileType?: string;
  filename?: string;
}

export function MediaUploadDemo() {
  const [uploadedFiles, setUploadedFiles] = useState<MediaFile[]>([]);

  const handleUploadComplete = (files: Array<{ url: string; fileId: string; fileType?: string }>) => {
    const newFiles = files.map((file) => ({
      ...file,
      filename: file.url.split('/').pop() || 'Unknown file',
    }));
    setUploadedFiles((prev) => [...prev, ...newFiles]);
  };

  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.fileId !== fileId));
  };

  const clearAllFiles = () => {
    setUploadedFiles([]);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>UploadThing Media Upload Demo</CardTitle>
          <CardDescription>
            Test the UploadThing integration with different upload methods and file types.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="dropzone" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="dropzone">Dropzone</TabsTrigger>
              <TabsTrigger value="button">Button</TabsTrigger>
              <TabsTrigger value="specific">Specific Types</TabsTrigger>
            </TabsList>

            <TabsContent value="dropzone" className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Upload Dropzone</h3>
                <UploadDropzone
                  endpoint="mediaUploader"
                  onUploadComplete={handleUploadComplete}
                />
              </div>
            </TabsContent>

            <TabsContent value="button" className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Upload Buttons</h3>
                <div className="flex flex-wrap gap-4">
                  <UploadButton
                    endpoint="mediaUploader"
                    onUploadComplete={handleUploadComplete}
                  />
                  <UploadButton
                    endpoint="imageUploader"
                    onUploadComplete={handleUploadComplete}
                  />
                  <UploadButton
                    endpoint="videoUploader"
                    onUploadComplete={handleUploadComplete}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="specific" className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Specific File Types</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Images Only (Max 4MB)</h4>
                    <UploadDropzone
                      endpoint="imageUploader"
                      onUploadComplete={handleUploadComplete}
                      className="h-32"
                    />
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">Videos Only (Max 16MB)</h4>
                    <UploadDropzone
                      endpoint="videoUploader"
                      onUploadComplete={handleUploadComplete}
                      className="h-32"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Uploaded Files Preview */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Uploaded Files ({uploadedFiles.length})</CardTitle>
                <CardDescription>
                  Preview of successfully uploaded media files
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFiles}
                className="flex items-center space-x-2"
              >
                <Trash2 className="h-4 w-4" />
                <span>Clear All</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <MediaPreview
              files={uploadedFiles}
              onRemove={handleRemoveFile}
            />
          </CardContent>
        </Card>
      )}

      {/* Upload Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{uploadedFiles.length}</div>
              <div className="text-sm text-muted-foreground">Total Files</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">
                {uploadedFiles.filter(f => f.fileType === 'image').length}
              </div>
              <div className="text-sm text-muted-foreground">Images</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">
                {uploadedFiles.filter(f => f.fileType === 'video').length}
              </div>
              <div className="text-sm text-muted-foreground">Videos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">
                {uploadedFiles.filter(f => !f.fileType || f.fileType === 'unknown').length}
              </div>
              <div className="text-sm text-muted-foreground">Other</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}