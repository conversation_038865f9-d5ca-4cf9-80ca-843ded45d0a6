import * as React from "react";
import { X, FileIcon, ImageIcon, VideoIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface MediaFile {
  url: string;
  fileId: string;
  fileType?: string;
  filename?: string;
}

interface MediaPreviewProps {
  files: MediaFile[];
  onRemove?: (fileId: string) => void;
  className?: string;
  maxHeight?: string;
}

export function MediaPreview({
  files,
  onRemove,
  className,
  maxHeight = "200px",
}: MediaPreviewProps) {
  if (files.length === 0) return null;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
        {files.map((file) => (
          <div
            key={file.fileId}
            className="relative group rounded-lg overflow-hidden border border-border bg-background"
          >
            {/* Remove button */}
            {onRemove && (
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                onClick={() => onRemove(file.fileId)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}

            {/* Media content */}
            <div className="aspect-square relative">
              {file.fileType === "image" || file.url.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                <img
                  src={file.url}
                  alt={file.filename || "Uploaded image"}
                  className="w-full h-full object-cover"
                  style={{ maxHeight }}
                />
              ) : file.fileType === "video" || file.url.match(/\.(mp4|webm|ogg|mov)$/i) ? (
                <div className="w-full h-full flex items-center justify-center bg-muted">
                  <VideoIcon className="h-8 w-8 text-muted-foreground" />
                  <video
                    src={file.url}
                    className="absolute inset-0 w-full h-full object-cover"
                    controls={false}
                    muted
                    preload="metadata"
                  />
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-muted">
                  <FileIcon className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>

            {/* File info */}
            <div className="p-2 bg-background/90 backdrop-blur-sm">
              <p className="text-xs text-muted-foreground truncate">
                {file.filename || "Uploaded file"}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}