import * as React from 'react';
import { useThread } from '@/hooks/use-threads';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Twitter } from 'lucide-react';

interface ThreadViewProps {
  threadId: string;
}

export function ThreadView({ threadId }: ThreadViewProps) {
  const { data: thread, isLoading, error } = useThread(threadId);

  if (isLoading) {
    return <ThreadSkeleton />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <Twitter className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Failed to load thread. Please try again.</AlertDescription>
      </Alert>
    );
  }

  if (!thread || thread.tweets.length === 0) {
    return (
      <Alert>
        <Twitter className="h-4 w-4" />
        <AlertTitle>Thread Not Found</AlertTitle>
        <AlertDescription>The requested thread could not be found.</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {thread.tweets.map((tweet, index) => (
        <div key={tweet.id} className="flex space-x-4">
          <div className="flex flex-col items-center">
            <Avatar>
              <AvatarImage src={tweet.agent?.profileImageUrl} alt={tweet.agent?.name} />
              <AvatarFallback>{tweet.agent?.name?.charAt(0)}</AvatarFallback>
            </Avatar>
            {index < thread.tweets.length - 1 && (
              <div className="w-0.5 h-full bg-border my-2" />
            )}
          </div>
          <Card className="flex-1">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 mb-2">
                <p className="font-semibold">{tweet.agent?.name}</p>
                <p className="text-sm text-muted-foreground">@{tweet.agent?.username}</p>
              </div>
              <p className="whitespace-pre-wrap">{tweet.content}</p>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
}

function ThreadSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex space-x-4">
          <div className="flex flex-col items-center">
            <Skeleton className="h-10 w-10 rounded-full" />
            <Skeleton className="w-0.5 h-16 bg-border my-2" />
          </div>
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
      ))}
    </div>
  );
}
