import * as React from 'react'
import Link from "next/link"
"use client"
import { usePathname } from "next/navigation"
import { 
  Bo<PERSON>, 
  Calendar, 
  BarChart3, 
  PenTool, 
  Settings, 
  Home,
  Zap,
  Sparkles,
  TestTube
} from "lucide-react"
import { cn } from "@/lib/utils"

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: Home },
  { name: "Agents", href: "/dashboard/agents", icon: Bo<PERSON> },
  { name: "<PERSON>mpo<PERSON>", href: "/dashboard/compose", icon: PenTool },
  { name: "Schedule", href: "/dashboard/schedule", icon: Calendar },
  { name: "AI Playground", href: "/dashboard/ai-playground", icon: Sparkles },
  { name: "Analytics", href: "/dashboard/analytics", icon: BarChart3 },
  { name: "Twitter Test", href: "/dashboard/twitter-test", icon: TestTube },
  { name: "Settings", href: "/settings", icon: Settings },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex h-full w-64 flex-col bg-dark-surface/50 backdrop-blur-sm border-r border-dark-border">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-dark-border">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-primary">
            <Zap className="h-5 w-5 text-white" />
          </div>
          <span className="text-xl font-bold text-white">XTask</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:text-foreground hover:bg-dark-surface"
              )}
            >
              <item.icon
                className={cn(
                  "mr-3 h-5 w-5 flex-shrink-0",
                  isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                )}
              />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* User section */}
      <div className="border-t border-dark-border p-4">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
            <span className="text-sm font-medium text-white">U</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-white truncate">User</p>
            <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  )
}