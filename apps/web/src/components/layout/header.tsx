import * as React from 'react'
import { <PERSON>, Search, User, Menu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { UserMenu } from "@/components/auth/user-menu"

interface HeaderProps {
  onMenuClick?: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  return (
    <header className="flex h-16 items-center justify-between border-b border-dark-border bg-dark-surface/50 backdrop-blur-sm px-4 sm:px-6">
      {/* Mobile Menu Button & Search */}
      <div className="flex items-center space-x-4 flex-1">
        {/* Mobile Menu Button */}
        {onMenuClick && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onMenuClick}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>
        )}

        {/* Search */}
        <div className="relative max-w-md flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search agents, tweets, analytics..."
            className="pl-10 w-full sm:w-80"
          />
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2 sm:space-x-4">
        <ThemeToggle />
        <Button variant="ghost" size="icon" className="hidden sm:flex">
          <Bell className="h-5 w-5" />
        </Button>
        <UserMenu />
      </div>
    </header>
  )
}