"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const tabsListVariants = cva(
  "inline-flex items-center justify-center text-muted-foreground",
  {
    variants: {
      variant: {
        default: "h-10 rounded-md bg-muted p-1",
        line: "h-10 border-b border-border bg-transparent p-0",
        pills: "h-10 rounded-lg bg-muted/50 p-1",
        underline: "h-10 bg-transparent p-0 border-b border-border",
      },
      size: {
        sm: "h-8 text-sm",
        md: "h-10 text-sm",
        lg: "h-12 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

const tabsTriggerVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "rounded-sm px-3 py-1.5 text-sm data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
        line: "border-b-2 border-transparent px-4 py-2 data-[state=active]:border-primary data-[state=active]:text-foreground",
        pills: "rounded-md px-3 py-1.5 text-sm data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
        underline: "border-b-2 border-transparent px-4 py-2 data-[state=active]:border-primary data-[state=active]:text-foreground",
      },
      size: {
        sm: "px-2 py-1 text-xs",
        md: "px-3 py-1.5 text-sm",
        lg: "px-4 py-2 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> &
    VariantProps<typeof tabsListVariants>
>(({ className, variant, size, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(tabsListVariants({ variant, size }), className)}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> &
    VariantProps<typeof tabsTriggerVariants>
>(({ className, variant, size, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(tabsTriggerVariants({ variant, size }), className)}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

// Enhanced Tabs component with simplified API
interface TabItem {
  value: string
  label: string
  content: React.ReactNode
  disabled?: boolean
  icon?: React.ComponentType<{ className?: string }>
  badge?: string | number
}

interface EnhancedTabsProps extends VariantProps<typeof tabsListVariants> {
  items: TabItem[]
  defaultValue?: string
  value?: string
  onValueChange?: (value: string) => void
  className?: string
  contentClassName?: string
}

const EnhancedTabs = ({
  items,
  defaultValue,
  value,
  onValueChange,
  variant = "default",
  size = "md",
  className,
  contentClassName,
}: EnhancedTabsProps) => {
  return (
    <Tabs
      defaultValue={defaultValue || items[0]?.value}
      value={value}
      onValueChange={onValueChange}
      className={className}
    >
      <TabsList variant={variant} size={size}>
        {items.map((item) => (
          <TabsTrigger
            key={item.value}
            value={item.value}
            disabled={item.disabled}
            variant={variant}
            size={size}
            className="gap-2"
          >
            {item.icon && <item.icon className="h-4 w-4" />}
            <span>{item.label}</span>
            {item.badge && (
              <span className="ml-1 rounded-full bg-muted-foreground/20 px-1.5 py-0.5 text-xs">
                {item.badge}
              </span>
            )}
          </TabsTrigger>
        ))}
      </TabsList>

      {items.map((item) => (
        <TabsContent
          key={item.value}
          value={item.value}
          className={contentClassName}
        >
          {item.content}
        </TabsContent>
      ))}
    </Tabs>
  )
}

// Vertical Tabs component
interface VerticalTabsProps extends EnhancedTabsProps {
  tabsWidth?: string
}

const VerticalTabs = ({
  items,
  defaultValue,
  value,
  onValueChange,
  variant = "line",
  size = "md",
  className,
  contentClassName,
  tabsWidth = "200px",
}: VerticalTabsProps) => {
  return (
    <Tabs
      defaultValue={defaultValue || items[0]?.value}
      value={value}
      onValueChange={onValueChange}
      orientation="vertical"
      className={cn("flex gap-4", className)}
    >
      <TabsList
        variant={variant}
        size={size}
        className="flex-col h-auto"
        style={{ width: tabsWidth }}
      >
        {items.map((item) => (
          <TabsTrigger
            key={item.value}
            value={item.value}
            disabled={item.disabled}
            variant={variant}
            size={size}
            className="w-full justify-start gap-2"
          >
            {item.icon && <item.icon className="h-4 w-4" />}
            <span>{item.label}</span>
            {item.badge && (
              <span className="ml-auto rounded-full bg-muted-foreground/20 px-1.5 py-0.5 text-xs">
                {item.badge}
              </span>
            )}
          </TabsTrigger>
        ))}
      </TabsList>

      <div className="flex-1">
        {items.map((item) => (
          <TabsContent
            key={item.value}
            value={item.value}
            className={cn("mt-0", contentClassName)}
          >
            {item.content}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  )
}

export {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  EnhancedTabs,
  VerticalTabs,
  tabsListVariants,
  tabsTriggerVariants,
  type TabItem,
  type EnhancedTabsProps,
  type VerticalTabsProps,
}
