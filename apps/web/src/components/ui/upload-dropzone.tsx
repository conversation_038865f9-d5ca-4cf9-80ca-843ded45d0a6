import * as React from "react";
import { UploadDropzone as UTUploadDropzone } from "@/lib/uploadthing";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface UploadDropzoneProps {
  endpoint: "imageUploader" | "videoUploader" | "mediaUploader";
  onUploadComplete?: (files: Array<{ url: string; fileId: string; fileType?: string }>) => void;
  onUploadError?: (error: Error) => void;
  className?: string;
  disabled?: boolean;
}

export function UploadDropzone({
  endpoint,
  onUploadComplete,
  onUploadError,
  className,
  disabled = false,
}: UploadDropzoneProps) {
  const { toast } = useToast();

  return (
    <UTUploadDropzone
      endpoint={endpoint}
      onClientUploadComplete={(res) => {
        if (res) {
          const files = res.map((file) => ({
            url: file.url,
            fileId: file.serverData?.fileId || "",
            fileType: file.serverData?.fileType,
          }));
          
          onUploadComplete?.(files);
          
          toast({
            variant: "success",
            title: "Upload Complete",
            description: `Successfully uploaded ${res.length} file${res.length > 1 ? 's' : ''}`,
          });
        }
      }}
      onUploadError={(error: Error) => {
        console.error("Upload error:", error);
        onUploadError?.(error);
        
        toast({
          variant: "error",
          title: "Upload Failed",
          description: error.message || "Failed to upload file",
        });
      }}
      appearance={{
        container: cn(
          "border-2 border-dashed border-border rounded-lg p-8",
          "hover:border-primary/50 transition-colors",
          "bg-background/50",
          disabled && "opacity-50 pointer-events-none",
          className
        ),
        uploadIcon: "text-primary",
        label: "text-foreground font-medium",
        allowedContent: "text-muted-foreground text-sm",
        button: cn(
          "bg-primary text-primary-foreground hover:bg-primary/90",
          "rounded-md px-4 py-2 text-sm font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
        ),
      }}
      content={{
        label: `Drop ${endpoint === "imageUploader" ? "images" : endpoint === "videoUploader" ? "videos" : "files"} here or click to browse`,
        allowedContent: `Max ${endpoint === "videoUploader" ? "16MB" : "4MB"} ${endpoint === "imageUploader" ? "images" : endpoint === "videoUploader" ? "videos" : "files"}`,
      }}
    />
  );
}