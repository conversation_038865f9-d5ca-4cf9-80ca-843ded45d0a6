# Enhanced UI Components

This directory contains a comprehensive collection of enhanced UI components built on top of shadcn/ui and Radix UI primitives. All components are fully typed, accessible, and customizable with Tailwind CSS.

## 🎨 Component Overview

### Modal Components
- **Modal**: Basic modal with size variants and customizable content
- **ConfirmModal**: Confirmation dialog with action buttons
- **AlertModal**: Alert dialog with type indicators (info, success, warning, error)
- **FormModal**: Modal specifically designed for forms with submit/cancel actions

### Avatar Components
- **Avatar**: Basic avatar with image fallback
- **UserAvatar**: Enhanced avatar with status indicators and size variants
- **AvatarGroup**: Display multiple avatars in a group with overflow indicator

### Dropdown Menu Components
- **DropdownMenu**: Full-featured dropdown menu with all Radix primitives
- **ActionMenu**: Pre-configured action menu with common patterns
- **UserMenu**: User-specific menu with profile, settings, and sign-out options

### Tab Components
- **Tabs**: Basic tab component with enhanced styling
- **EnhancedTabs**: Simplified API with icon and badge support
- **VerticalTabs**: Vertical orientation tabs for sidebar navigation

### Loading Components
- **Spinner**: Circular loading spinner with size and color variants
- **DotsSpinner**: Three-dot loading animation
- **PulseSpinner**: Pulsing circle loader
- **LoadingOverlay**: Overlay component for loading states

### Progress Components
- **Progress**: Linear progress bar with color variants
- **CircularProgress**: Circular progress indicator
- **StepProgress**: Multi-step progress indicator for wizards

### Theme Components
- **ThemeToggle**: Dropdown theme selector
- **ThemeSwitch**: Toggle switch for light/dark mode
- **ThemeSelector**: Button group theme selector

### Toast Components
- **Toast**: Notification toast with variants
- **Toaster**: Toast container with theme integration

## 🚀 Quick Start

```tsx
import { 
  Modal, 
  UserAvatar, 
  ActionMenu, 
  EnhancedTabs,
  LoadingOverlay,
  Progress,
  ThemeToggle 
} from "@/components/ui"

// Basic usage examples
function MyComponent() {
  return (
    <div>
      {/* Avatar with status */}
      <UserAvatar 
        name="John Doe" 
        size="lg" 
        showStatus 
        status="online" 
      />
      
      {/* Action menu */}
      <ActionMenu items={[
        { label: "Edit", icon: Edit, onClick: handleEdit },
        { label: "Delete", icon: Trash, onClick: handleDelete, destructive: true }
      ]} />
      
      {/* Enhanced tabs */}
      <EnhancedTabs items={[
        { value: "tab1", label: "Overview", content: <div>Content 1</div> },
        { value: "tab2", label: "Settings", icon: Settings, content: <div>Content 2</div> }
      ]} />
      
      {/* Loading overlay */}
      <LoadingOverlay isLoading={loading} text="Processing...">
        <div>Your content here</div>
      </LoadingOverlay>
      
      {/* Progress bar */}
      <Progress value={75} color="success" showValue />
      
      {/* Theme toggle */}
      <ThemeToggle />
    </div>
  )
}
```

## 📖 Component Documentation

### Modal Components

#### Modal
```tsx
<Modal
  open={isOpen}
  onOpenChange={setIsOpen}
  title="Modal Title"
  description="Modal description"
  size="md" // sm, md, lg, xl, 2xl, 3xl, 4xl, 5xl, full
>
  <div>Modal content</div>
</Modal>
```

#### ConfirmModal
```tsx
<ConfirmModal
  open={isOpen}
  onOpenChange={setIsOpen}
  title="Confirm Action"
  description="Are you sure?"
  onConfirm={handleConfirm}
  variant="destructive" // default, destructive
  confirmText="Delete"
  cancelText="Cancel"
/>
```

### Avatar Components

#### UserAvatar
```tsx
<UserAvatar
  name="John Doe"
  src="/avatar.jpg"
  size="lg" // xs, sm, md, lg, xl, 2xl, 3xl
  showStatus={true}
  status="online" // online, offline, away, busy
/>
```

#### AvatarGroup
```tsx
<AvatarGroup
  avatars={[
    { name: "John", src: "/john.jpg" },
    { name: "Jane", src: "/jane.jpg" }
  ]}
  max={3}
  size="md"
/>
```

### Loading Components

#### LoadingOverlay
```tsx
<LoadingOverlay
  isLoading={loading}
  spinner="default" // default, dots, pulse
  size="lg"
  text="Loading..."
>
  <div>Content to overlay</div>
</LoadingOverlay>
```

### Progress Components

#### Progress
```tsx
<Progress
  value={75}
  size="md" // sm, md, lg, xl
  color="success" // default, secondary, success, warning, danger, info, gradient
  showValue={true}
  formatValue={(value) => `${value}%`}
/>
```

#### CircularProgress
```tsx
<CircularProgress
  value={75}
  size={120}
  strokeWidth={8}
  color="hsl(var(--primary))"
  showValue={true}
/>
```

## 🎨 Styling & Customization

All components use CSS variables for theming and can be customized through:

1. **Tailwind Classes**: Pass custom classes via `className` prop
2. **CSS Variables**: Modify theme colors in your CSS
3. **Variant Props**: Use built-in size and variant options
4. **Custom Styles**: Override styles with CSS modules or styled-components

## 🔧 TypeScript Support

All components are fully typed with TypeScript, providing:
- Prop validation
- IntelliSense support
- Type-safe event handlers
- Generic type support where applicable

## 🌙 Theme Integration

Components automatically adapt to light/dark themes using:
- `next-themes` for theme management
- CSS variables for color tokens
- Automatic theme detection
- Smooth theme transitions

## 📱 Responsive Design

Components are built with mobile-first responsive design:
- Flexible sizing options
- Touch-friendly interactions
- Responsive breakpoints
- Adaptive layouts

## ♿ Accessibility

All components follow accessibility best practices:
- ARIA labels and roles
- Keyboard navigation
- Screen reader support
- Focus management
- Color contrast compliance

## 🧪 Testing

Components can be tested using:
- Jest for unit tests
- React Testing Library for integration tests
- Storybook for visual testing
- Playwright for E2E testing

## 📦 Bundle Size

Components are optimized for bundle size:
- Tree-shakeable exports
- Minimal dependencies
- Code splitting support
- Lazy loading where appropriate

## 🔄 Updates & Maintenance

To update components:
1. Follow semantic versioning
2. Update TypeScript definitions
3. Test across all variants
4. Update documentation
5. Run accessibility audits

## 🤝 Contributing

When adding new components:
1. Follow existing patterns
2. Include TypeScript types
3. Add accessibility features
4. Write comprehensive tests
5. Update documentation
6. Add to showcase page
