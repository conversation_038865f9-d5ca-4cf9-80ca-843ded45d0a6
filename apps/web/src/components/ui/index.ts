/**
 * Enhanced UI Components Index
 * 
 * This file exports all enhanced UI components for easy importing
 * throughout the application.
 */

// Core shadcn/ui components
export * from "./button"
export * from "./input"
export * from "./label"
export * from "./textarea"
export * from "./select"
export * from "./checkbox"
export * from "./radio-group"
export * from "./switch"
export * from "./slider"
export * from "./card"
export * from "./badge"
export * from "./separator"
export * from "./dialog"
export * from "./sheet"
export * from "./popover"
export * from "./tooltip"
export * from "./accordion"
export * from "./collapsible"
export * from "./command"
export * from "./calendar"
export * from "./form"
export * from "./table"
export * from "./scroll-area"
export * from "./resizable"

// Enhanced Modal Components
export {
  Modal,
  ConfirmModal,
  AlertModal,
  FormModal,
  modalVariants,
  type ModalProps,
  type ConfirmModalProps,
  type AlertModalProps,
  type FormModalProps,
} from "./modal"

// Enhanced Avatar Components
export {
  Avatar,
  AvatarImage,
  AvatarFallback,
  UserAvatar,
  AvatarGroup,
  avatarVariants,
  type UserAvatarProps,
  type AvatarGroupProps,
} from "./avatar"

// Enhanced Dropdown Menu Components
export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
  ActionMenu,
  UserMenu,
  type ActionMenuItem,
  type ActionMenuProps,
  type UserMenuProps,
} from "./dropdown-menu"

// Enhanced Tab Components
export {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  EnhancedTabs,
  VerticalTabs,
  tabsListVariants,
  tabsTriggerVariants,
  type TabItem,
  type EnhancedTabsProps,
  type VerticalTabsProps,
} from "./tabs"

// Toast Components
export {
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
  ToastProvider,
  ToastViewport,
  type ToastProps,
  type ToastActionElement,
} from "./toast"

export { Toaster } from "./toaster"

// Enhanced Spinner Components
export {
  Spinner,
  DotsSpinner,
  PulseSpinner,
  LoadingOverlay,
  spinnerVariants,
  type SpinnerProps,
  type DotsSpinnerProps,
  type PulseSpinnerProps,
  type LoadingOverlayProps,
} from "./spinner"

// Enhanced Progress Components
export {
  Progress,
  CircularProgress,
  StepProgress,
  progressVariants,
  progressIndicatorVariants,
  type ProgressProps,
  type CircularProgressProps,
  type StepProgressProps,
  type Step,
} from "./progress"

// Enhanced Theme Components
export {
  ThemeToggle,
  ThemeSwitch,
  ThemeSelector,
  themeToggleVariants,
  type ThemeToggleProps,
} from "./theme-toggle"

// Hooks
export { useToast, toast } from "../../hooks/use-toast"

// Types for common component patterns
export interface ComponentVariants {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "secondary" | "outline" | "ghost"
}

export interface LoadingState {
  isLoading: boolean
  loadingText?: string
}

export interface StatusIndicator {
  status?: "online" | "offline" | "away" | "busy"
  showStatus?: boolean
}

// Utility types
export type Size = "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "3xl"
export type Variant = "default" | "secondary" | "outline" | "ghost" | "destructive"
export type Color = "default" | "primary" | "secondary" | "success" | "warning" | "danger" | "info"

// Component composition helpers
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface InteractiveComponentProps extends BaseComponentProps {
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
}

// Re-export commonly used utilities
export { cn } from "../../lib/utils"
