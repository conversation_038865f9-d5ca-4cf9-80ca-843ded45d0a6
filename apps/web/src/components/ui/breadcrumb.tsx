"use client"

import * as React from "react"
import { ChevronRight, Home, MoreHorizontal } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const breadcrumbVariants = cva(
  "flex items-center space-x-1 text-sm text-muted-foreground",
  {
    variants: {
      size: {
        sm: "text-xs",
        md: "text-sm",
        lg: "text-base",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

const breadcrumbItemVariants = cva(
  "inline-flex items-center transition-colors hover:text-foreground",
  {
    variants: {
      variant: {
        default: "hover:text-foreground",
        link: "hover:text-primary hover:underline",
        button: "hover:bg-accent hover:text-accent-foreground rounded-md px-2 py-1",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
  onClick?: () => void
  current?: boolean
}

interface BreadcrumbProps extends VariantProps<typeof breadcrumbVariants> {
  items: BreadcrumbItem[]
  separator?: React.ReactNode
  maxItems?: number
  showHome?: boolean
  homeHref?: string
  onHomeClick?: () => void
  className?: string
  itemVariant?: VariantProps<typeof breadcrumbItemVariants>["variant"]
}

const Breadcrumb = React.forwardRef<HTMLElement, BreadcrumbProps>(
  ({
    items,
    separator = <ChevronRight className="h-4 w-4" />,
    maxItems = 3,
    showHome = true,
    homeHref = "/",
    onHomeClick,
    size,
    className,
    itemVariant = "default",
    ...props
  }, ref) => {
    // Add home item if requested
    const allItems = showHome
      ? [
          {
            label: "Home",
            href: homeHref,
            onClick: onHomeClick,
            icon: Home,
          },
          ...items,
        ]
      : items

    // Handle item overflow
    const shouldCollapse = allItems.length > maxItems
    const visibleItems = shouldCollapse
      ? [
          allItems[0], // First item (usually Home)
          ...allItems.slice(-2), // Last 2 items
        ]
      : allItems

    const collapsedItems = shouldCollapse
      ? allItems.slice(1, -2) // Items between first and last 2
      : []

    const renderBreadcrumbItem = (item: BreadcrumbItem, index: number) => {
      const isLast = index === visibleItems.length - 1
      const ItemIcon = item.icon

      const itemContent = (
        <>
          {ItemIcon && <ItemIcon className="h-4 w-4 mr-1" />}
          <span className={cn(isLast && item.current && "font-medium text-foreground")}>
            {item.label}
          </span>
        </>
      )

      const itemElement = item.href ? (
        <a
          href={item.href}
          className={cn(breadcrumbItemVariants({ variant: itemVariant }))}
          onClick={item.onClick}
          aria-current={isLast && item.current ? "page" : undefined}
        >
          {itemContent}
        </a>
      ) : item.onClick ? (
        <button
          onClick={item.onClick}
          className={cn(breadcrumbItemVariants({ variant: itemVariant }))}
          aria-current={isLast && item.current ? "page" : undefined}
        >
          {itemContent}
        </button>
      ) : (
        <span
          className={cn(breadcrumbItemVariants({ variant: itemVariant }))}
          aria-current={isLast && item.current ? "page" : undefined}
        >
          {itemContent}
        </span>
      )

      return (
        <React.Fragment key={index}>
          {itemElement}
          {!isLast && (
            <span className="text-muted-foreground" aria-hidden="true">
              {separator}
            </span>
          )}
        </React.Fragment>
      )
    }

    const renderCollapsedItems = () => {
      if (collapsedItems.length === 0) return null

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-1 text-muted-foreground hover:text-foreground"
                aria-label={`Show ${collapsedItems.length} more items`}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {collapsedItems.map((item, index) => (
                <DropdownMenuItem
                  key={index}
                  onClick={item.onClick}
                  asChild={!!item.href}
                >
                  {item.href ? (
                    <a href={item.href} className="flex items-center">
                      {item.icon && <item.icon className="h-4 w-4 mr-2" />}
                      {item.label}
                    </a>
                  ) : (
                    <div className="flex items-center">
                      {item.icon && <item.icon className="h-4 w-4 mr-2" />}
                      {item.label}
                    </div>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <span className="text-muted-foreground" aria-hidden="true">
            {separator}
          </span>
        </>
      )
    }

    return (
      <nav
        ref={ref}
        aria-label="Breadcrumb"
        className={cn(breadcrumbVariants({ size }), className)}
        {...props}
      >
        <ol className="flex items-center space-x-1">
          <li className="flex items-center space-x-1">
            {visibleItems[0] && renderBreadcrumbItem(visibleItems[0], 0)}
          </li>
          
          {shouldCollapse && (
            <li className="flex items-center space-x-1">
              <span className="text-muted-foreground" aria-hidden="true">
                {separator}
              </span>
              {renderCollapsedItems()}
            </li>
          )}
          
          {visibleItems.slice(1).map((item, index) => (
            <li key={index + 1} className="flex items-center space-x-1">
              <span className="text-muted-foreground" aria-hidden="true">
                {separator}
              </span>
              {renderBreadcrumbItem(item, index + 1)}
            </li>
          ))}
        </ol>
      </nav>
    )
  }
)
Breadcrumb.displayName = "Breadcrumb"

// Simple Breadcrumb component for basic use cases
interface SimpleBreadcrumbProps {
  paths: string[]
  baseHref?: string
  className?: string
  onNavigate?: (path: string, index: number) => void
}

const SimpleBreadcrumb = ({
  paths,
  baseHref = "",
  className,
  onNavigate,
}: SimpleBreadcrumbProps) => {
  const items: BreadcrumbItem[] = paths.map((path, index) => ({
    label: path.charAt(0).toUpperCase() + path.slice(1).replace(/[-_]/g, " "),
    href: onNavigate ? undefined : `${baseHref}/${paths.slice(0, index + 1).join("/")}`,
    onClick: onNavigate ? () => onNavigate(path, index) : undefined,
    current: index === paths.length - 1,
  }))

  return <Breadcrumb items={items} className={className} />
}

export {
  Breadcrumb,
  SimpleBreadcrumb,
  breadcrumbVariants,
  breadcrumbItemVariants,
  type BreadcrumbProps,
  type BreadcrumbItem,
  type SimpleBreadcrumbProps,
}
