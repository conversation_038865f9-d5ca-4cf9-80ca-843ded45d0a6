"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const progressVariants = cva(
  "relative w-full overflow-hidden bg-secondary",
  {
    variants: {
      size: {
        sm: "h-2",
        md: "h-4",
        lg: "h-6",
        xl: "h-8",
      },
      variant: {
        default: "rounded-full",
        square: "rounded-none",
        rounded: "rounded-md",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
)

const progressIndicatorVariants = cva(
  "h-full w-full flex-1 transition-all duration-300 ease-in-out",
  {
    variants: {
      color: {
        default: "bg-primary",
        secondary: "bg-secondary-foreground",
        success: "bg-green-500",
        warning: "bg-yellow-500",
        danger: "bg-red-500",
        info: "bg-blue-500",
        gradient: "bg-gradient-to-r from-blue-500 to-purple-600",
      },
    },
    defaultVariants: {
      color: "default",
    },
  }
)

interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  color?: VariantProps<typeof progressIndicatorVariants>["color"]
  showValue?: boolean
  formatValue?: (value: number) => string
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, size, variant, color, showValue, formatValue, ...props }, ref) => {
  const displayValue = value || 0
  const formattedValue = formatValue ? formatValue(displayValue) : `${Math.round(displayValue)}%`

  return (
    <div className="w-full">
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(progressVariants({ size, variant }), className)}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(progressIndicatorVariants({ color }))}
          style={{ transform: `translateX(-${100 - displayValue}%)` }}
        />
      </ProgressPrimitive.Root>
      {showValue && (
        <div className="mt-1 text-right text-sm text-muted-foreground">
          {formattedValue}
        </div>
      )}
    </div>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

// Circular Progress Component
interface CircularProgressProps {
  value: number
  size?: number
  strokeWidth?: number
  color?: string
  backgroundColor?: string
  showValue?: boolean
  formatValue?: (value: number) => string
  className?: string
}

const CircularProgress = ({
  value,
  size = 120,
  strokeWidth = 8,
  color = "hsl(var(--primary))",
  backgroundColor = "hsl(var(--secondary))",
  showValue = true,
  formatValue,
  className,
}: CircularProgressProps) => {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const offset = circumference - (value / 100) * circumference
  const displayValue = formatValue ? formatValue(value) : `${Math.round(value)}%`

  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          strokeLinecap="round"
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium text-foreground">
            {displayValue}
          </span>
        </div>
      )}
    </div>
  )
}

// Multi-step Progress Component
interface Step {
  label: string
  description?: string
  completed?: boolean
  current?: boolean
}

interface StepProgressProps {
  steps: Step[]
  currentStep?: number
  orientation?: "horizontal" | "vertical"
  showLabels?: boolean
  className?: string
}

const StepProgress = ({
  steps,
  currentStep = 0,
  orientation = "horizontal",
  showLabels = true,
  className,
}: StepProgressProps) => {
  const isHorizontal = orientation === "horizontal"

  return (
    <div className={cn(
      "flex",
      isHorizontal ? "items-center space-x-4" : "flex-col space-y-4",
      className
    )}>
      {steps.map((step, index) => {
        const isCompleted = index < currentStep || step.completed
        const isCurrent = index === currentStep || step.current
        const isUpcoming = index > currentStep && !step.completed

        return (
          <div
            key={index}
            className={cn(
              "flex items-center",
              isHorizontal ? "flex-row" : "flex-col",
              !isHorizontal && "w-full"
            )}
          >
            {/* Step indicator */}
            <div className="flex items-center">
              <div
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium",
                  isCompleted && "border-primary bg-primary text-primary-foreground",
                  isCurrent && "border-primary bg-background text-primary",
                  isUpcoming && "border-muted bg-background text-muted-foreground"
                )}
              >
                {isCompleted ? "✓" : index + 1}
              </div>

              {/* Connector line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    isHorizontal ? "h-0.5 w-12" : "h-12 w-0.5 ml-4",
                    isCompleted ? "bg-primary" : "bg-muted"
                  )}
                />
              )}
            </div>

            {/* Step content */}
            {showLabels && (
              <div className={cn(
                isHorizontal ? "ml-3" : "mt-2 text-center"
              )}>
                <div className={cn(
                  "text-sm font-medium",
                  isCurrent ? "text-foreground" : "text-muted-foreground"
                )}>
                  {step.label}
                </div>
                {step.description && (
                  <div className="text-xs text-muted-foreground">
                    {step.description}
                  </div>
                )}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export {
  Progress,
  CircularProgress,
  StepProgress,
  progressVariants,
  progressIndicatorVariants,
  type ProgressProps,
  type CircularProgressProps,
  type StepProgressProps,
  type Step,
}
