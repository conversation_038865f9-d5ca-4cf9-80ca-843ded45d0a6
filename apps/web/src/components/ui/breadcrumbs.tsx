import * as React from "react"
import { ChevronRight, MoreHorizontal } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[]
  maxItems?: number
  className?: string
}

const Breadcrumbs = React.forwardRef<HTMLElement, BreadcrumbsProps>(
  ({ items, maxItems = 3, className, ...props }, ref) => {
    const displayItems = React.useMemo(() => {
      if (items.length <= maxItems) {
        return items
      }

      const firstItem = items[0]
      const lastItems = items.slice(-(maxItems - 1))
      
      return [firstItem, { label: "...", href: undefined }, ...lastItems]
    }, [items, maxItems])

    return (
      <nav
        ref={ref}
        aria-label="Breadcrumb"
        className={cn("flex", className)}
        {...props}
      >
        <ol className="flex items-center space-x-1 md:space-x-3">
          {displayItems.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="mx-1 h-4 w-4 text-muted-foreground" />
              )}
              {item.label === "..." ? (
                <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
              ) : item.href && !item.current ? (
                <a
                  href={item.href}
                  className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                >
                  {item.label}
                </a>
              ) : (
                <span
                  className={cn(
                    "text-sm font-medium",
                    item.current
                      ? "text-foreground"
                      : "text-muted-foreground"
                  )}
                  aria-current={item.current ? "page" : undefined}
                >
                  {item.label}
                </span>
              )}
            </li>
          ))}
        </ol>
      </nav>
    )
  }
)
Breadcrumbs.displayName = "Breadcrumbs"

export { Breadcrumbs, type BreadcrumbItem }