import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const spinnerVariants = cva(
  "animate-spin rounded-full border-2 border-current border-t-transparent",
  {
    variants: {
      size: {
        sm: "h-4 w-4",
        md: "h-6 w-6", 
        lg: "h-8 w-8",
        xl: "h-12 w-12",
      },
      variant: {
        default: "text-primary",
        muted: "text-muted-foreground",
        white: "text-white",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
)

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(spinnerVariants({ size, variant }), className)}
        role="status"
        aria-label="Loading"
        {...props}
      >
        <span className="sr-only">Loading...</span>
      </div>
    )
  }
)
Spinner.displayName = "Spinner"

// Dots Spinner Component
interface DotsSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "muted" | "white"
}

const DotsSpinner = React.forwardRef<HTMLDivElement, DotsSpinnerProps>(
  ({ className, size = "md", variant = "default", ...props }, ref) => {
    const dotSizes = {
      sm: "h-1 w-1",
      md: "h-1.5 w-1.5",
      lg: "h-2 w-2",
      xl: "h-2.5 w-2.5",
    }

    const colors = {
      default: "bg-primary",
      muted: "bg-muted-foreground",
      white: "bg-white",
    }

    return (
      <div
        ref={ref}
        className={cn("flex space-x-1", className)}
        role="status"
        aria-label="Loading"
        {...props}
      >
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              "rounded-full animate-pulse",
              dotSizes[size],
              colors[variant]
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: "1.4s",
            }}
          />
        ))}
        <span className="sr-only">Loading...</span>
      </div>
    )
  }
)
DotsSpinner.displayName = "DotsSpinner"

// Pulse Spinner Component
interface PulseSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "muted" | "white"
}

const PulseSpinner = React.forwardRef<HTMLDivElement, PulseSpinnerProps>(
  ({ className, size = "md", variant = "default", ...props }, ref) => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-6 w-6",
      lg: "h-8 w-8",
      xl: "h-12 w-12",
    }

    const colors = {
      default: "bg-primary",
      muted: "bg-muted-foreground",
      white: "bg-white",
    }

    return (
      <div
        ref={ref}
        className={cn(
          "rounded-full animate-pulse",
          sizeClasses[size],
          colors[variant],
          className
        )}
        role="status"
        aria-label="Loading"
        {...props}
      >
        <span className="sr-only">Loading...</span>
      </div>
    )
  }
)
PulseSpinner.displayName = "PulseSpinner"

// Loading Overlay Component
interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  spinner?: "default" | "dots" | "pulse"
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "muted" | "white"
  text?: string
  className?: string
  overlayClassName?: string
}

const LoadingOverlay = ({
  isLoading,
  children,
  spinner = "default",
  size = "lg",
  variant = "default",
  text,
  className,
  overlayClassName,
}: LoadingOverlayProps) => {
  const SpinnerComponent = {
    default: Spinner,
    dots: DotsSpinner,
    pulse: PulseSpinner,
  }[spinner]

  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div
          className={cn(
            "absolute inset-0 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm z-50",
            overlayClassName
          )}
        >
          <SpinnerComponent size={size} variant={variant} />
          {text && (
            <p className="mt-2 text-sm text-muted-foreground">{text}</p>
          )}
        </div>
      )}
    </div>
  )
}

export {
  Spinner,
  DotsSpinner,
  PulseSpinner,
  LoadingOverlay,
  spinnerVariants,
  type SpinnerProps,
  type DotsSpinnerProps,
  type PulseSpinnerProps,
  type LoadingOverlayProps,
}