"use client"

import * as React from "react";
import { UploadButton as UTUploadButton } from "@/lib/uploadthing";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Upload, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface UploadButtonProps {
  endpoint: "imageUploader" | "videoUploader" | "mediaUploader";
  onUploadComplete?: (files: Array<{ url: string; fileId: string; fileType?: string }>) => void;
  onUploadError?: (error: Error) => void;
  className?: string;
  disabled?: boolean;
  multiple?: boolean;
}

export function UploadButton({
  endpoint,
  onUploadComplete,
  onUploadError,
  className,
  disabled = false,
  multiple = false,
}: UploadButtonProps) {
  const { toast } = useToast();

  return (
    <UTUploadButton
      endpoint={endpoint}
      onClientUploadComplete={(res) => {
        if (res) {
          const files = res.map((file) => ({
            url: file.url,
            fileId: file.serverData?.fileId || "",
            fileType: file.serverData?.fileType,
          }));
          
          onUploadComplete?.(files);
          
          toast({
            variant: "success",
            title: "Upload Complete",
            description: `Successfully uploaded ${res.length} file${res.length > 1 ? 's' : ''}`,
          });
        }
      }}
      onUploadError={(error: Error) => {
        console.error("Upload error:", error);
        onUploadError?.(error);
        
        toast({
          variant: "error",
          title: "Upload Failed",
          description: error.message || "Failed to upload file",
        });
      }}
      appearance={{
        button: cn(
          "inline-flex items-center justify-center rounded-md text-sm font-medium",
          "bg-primary text-primary-foreground hover:bg-primary/90",
          "h-10 px-4 py-2 transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
          "disabled:pointer-events-none disabled:opacity-50",
          disabled && "opacity-50 pointer-events-none",
          className
        ),
        allowedContent: "text-xs text-muted-foreground mt-2",
      }}
      content={{
        button: (
          <div className="flex items-center space-x-2">
            <Upload className="h-4 w-4" />
            <span>Upload {endpoint === "imageUploader" ? "Images" : endpoint === "videoUploader" ? "Videos" : "Media"}</span>
          </div>
        ),
        allowedContent: `Max ${endpoint === "videoUploader" ? "16MB" : "4MB"} ${endpoint === "imageUploader" ? "images" : endpoint === "videoUploader" ? "videos" : "files"}`,
      }}
    />
  );
}