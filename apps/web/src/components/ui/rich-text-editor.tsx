"use client"

import React, { use<PERSON><PERSON>back, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Editor<PERSON>ontent, Editor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import CharacterCount from '@tiptap/extension-character-count'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { UploadButton } from '@/components/ui/upload-button'
import { MediaPreview } from '@/components/ui/media-preview'
import { getTwitterCharacterCount, validateTweet } from '@/lib/twitter-utils'
import {
  Bold,
  Italic,
  Link as LinkIcon,
  Image as ImageIcon,
  Type,
  Undo,
  Redo,
  X,
  Play,
  FileText,
  AlertCircle,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface MediaFile {
  url: string
  fileId: string
  fileType?: string
  filename?: string
}

interface RichTextEditorProps {
  content?: string
  onChange?: (content: string) => void
  placeholder?: string
  maxLength?: number
  className?: string
  editable?: boolean
  showToolbar?: boolean
  showCharacterCount?: boolean
  onImageUpload?: () => void
  onMediaUpload?: (files: MediaFile[]) => void
  mediaFiles?: MediaFile[]
  onMediaRemove?: (fileId: string) => void
  error?: string
  disabled?: boolean
  autoFocus?: boolean
  onValidationChange?: (validation: any) => void
}

interface MenuBarProps {
  editor: Editor | null
  onImageUpload?: () => void
  onMediaUpload?: (files: MediaFile[]) => void
  mediaFiles?: MediaFile[]
}

const MenuBar = ({ editor, onImageUpload, onMediaUpload, mediaFiles = [] }: MenuBarProps) => {
  if (!editor) {
    return null
  }

  const addLink = useCallback(() => {
    const previousUrl = editor.getAttributes('link').href
    const url = window.prompt('URL', previousUrl)

    // cancelled
    if (url === null) {
      return
    }

    // empty
    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run()
      return
    }

    // update link
    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
  }, [editor])

  return (
    <div className="flex items-center gap-1 p-2 border-b border-border">
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleBold().run()}
        disabled={!editor.can().chain().focus().toggleBold().run()}
        className={cn(
          "h-8 w-8 p-0",
          editor.isActive('bold') && "bg-accent text-accent-foreground"
        )}
      >
        <Bold className="h-4 w-4" />
      </Button>
      
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleItalic().run()}
        disabled={!editor.can().chain().focus().toggleItalic().run()}
        className={cn(
          "h-8 w-8 p-0",
          editor.isActive('italic') && "bg-accent text-accent-foreground"
        )}
      >
        <Italic className="h-4 w-4" />
      </Button>

      <div className="w-px h-6 bg-border mx-1" />

      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={addLink}
        className={cn(
          "h-8 w-8 p-0",
          editor.isActive('link') && "bg-accent text-accent-foreground"
        )}
      >
        <LinkIcon className="h-4 w-4" />
      </Button>

      {onMediaUpload && (
        <UploadButton
          endpoint="mediaUploader"
          onClientUploadComplete={(res) => {
            const files = res.map(file => ({
              url: file.url,
              fileId: file.key,
              fileType: file.type,
              filename: file.name,
            }))
            onMediaUpload(files)
          }}
          onUploadError={(error: Error) => {
            console.error("Upload error:", error)
          }}
          className="h-8 w-8 p-0"
          content={{
            button: <ImageIcon className="h-4 w-4" />,
            allowedContent: "",
          }}
        />
      )}

      {onImageUpload && !onMediaUpload && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onImageUpload}
          className="h-8 w-8 p-0"
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
      )}

      <div className="w-px h-6 bg-border mx-1" />

      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().undo().run()}
        disabled={!editor.can().chain().focus().undo().run()}
        className="h-8 w-8 p-0"
      >
        <Undo className="h-4 w-4" />
      </Button>

      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().redo().run()}
        disabled={!editor.can().chain().focus().redo().run()}
        className="h-8 w-8 p-0"
      >
        <Redo className="h-4 w-4" />
      </Button>
    </div>
  )
}

export const RichTextEditor = React.forwardRef<HTMLDivElement, RichTextEditorProps>(
  ({
    content = '',
    onChange,
    placeholder = "Start typing...",
    maxLength = 280,
    className,
    editable = true,
    showToolbar = true,
    showCharacterCount = true,
    onImageUpload,
    onMediaUpload,
    mediaFiles = [],
    onMediaRemove,
    error,
    disabled = false,
    autoFocus = false,
    onValidationChange,
  }, ref) => {
    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          heading: false, // Disable headings for tweets
          codeBlock: false, // Disable code blocks for tweets
          blockquote: false, // Disable blockquotes for tweets
        }),
        Placeholder.configure({
          placeholder,
        }),
        CharacterCount.configure({
          limit: maxLength,
        }),
        Link.configure({
          openOnClick: false,
          HTMLAttributes: {
            class: 'text-primary underline underline-offset-4',
          },
        }),
        Image.configure({
          HTMLAttributes: {
            class: 'max-w-full h-auto rounded-md',
          },
        }),
      ],
      content,
      editable: editable && !disabled,
      autofocus: autoFocus,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML()
        const text = editor.getText()
        const validation = validateTweet(text, maxLength)
        onChange?.(text)
        onValidationChange?.(validation)
      },
    })

    // Update editor content when prop changes
    useEffect(() => {
      if (editor && content !== editor.getText()) {
        editor.commands.setContent(content)
      }
    }, [content, editor])

    const text = editor?.getText() || ''
    const twitterValidation = validateTweet(text, maxLength)
    const characterCount = twitterValidation.characterCount
    const isOverLimit = twitterValidation.isOverLimit
    const isNearLimit = twitterValidation.isNearLimit
    const remainingChars = twitterValidation.remainingCharacters

    return (
      <div className="space-y-2">
        <div
          ref={ref}
          className={cn(
            "border border-input rounded-md bg-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 transition-colors",
            isOverLimit && "border-red-500 focus-within:ring-red-500",
            error && "border-red-500",
            disabled && "opacity-50 cursor-not-allowed bg-muted",
            className
          )}
        >
        {showToolbar && (
          <MenuBar
            editor={editor}
            onImageUpload={onImageUpload}
            onMediaUpload={onMediaUpload}
            mediaFiles={mediaFiles}
          />
        )}
        
        <EditorContent
          editor={editor}
          className={cn(
            "prose prose-sm max-w-none p-3 min-h-[100px] focus:outline-none",
            "prose-p:my-1 prose-p:leading-relaxed",
            "prose-a:text-primary prose-a:no-underline hover:prose-a:underline",
            !showToolbar && "pt-3"
          )}
        />

        {/* Media Preview Section */}
        {mediaFiles.length > 0 && (
          <div className="p-3 border-t border-border">
            <MediaPreview
              files={mediaFiles}
              onRemove={onMediaRemove}
              layout="grid"
              showControls={true}
              showFileInfo={false}
              maxFiles={4}
            />
          </div>
        )}
        
        {showCharacterCount && (
          <div className="flex items-center justify-between px-3 py-2 border-t border-border text-sm">
            <div className="flex items-center gap-2">
              <Type className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Rich text formatting available
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/* Progress indicator */}
              <div className="w-16 h-1 bg-muted rounded-full overflow-hidden">
                <div
                  className={cn(
                    "h-full transition-all duration-200",
                    isOverLimit ? "bg-red-500" :
                    isNearLimit ? "bg-yellow-500" : "bg-green-500"
                  )}
                  style={{
                    width: `${Math.min(100, (characterCount / maxLength) * 100)}%`
                  }}
                />
              </div>

              {/* Character count */}
              <span className={cn(
                "font-medium tabular-nums",
                isOverLimit ? "text-red-500" :
                isNearLimit ? "text-yellow-600" : "text-muted-foreground"
              )}>
                {isOverLimit ? `-${Math.abs(remainingChars)}` : remainingChars}
              </span>
            </div>
          </div>
        )}
        </div>

        {/* Error and Validation Messages */}
        {(error || twitterValidation.errors.length > 0 || twitterValidation.warnings.length > 0) && (
          <div className="space-y-1">
            {error && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
              </div>
            )}

            {twitterValidation.errors.map((errorMsg, index) => (
              <div key={`error-${index}`} className="flex items-center gap-2 text-sm text-red-600">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{errorMsg}</span>
              </div>
            ))}

            {twitterValidation.warnings.map((warning, index) => (
              <div key={`warning-${index}`} className="flex items-center gap-2 text-sm text-yellow-600">
                <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                <span>{warning}</span>
              </div>
            ))}

            {twitterValidation.isValid && !error && twitterValidation.warnings.length === 0 && characterCount > 0 && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4 flex-shrink-0" />
                <span>Tweet looks good!</span>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }
)

RichTextEditor.displayName = "RichTextEditor"

export default RichTextEditor
