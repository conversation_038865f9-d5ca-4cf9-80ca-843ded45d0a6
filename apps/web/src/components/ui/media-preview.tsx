"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  X, 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize2, 
  Download,
  FileText,
  Image as ImageIcon,
  Video,
  Music,
  File
} from 'lucide-react'

interface MediaFile {
  url: string
  fileId: string
  fileType?: string
  filename?: string
  size?: number
}

interface MediaPreviewProps {
  files: MediaFile[]
  onRemove?: (fileId: string) => void
  maxFiles?: number
  layout?: 'grid' | 'list' | 'compact'
  showControls?: boolean
  showFileInfo?: boolean
  className?: string
}

const formatFileSize = (bytes?: number): string => {
  if (!bytes) return 'Unknown size'
  
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const getFileIcon = (fileType?: string) => {
  if (!fileType) return <File className="h-4 w-4" />
  
  if (fileType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />
  if (fileType.startsWith('video/')) return <Video className="h-4 w-4" />
  if (fileType.startsWith('audio/')) return <Music className="h-4 w-4" />
  return <FileText className="h-4 w-4" />
}

const getFileTypeLabel = (fileType?: string): string => {
  if (!fileType) return 'File'
  
  if (fileType.startsWith('image/')) return 'Image'
  if (fileType.startsWith('video/')) return 'Video'
  if (fileType.startsWith('audio/')) return 'Audio'
  return 'Document'
}

interface MediaItemProps {
  file: MediaFile
  onRemove?: (fileId: string) => void
  layout: 'grid' | 'list' | 'compact'
  showControls: boolean
  showFileInfo: boolean
}

const MediaItem = ({ file, onRemove, layout, showControls, showFileInfo }: MediaItemProps) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [showFullscreen, setShowFullscreen] = useState(false)

  const isImage = file.fileType?.startsWith('image/')
  const isVideo = file.fileType?.startsWith('video/')
  const isAudio = file.fileType?.startsWith('audio/')

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (layout === 'compact') {
    return (
      <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
        <div className="flex-shrink-0">
          {getFileIcon(file.fileType)}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">
            {file.filename || 'Untitled'}
          </p>
          {showFileInfo && (
            <p className="text-xs text-muted-foreground">
              {getFileTypeLabel(file.fileType)} • {formatFileSize(file.size)}
            </p>
          )}
        </div>
        {onRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => onRemove(file.fileId)}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  if (layout === 'list') {
    return (
      <div className="flex items-center gap-3 p-3 border rounded-md">
        <div className="flex-shrink-0">
          {isImage ? (
            <img
              src={file.url}
              alt={file.filename || 'Preview'}
              className="w-12 h-12 object-cover rounded"
            />
          ) : (
            <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
              {getFileIcon(file.fileType)}
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <p className="font-medium truncate">
            {file.filename || 'Untitled'}
          </p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="secondary" className="text-xs">
              {getFileTypeLabel(file.fileType)}
            </Badge>
            {showFileInfo && (
              <span className="text-xs text-muted-foreground">
                {formatFileSize(file.size)}
              </span>
            )}
          </div>
        </div>
        
        {showControls && (
          <div className="flex items-center gap-1">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleDownload}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
            </Button>
            {onRemove && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onRemove(file.fileId)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    )
  }

  // Grid layout (default)
  return (
    <div className="relative group border rounded-md overflow-hidden bg-muted/50">
      {isImage && (
        <div className="aspect-square">
          <img
            src={file.url}
            alt={file.filename || 'Preview'}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      
      {isVideo && (
        <div className="aspect-square relative">
          <video
            src={file.url}
            className="w-full h-full object-cover"
            muted={isMuted}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
          <div className="absolute inset-0 flex items-center justify-center">
            <Button
              type="button"
              variant="secondary"
              size="sm"
              className="opacity-80 hover:opacity-100"
              onClick={() => {
                const video = document.querySelector(`video[src="${file.url}"]`) as HTMLVideoElement
                if (video) {
                  if (isPlaying) {
                    video.pause()
                  } else {
                    video.play()
                  }
                }
              }}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      )}
      
      {isAudio && (
        <div className="aspect-square flex items-center justify-center">
          <div className="text-center">
            <Music className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm font-medium truncate px-2">
              {file.filename || 'Audio file'}
            </p>
          </div>
        </div>
      )}
      
      {!isImage && !isVideo && !isAudio && (
        <div className="aspect-square flex items-center justify-center">
          <div className="text-center">
            {getFileIcon(file.fileType)}
            <p className="text-sm font-medium truncate px-2 mt-2">
              {file.filename || 'File'}
            </p>
          </div>
        </div>
      )}
      
      {/* Overlay controls */}
      {showControls && (
        <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={handleDownload}
            className="h-6 w-6 p-0"
          >
            <Download className="h-3 w-3" />
          </Button>
          {onRemove && (
            <Button
              type="button"
              variant="destructive"
              size="sm"
              onClick={() => onRemove(file.fileId)}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}
      
      {/* File info overlay */}
      {showFileInfo && (
        <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2">
          <p className="text-xs truncate">
            {file.filename || 'Untitled'}
          </p>
          <p className="text-xs opacity-75">
            {getFileTypeLabel(file.fileType)} • {formatFileSize(file.size)}
          </p>
        </div>
      )}
    </div>
  )
}

export const MediaPreview = ({
  files,
  onRemove,
  maxFiles = 4,
  layout = 'grid',
  showControls = true,
  showFileInfo = false,
  className,
}: MediaPreviewProps) => {
  if (files.length === 0) {
    return null
  }

  const displayFiles = files.slice(0, maxFiles)
  const remainingCount = files.length - maxFiles

  return (
    <div className={cn("space-y-2", className)}>
      <div className={cn(
        layout === 'grid' && "grid gap-2",
        layout === 'grid' && displayFiles.length === 1 && "grid-cols-1",
        layout === 'grid' && displayFiles.length === 2 && "grid-cols-2",
        layout === 'grid' && displayFiles.length >= 3 && "grid-cols-2",
        layout === 'list' && "space-y-2",
        layout === 'compact' && "space-y-1"
      )}>
        {displayFiles.map((file) => (
          <MediaItem
            key={file.fileId}
            file={file}
            onRemove={onRemove}
            layout={layout}
            showControls={showControls}
            showFileInfo={showFileInfo}
          />
        ))}
      </div>
      
      {remainingCount > 0 && (
        <div className="text-center">
          <Badge variant="secondary">
            +{remainingCount} more file{remainingCount !== 1 ? 's' : ''}
          </Badge>
        </div>
      )}
    </div>
  )
}

export default MediaPreview
