import * as React from "react"

import { cn } from "@/lib/utils"

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea"> & { error?: string }
>(({ className, error, ...props }, ref) => {
  return (
    <div className="flex flex-col">
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          error ? "border-error" : "",
          className
        )}
        ref={ref}
        {...props}
      />
      {error && <div className="text-error mt-1 text-sm">{error}</div>}
    </div>

  )
})
Textarea.displayName = "Textarea"

export { Textarea }
