"use client"

import * as React from "react"
import * as AvatarPrimitive from "@radix-ui/react-avatar"
import { cva, type VariantProps } from "class-variance-authority"
import { User } from "lucide-react"

import { cn } from "@/lib/utils"

const avatarVariants = cva(
  "relative flex shrink-0 overflow-hidden rounded-full",
  {
    variants: {
      size: {
        xs: "h-6 w-6",
        sm: "h-8 w-8",
        md: "h-10 w-10",
        lg: "h-12 w-12",
        xl: "h-16 w-16",
        "2xl": "h-20 w-20",
        "3xl": "h-24 w-24",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

const Avatar = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root> &
    VariantProps<typeof avatarVariants>
>(({ className, size, ...props }, ref) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn(avatarVariants({ size }), className)}
    {...props}
  />
))
Avatar.displayName = AvatarPrimitive.Root.displayName

const AvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
))
AvatarImage.displayName = AvatarPrimitive.Image.displayName

const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Fallback
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full bg-muted",
      className
    )}
    {...props}
  />
))
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName

// Enhanced Avatar component with user-friendly props
interface UserAvatarProps extends VariantProps<typeof avatarVariants> {
  src?: string
  alt?: string
  name?: string
  className?: string
  showStatus?: boolean
  status?: "online" | "offline" | "away" | "busy"
  fallbackIcon?: React.ComponentType<{ className?: string }>
}

const UserAvatar = React.forwardRef<
  React.ElementRef<typeof Avatar>,
  UserAvatarProps
>(({
  src,
  alt,
  name,
  size = "md",
  className,
  showStatus = false,
  status = "offline",
  fallbackIcon: FallbackIcon = User,
  ...props
}, ref) => {
  // Generate initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const statusColors = {
    online: "bg-green-500",
    offline: "bg-gray-400",
    away: "bg-yellow-500",
    busy: "bg-red-500",
  }

  const statusSizes = {
    xs: "h-1.5 w-1.5",
    sm: "h-2 w-2",
    md: "h-2.5 w-2.5",
    lg: "h-3 w-3",
    xl: "h-3.5 w-3.5",
    "2xl": "h-4 w-4",
    "3xl": "h-5 w-5",
  }

  return (
    <div className="relative inline-block">
      <Avatar ref={ref} size={size} className={className} {...props}>
        {src && (
          <AvatarImage
            src={src}
            alt={alt || name || "Avatar"}
          />
        )}
        <AvatarFallback className="bg-muted text-muted-foreground">
          {name ? (
            <span className="text-sm font-medium">
              {getInitials(name)}
            </span>
          ) : (
            <FallbackIcon className="h-1/2 w-1/2" />
          )}
        </AvatarFallback>
      </Avatar>

      {showStatus && (
        <span
          className={cn(
            "absolute bottom-0 right-0 block rounded-full border-2 border-background",
            statusColors[status],
            statusSizes[size || "md"]
          )}
          aria-label={`Status: ${status}`}
        />
      )}
    </div>
  )
})
UserAvatar.displayName = "UserAvatar"

// Avatar Group component for displaying multiple avatars
interface AvatarGroupProps {
  avatars: Array<{
    src?: string
    alt?: string
    name?: string
  }>
  max?: number
  size?: VariantProps<typeof avatarVariants>["size"]
  className?: string
}

const AvatarGroup = ({
  avatars,
  max = 3,
  size = "md",
  className
}: AvatarGroupProps) => {
  const displayAvatars = avatars.slice(0, max)
  const remainingCount = Math.max(0, avatars.length - max)

  return (
    <div className={cn("flex -space-x-2", className)}>
      {displayAvatars.map((avatar, index) => (
        <UserAvatar
          key={index}
          src={avatar.src}
          alt={avatar.alt}
          name={avatar.name}
          size={size}
          className="border-2 border-background"
        />
      ))}

      {remainingCount > 0 && (
        <Avatar size={size} className="border-2 border-background">
          <AvatarFallback className="bg-muted text-muted-foreground">
            <span className="text-xs font-medium">
              +{remainingCount}
            </span>
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  )
}

export {
  Avatar,
  AvatarImage,
  AvatarFallback,
  UserAvatar,
  AvatarGroup,
  avatarVariants,
  type UserAvatarProps,
  type AvatarGroupProps,
}
