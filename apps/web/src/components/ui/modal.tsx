"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { <PERSON>ertTriangle, CheckCircle, Info, X, XCircle } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"

const modalVariants = cva(
  "fixed left-[50%] top-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
  {
    variants: {
      size: {
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-xl",
        "2xl": "max-w-2xl",
        "3xl": "max-w-3xl",
        "4xl": "max-w-4xl",
        "5xl": "max-w-5xl",
        full: "max-w-[95vw] max-h-[95vh]",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

interface ModalProps extends VariantProps<typeof modalVariants> {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  showCloseButton?: boolean
}

const Modal = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  ModalProps
>(({ 
  open, 
  onOpenChange, 
  title, 
  description, 
  children, 
  size, 
  className,
  showCloseButton = true,
  ...props 
}, ref) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        ref={ref}
        className={cn(modalVariants({ size }), className)}
        {...props}
      >
        {(title || description) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        )}
        {children}
      </DialogContent>
    </Dialog>
  )
})
Modal.displayName = "Modal"

// Confirmation Modal Component
interface ConfirmModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive"
  onConfirm: () => void
  onCancel?: () => void
  loading?: boolean
}

const ConfirmModal = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  onConfirm,
  onCancel,
  loading = false,
}: ConfirmModalProps) => {
  const handleConfirm = () => {
    onConfirm()
    if (!loading) {
      onOpenChange(false)
    }
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange(false)
  }

  return (
    <Modal
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      size="sm"
    >
      <DialogFooter>
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          {cancelText}
        </Button>
        <Button
          variant={variant === "destructive" ? "destructive" : "default"}
          onClick={handleConfirm}
          disabled={loading}
        >
          {loading ? "Loading..." : confirmText}
        </Button>
      </DialogFooter>
    </Modal>
  )
}

// Alert Modal Component
interface AlertModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  type?: "info" | "success" | "warning" | "error"
  actionText?: string
  onAction?: () => void
}

const AlertModal = ({
  open,
  onOpenChange,
  title,
  description,
  type = "info",
  actionText = "OK",
  onAction,
}: AlertModalProps) => {
  const icons = {
    info: Info,
    success: CheckCircle,
    warning: AlertTriangle,
    error: XCircle,
  }

  const iconColors = {
    info: "text-blue-500",
    success: "text-green-500",
    warning: "text-yellow-500",
    error: "text-red-500",
  }

  const Icon = icons[type]

  const handleAction = () => {
    onAction?.()
    onOpenChange(false)
  }

  return (
    <Modal
      open={open}
      onOpenChange={onOpenChange}
      size="sm"
    >
      <div className="flex items-start space-x-3">
        <Icon className={cn("h-6 w-6 mt-0.5", iconColors[type])} />
        <div className="flex-1">
          <DialogHeader className="text-left">
            <DialogTitle>{title}</DialogTitle>
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        </div>
      </div>
      <DialogFooter>
        <Button onClick={handleAction}>
          {actionText}
        </Button>
      </DialogFooter>
    </Modal>
  )
}

// Form Modal Component
interface FormModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  children: React.ReactNode
  submitText?: string
  cancelText?: string
  onSubmit?: () => void
  onCancel?: () => void
  loading?: boolean
  size?: VariantProps<typeof modalVariants>["size"]
}

const FormModal = ({
  open,
  onOpenChange,
  title,
  description,
  children,
  submitText = "Submit",
  cancelText = "Cancel",
  onSubmit,
  onCancel,
  loading = false,
  size = "md",
}: FormModalProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit?.()
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange(false)
  }

  return (
    <Modal
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      size={size}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {children}
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? "Loading..." : submitText}
          </Button>
        </DialogFooter>
      </form>
    </Modal>
  )
}

export { 
  Modal, 
  ConfirmModal, 
  AlertModal, 
  FormModal,
  modalVariants,
  type ModalProps,
  type ConfirmModalProps,
  type AlertModalProps,
  type FormModalProps,
}
