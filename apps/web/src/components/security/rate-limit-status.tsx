import * as React from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Shield, Clock, AlertTriangle } from 'lucide-react'

interface RateLimitStatus {
  type: string
  remaining: number
  resetTime: number
  totalHits: number
}

interface RateLimitData {
  ip: string
  userId?: string
  limits: RateLimitStatus[]
  timestamp: number
}

export function RateLimitStatus() {
  const { data, isLoading, error } = useQuery<RateLimitData>({
    queryKey: ['rate-limit-status'],
    queryFn: async () => {
      const response = await fetch('/api/rate-limit-status')
      if (!response.ok) {
        throw new Error('Failed to fetch rate limit status')
      }
      return response.json()
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Rate Limit Status</span>
          </CardTitle>
          <CardDescription>Loading rate limit information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Rate Limit Status</span>
          </CardTitle>
          <CardDescription>Failed to load rate limit information</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  if (!data) return null

  const formatResetTime = (resetTime: number) => {
    const now = Date.now()
    const diff = resetTime - now
    if (diff <= 0) return 'Reset now'
    
    const minutes = Math.floor(diff / 60000)
    const seconds = Math.floor((diff % 60000) / 1000)
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`
    }
    return `${seconds}s`
  }

  const getStatusColor = (remaining: number, total: number) => {
    const percentage = (remaining / total) * 100
    if (percentage > 50) return 'text-green-600'
    if (percentage > 20) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shield className="h-5 w-5" />
          <span>Rate Limit Status</span>
        </CardTitle>
        <CardDescription>
          Current rate limiting status for IP: {data.ip}
          {data.userId && ` (User: ${data.userId.slice(0, 8)}...)`}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.limits.map((limit) => {
          const maxRequests = limit.totalHits + limit.remaining
          const usagePercentage = maxRequests > 0 ? ((limit.totalHits / maxRequests) * 100) : 0
          
          return (
            <div key={limit.type} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="capitalize">
                    {limit.type}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {limit.totalHits}/{maxRequests} requests
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Reset in {formatResetTime(limit.resetTime)}
                  </span>
                </div>
              </div>
              
              <div className="space-y-1">
                <Progress value={usagePercentage} className="h-2" />
                <div className="flex justify-between text-xs">
                  <span className={getStatusColor(limit.remaining, maxRequests)}>
                    {limit.remaining} remaining
                  </span>
                  <span className="text-muted-foreground">
                    {usagePercentage.toFixed(1)}% used
                  </span>
                </div>
              </div>
            </div>
          )
        })}
        
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            Last updated: {new Date(data.timestamp).toLocaleTimeString()}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}