import * as React from 'react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useCSRF, useCSRFProtectedFetch } from '@/hooks/use-csrf'
import { CSRFForm } from '@/components/ui/csrf-form'
import { useToast } from '@/hooks/use-toast'
import { Shield, AlertTriangle, CheckCircle, Clock } from 'lucide-react'

interface TestResult {
  method: string
  status: number
  success: boolean
  message: string
  timestamp: string
}

export function SecurityTest() {
  const [results, setResults] = useState<TestResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [testData, setTestData] = useState('{"test": "data"}')
  
  const { token, isLoading: csrfLoading, getCSRFHeaders } = useCSRF()
  const csrfFetch = useCSRFProtectedFetch()
  const { toast } = useToast()

  const addResult = (result: TestResult) => {
    setResults(prev => [result, ...prev.slice(0, 9)]) // Keep last 10 results
  }

  const testEndpoint = async (method: string, includeCSRF: boolean = true) => {
    setIsLoading(true)
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(includeCSRF ? getCSRFHeaders() : {}),
        },
      }

      if (['POST', 'PUT', 'PATCH'].includes(method)) {
        options.body = testData
      }

      const response = await fetch('/api/test-security', options)
      const data = await response.json()

      const result: TestResult = {
        method: `${method}${includeCSRF ? ' (with CSRF)' : ' (no CSRF)'}`,
        status: response.status,
        success: response.ok,
        message: data.message || data.error || 'Unknown response',
        timestamp: new Date().toLocaleTimeString(),
      }

      addResult(result)

      if (response.ok) {
        toast({
          variant: 'success',
          title: 'Request Successful',
          description: `${method} request completed successfully`,
        })
      } else {
        toast({
          variant: 'error',
          title: 'Request Failed',
          description: `${method} request failed: ${data.error}`,
        })
      }
    } catch (error) {
      const result: TestResult = {
        method: `${method}${includeCSRF ? ' (with CSRF)' : ' (no CSRF)'}`,
        status: 0,
        success: false,
        message: error instanceof Error ? error.message : 'Network error',
        timestamp: new Date().toLocaleTimeString(),
      }

      addResult(result)

      toast({
        variant: 'error',
        title: 'Request Error',
        description: 'Network error occurred',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testRateLimit = async () => {
    setIsLoading(true)
    const promises = []
    
    // Send 10 rapid requests to test rate limiting
    for (let i = 0; i < 10; i++) {
      promises.push(
        fetch('/api/test-security', {
          method: 'GET',
          headers: getCSRFHeaders(),
        }).then(response => ({
          attempt: i + 1,
          status: response.status,
          success: response.ok,
        }))
      )
    }

    try {
      const responses = await Promise.all(promises)
      const successCount = responses.filter(r => r.success).length
      const rateLimitedCount = responses.filter(r => r.status === 429).length

      addResult({
        method: 'Rate Limit Test',
        status: 200,
        success: true,
        message: `${successCount} successful, ${rateLimitedCount} rate limited`,
        timestamp: new Date().toLocaleTimeString(),
      })

      toast({
        variant: rateLimitedCount > 0 ? 'warning' : 'info',
        title: 'Rate Limit Test Complete',
        description: `${successCount}/${responses.length} requests succeeded`,
      })
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Rate Limit Test Failed',
        description: 'Failed to complete rate limit test',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const clearResults = () => {
    setResults([])
  }

  return (
    <div className="space-y-6">
      {/* CSRF Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>CSRF Protection Status</span>
          </CardTitle>
          <CardDescription>Current CSRF token status and configuration</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">CSRF Token:</span>
              <Badge variant={token ? 'default' : 'destructive'}>
                {csrfLoading ? 'Loading...' : token ? 'Active' : 'Missing'}
              </Badge>
            </div>
            {token && (
              <div className="space-y-2">
                <span className="text-sm font-medium">Token (first 16 chars):</span>
                <code className="block p-2 bg-muted rounded text-xs font-mono">
                  {token.substring(0, 16)}...
                </code>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Security Tests</CardTitle>
          <CardDescription>Test various security features and protections</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Test Data (JSON):</label>
            <Input
              value={testData}
              onChange={(e) => setTestData(e.target.value)}
              placeholder='{"test": "data"}'
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button
              onClick={() => testEndpoint('GET')}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              GET
            </Button>
            <Button
              onClick={() => testEndpoint('POST')}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              POST
            </Button>
            <Button
              onClick={() => testEndpoint('PUT')}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              PUT
            </Button>
            <Button
              onClick={() => testEndpoint('DELETE')}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              DELETE
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Button
              onClick={() => testEndpoint('POST', false)}
              disabled={isLoading}
              variant="destructive"
              size="sm"
            >
              POST (No CSRF)
            </Button>
            <Button
              onClick={testRateLimit}
              disabled={isLoading}
              variant="secondary"
              size="sm"
            >
              Test Rate Limit
            </Button>
          </div>

          <div className="flex justify-between">
            <Button
              onClick={clearResults}
              variant="ghost"
              size="sm"
            >
              Clear Results
            </Button>
            <span className="text-sm text-muted-foreground">
              {results.length} test results
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>Recent security test results</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {results.map((result, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
                >
                  <div className="flex items-center space-x-3">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                    <div>
                      <p className="text-sm font-medium">{result.method}</p>
                      <p className="text-xs text-muted-foreground">{result.message}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={result.success ? 'default' : 'destructive'}
                    >
                      {result.status}
                    </Badge>
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{result.timestamp}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* CSRF Form Example */}
      <Card>
        <CardHeader>
          <CardTitle>CSRF Protected Form</CardTitle>
          <CardDescription>Example form with automatic CSRF protection</CardDescription>
        </CardHeader>
        <CardContent>
          <CSRFForm
            onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.currentTarget)
              const data = Object.fromEntries(formData.entries())
              
              toast({
                variant: 'success',
                title: 'Form Submitted',
                description: `Form data: ${JSON.stringify(data)}`,
              })
            }}
            className="space-y-4"
          >
            <div className="space-y-2">
              <label className="text-sm font-medium">Example Field:</label>
              <Input name="example" placeholder="Enter some text..." />
            </div>
            <Button type="submit">Submit Form</Button>
          </CSRFForm>
        </CardContent>
      </Card>
    </div>
  )
}