import * as React from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { Plus, X } from 'lucide-react'
import { createAgentSchema, updateAgentSchema } from '@/lib/validations/agent'
import { useCreateAgent, useUpdateAgent } from '@/hooks/use-agents'
import type { Agent, CreateAgentData, UpdateAgentData } from '@/types/agent'
import { AI_PROVIDERS } from '@/types/agent'
import { z } from 'zod'
import { toast } from 'sonner'

interface AgentFormProps {
  agent?: Agent
  open: boolean
  onOpenChange: (open: boolean) => void
}

type FormData = z.infer<typeof createAgentSchema>

export function AgentForm({ agent, open, onOpenChange }: AgentFormProps) {
  const isEditing = !!agent
  const createAgent = useCreateAgent()
  const updateAgent = useUpdateAgent()

  const form = useForm<FormData>({
    resolver: zodResolver(isEditing ? updateAgentSchema : createAgentSchema),
    defaultValues: agent ? {
      name: agent.name,
      description: agent.description,
      personaData: agent.personaData,
      aiProvider: agent.aiProvider,
      aiModel: agent.aiModel,
      maxDailyTweets: agent.maxDailyTweets,
    } : {
      name: '',
      description: '',
      personaData: {
        personality: '',
        tone: '',
        topics: [''],
        writingStyle: '',
        restrictions: [''],
        expertise: [''],
        communicationStyle: '',
        targetAudience: '',
        contentThemes: [''],
        avoidTopics: [''],
      },
      aiProvider: 'openai',
      aiModel: 'gpt-4',
      maxDailyTweets: 10,
    },
  })

  const {
    fields: topicFields,
    append: appendTopic,
    remove: removeTopic,
  } = useFieldArray({
    control: form.control,
    name: 'personaData.topics',
  })

  const {
    fields: restrictionFields,
    append: appendRestriction,
    remove: removeRestriction,
  } = useFieldArray({
    control: form.control,
    name: 'personaData.restrictions',
  })

  const {
    fields: expertiseFields,
    append: appendExpertise,
    remove: removeExpertise,
  } = useFieldArray({
    control: form.control,
    name: 'personaData.expertise',
  })

  const {
    fields: themeFields,
    append: appendTheme,
    remove: removeTheme,
  } = useFieldArray({
    control: form.control,
    name: 'personaData.contentThemes',
  })

  const {
    fields: avoidFields,
    append: appendAvoid,
    remove: removeAvoid,
  } = useFieldArray({
    control: form.control,
    name: 'personaData.avoidTopics',
  })

  const selectedProvider = form.watch('aiProvider')
  const availableModels = AI_PROVIDERS[selectedProvider]?.models || []

  const onSubmit = async (data: FormData) => {
    let personaDefinition
    if (data.personaDefinition && data.personaDefinition[0]) {
      try {
        const file = data.personaDefinition[0]
        const fileContent = await file.text()
        personaDefinition = JSON.parse(fileContent)
      } catch (error) {
        console.error('Error parsing persona definition file:', error)
        toast.error('Invalid persona definition file. Please upload a valid JSON file.')
        return
      }
    }

    const finalData = { ...data, personaDefinition }
    if (isEditing && agent) {
      updateAgent.mutate(
        { id: agent.id, data: finalData as UpdateAgentData },
        {
          onSuccess: () => {
            onOpenChange(false)
            form.reset()
          },
        }
      )
    } else {
      createAgent.mutate(finalData as CreateAgentData, {
        onSuccess: () => {
          onOpenChange(false)
          form.reset()
        },
      })
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    form.reset()
  }

  const isLoading = createAgent.isPending || updateAgent.isPending

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Agent' : 'Create New Agent'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update your AI agent configuration and persona.'
              : 'Configure your AI agent with a unique personality and behavior.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>

              <div className="space-y-2">
                <Label htmlFor="personaDefinition">Persona Definition (JSON)</Label>
                <Input
                  id="personaDefinition"
                  type="file"
                  {...form.register('personaDefinition')}
                  accept=".json"
                />
              </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Agent Name</Label>
                <Input
                  id="name"
                  {...form.register('name')}
                  placeholder="e.g., Tech Guru"
                  error={form.formState.errors.name?.message}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maxDailyTweets">Daily Tweet Limit</Label>
                <Input
                  id="maxDailyTweets"
                  type="number"
                  min="1"
                  max="100"
                  {...form.register('maxDailyTweets', { valueAsNumber: true })}
                  error={form.formState.errors.maxDailyTweets?.message}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register('description')}
                placeholder="Describe what this agent does and its purpose..."
                rows={3}
                error={form.formState.errors.description?.message}
              />
            </div>
          </div>

          <Separator />

          {/* AI Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">AI Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>AI Provider</Label>
                <Select
                  value={form.watch('aiProvider')}
                  onValueChange={(value: 'openai' | 'google') => {
                    form.setValue('aiProvider', value)
                    // Reset model when provider changes
                    const firstModel = AI_PROVIDERS[value]?.models[0]
                    if (firstModel) {
                      form.setValue('aiModel', firstModel)
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(AI_PROVIDERS).map(([key, provider]) => (
                      <SelectItem key={key} value={key}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>AI Model</Label>
                <Select
                  value={form.watch('aiModel')}
                  onValueChange={(value) => form.setValue('aiModel', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableModels.map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* Persona Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Persona Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="personality">Personality</Label>
                <Textarea
                  id="personality"
                  {...form.register('personaData.personality')}
                  placeholder="e.g., Enthusiastic, knowledgeable, helpful..."
                  rows={3}
                  error={form.formState.errors.personaData?.personality?.message}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tone">Tone</Label>
                <Input
                  id="tone"
                  {...form.register('personaData.tone')}
                  placeholder="e.g., Professional, casual, friendly"
                  error={form.formState.errors.personaData?.tone?.message}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="writingStyle">Writing Style</Label>
              <Textarea
                id="writingStyle"
                {...form.register('personaData.writingStyle')}
                placeholder="Describe how the agent should write content..."
                rows={3}
                error={form.formState.errors.personaData?.writingStyle?.message}
              />
            </div>

            {/* Topics */}
            <div className="space-y-2">
              <Label>Topics of Interest</Label>
              <div className="space-y-2">
                {topicFields.map((field, index) => (
                  <div key={field.id} className="flex gap-2">
                    <Input
                      {...form.register(`personaData.topics.${index}` as const)}
                      placeholder="e.g., Technology, AI, Programming"
                    />
                    {topicFields.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => removeTopic(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendTopic('')}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Topic
                </Button>
              </div>
            </div>

            {/* Restrictions */}
            <div className="space-y-2">
              <Label>Content Restrictions</Label>
              <div className="space-y-2">
                {restrictionFields.map((field, index) => (
                  <div key={field.id} className="flex gap-2">
                    <Input
                      {...form.register(`personaData.restrictions.${index}` as const)}
                      placeholder="e.g., No political content, No offensive language"
                    />
                    {restrictionFields.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => removeRestriction(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendRestriction('')}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Restriction
                </Button>
              </div>
            </div>
          </div>

          <Separator />

          {/* Advanced Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Advanced Configuration (Optional)</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="communicationStyle">Communication Style</Label>
                <Input
                  id="communicationStyle"
                  {...form.register('personaData.communicationStyle')}
                  placeholder="e.g., Direct, storytelling, conversational"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetAudience">Target Audience</Label>
                <Input
                  id="targetAudience"
                  {...form.register('personaData.targetAudience')}
                  placeholder="e.g., Developers, entrepreneurs, students"
                />
              </div>
            </div>

            {/* Expertise Areas */}
            <div className="space-y-2">
              <Label>Areas of Expertise</Label>
              <div className="space-y-2">
                {expertiseFields.map((field, index) => (
                  <div key={field.id} className="flex gap-2">
                    <Input
                      {...form.register(`personaData.expertise.${index}` as const)}
                      placeholder="e.g., Machine Learning, Web Development"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeExpertise(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendExpertise('')}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Expertise
                </Button>
              </div>
            </div>

            {/* Content Themes */}
            <div className="space-y-2">
              <Label>Content Themes</Label>
              <div className="space-y-2">
                {themeFields.map((field, index) => (
                  <div key={field.id} className="flex gap-2">
                    <Input
                      {...form.register(`personaData.contentThemes.${index}` as const)}
                      placeholder="e.g., Tutorials, News, Tips"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeTheme(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendTheme('')}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Theme
                </Button>
              </div>
            </div>

            {/* Avoid Topics */}
            <div className="space-y-2">
              <Label>Topics to Avoid</Label>
              <div className="space-y-2">
                {avoidFields.map((field, index) => (
                  <div key={field.id} className="flex gap-2">
                    <Input
                      {...form.register(`personaData.avoidTopics.${index}` as const)}
                      placeholder="e.g., Politics, Controversial topics"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeAvoid(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendAvoid('')}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Topic to Avoid
                </Button>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" loading={isLoading}>
              {isEditing ? 'Update Agent' : 'Create Agent'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}