import * as React from 'react'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Spinner } from '@/components/ui/spinner'
import { ScheduledTweetCard } from './scheduled-tweet-card'
import { useScheduledTweets } from '@/hooks/use-schedule'
import { Calendar, Clock, CheckCircle, XCircle } from 'lucide-react'
import type { ScheduledTweet } from '@/types/schedule'

export function ScheduledTweetsList() {
  const [page, setPage] = useState(1)
  const [status, setStatus] = useState<'scheduled' | 'published' | 'failed'>('scheduled')

  const { data: tweetsData, isLoading, error } = useScheduledTweets({
    page,
    limit: 12,
    status,
  })

  const handleStatusFilter = (value: string) => {
    setStatus(value as 'scheduled' | 'published' | 'failed')
    setPage(1)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'published':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Calendar className="h-4 w-4" />
    }
  }

  const getStatusStats = () => {
    if (!tweetsData) return null

    return {
      scheduled: tweetsData.tweets.filter(t => t.status === 'scheduled').length,
      published: tweetsData.tweets.filter(t => t.status === 'published').length,
      failed: tweetsData.tweets.filter(t => t.status === 'failed').length,
    }
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-500 mb-2">Failed to load scheduled tweets</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Scheduled Tweets</h1>
          <p className="text-muted-foreground">Manage your scheduled and published tweets</p>
        </div>
      </div>

      {/* Status Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filter by Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Select value={status} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="scheduled">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <span>Scheduled</span>
                  </div>
                </SelectItem>
                <SelectItem value="published">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Published</span>
                  </div>
                </SelectItem>
                <SelectItem value="failed">
                  <div className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Failed</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tweets Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Spinner size="lg" />
        </div>
      ) : tweetsData?.tweets.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            {getStatusIcon(status)}
            <h3 className="text-lg font-medium mb-2 mt-4">No {status} tweets</h3>
            <p className="text-muted-foreground text-center mb-4">
              {status === 'scheduled' 
                ? 'You have no tweets scheduled for publishing.'
                : status === 'published'
                ? 'No tweets have been published yet.'
                : 'No failed tweets to show.'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {tweetsData?.tweets.map((tweet) => (
              <ScheduledTweetCard
                key={tweet.id}
                tweet={tweet}
              />
            ))}
          </div>

          {/* Pagination */}
          {tweetsData && tweetsData.pagination.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: tweetsData.pagination.totalPages }, (_, i) => i + 1)
                  .filter(pageNum => {
                    const current = page
                    const total = tweetsData.pagination.totalPages
                    return (
                      pageNum === 1 ||
                      pageNum === total ||
                      (pageNum >= current - 1 && pageNum <= current + 1)
                    )
                  })
                  .map((pageNum, index, array) => (
                    <React.Fragment key={pageNum}>
                      {index > 0 && array[index - 1] !== pageNum - 1 && (
                        <span className="px-2 text-muted-foreground">...</span>
                      )}
                      <Button
                        variant={pageNum === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    </React.Fragment>
                  ))}
              </div>

              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === tweetsData.pagination.totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}