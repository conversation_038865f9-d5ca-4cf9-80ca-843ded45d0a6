'use client'

import * as React from 'react'
import { useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Plus, 
  X, 
  Calendar, 
  Clock, 
  Upload,
  Download,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { useTwitterAccounts } from '@/hooks/use-twitter-accounts'
import { useAgents } from '@/hooks/use-agents'
import { TimezoneSelector } from './timezone-selector'

// Bulk schedule schema
const bulkScheduleSchema = z.object({
  twitterAccountId: z.string().min(1, 'Twitter account is required'),
  agentId: z.string().min(1, 'Agent is required'),
  timezone: z.string().default('UTC'),
  tweets: z.array(z.object({
    content: z.string().min(1, 'Content is required').max(280, 'Content too long'),
    scheduledFor: z.string().min(1, 'Schedule time is required'),
    mediaUrls: z.array(z.string()).optional(),
  })).min(1, 'At least one tweet is required'),
  schedulingOptions: z.object({
    intervalMinutes: z.number().min(1).optional(),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
    skipWeekends: z.boolean().default(false),
    skipHours: z.array(z.number()).optional(), // Hours to skip (0-23)
  }).optional(),
})

type BulkScheduleFormData = z.infer<typeof bulkScheduleSchema>

interface BulkScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialTweets?: Array<{ content: string; mediaUrls?: string[] }>
}

export function BulkScheduleDialog({ 
  open, 
  onOpenChange, 
  initialTweets = [] 
}: BulkScheduleDialogProps) {
  const [schedulingMode, setSchedulingMode] = useState<'manual' | 'auto'>('manual')
  const [importMode, setImportMode] = useState(false)
  
  const { data: twitterAccountsData } = useTwitterAccounts()
  const { data: agentsData } = useAgents({ limit: 100 })

  const form = useForm<BulkScheduleFormData>({
    resolver: zodResolver(bulkScheduleSchema),
    defaultValues: {
      tweets: initialTweets.length > 0 
        ? initialTweets.map(tweet => ({ ...tweet, scheduledFor: '' }))
        : [{ content: '', scheduledFor: '', mediaUrls: [] }],
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      schedulingOptions: {
        intervalMinutes: 60,
        skipWeekends: false,
        skipHours: [],
      },
    },
  })

  const {
    fields: tweetFields,
    append: appendTweet,
    remove: removeTweet,
  } = useFieldArray({
    control: form.control,
    name: 'tweets',
  })

  const onSubmit = (data: BulkScheduleFormData) => {
    console.log('Bulk schedule data:', data)
    // TODO: Implement bulk scheduling API call
    onOpenChange(false)
  }

  const addTweet = () => {
    appendTweet({ content: '', scheduledFor: '', mediaUrls: [] })
  }

  const generateAutoSchedule = () => {
    const options = form.getValues('schedulingOptions')
    if (!options?.startTime || !options?.intervalMinutes) return

    const startDate = new Date(options.startTime)
    const tweets = form.getValues('tweets')
    
    tweets.forEach((_, index) => {
      const scheduleTime = new Date(startDate)
      scheduleTime.setMinutes(scheduleTime.getMinutes() + (index * options.intervalMinutes!))
      
      // Skip weekends if enabled
      if (options.skipWeekends && (scheduleTime.getDay() === 0 || scheduleTime.getDay() === 6)) {
        scheduleTime.setDate(scheduleTime.getDate() + (scheduleTime.getDay() === 0 ? 1 : 2))
      }
      
      // Skip specified hours
      if (options.skipHours?.includes(scheduleTime.getHours())) {
        scheduleTime.setHours(scheduleTime.getHours() + 1)
      }
      
      form.setValue(`tweets.${index}.scheduledFor`, scheduleTime.toISOString().slice(0, 16))
    })
  }

  const importFromCSV = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const csv = e.target?.result as string
      const lines = csv.split('\n').filter(line => line.trim())
      const tweets = lines.slice(1).map(line => {
        const [content, scheduledFor] = line.split(',').map(cell => cell.trim().replace(/"/g, ''))
        return { content, scheduledFor, mediaUrls: [] }
      })
      
      form.setValue('tweets', tweets)
    }
    reader.readAsText(file)
  }

  const exportTemplate = () => {
    const csv = 'Content,Scheduled For\n"Your tweet content here","2024-01-01T12:00"\n"Another tweet","2024-01-01T13:00"'
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'bulk-schedule-template.csv'
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Bulk Schedule Tweets</span>
          </DialogTitle>
          <DialogDescription>
            Schedule multiple tweets at once with automatic timing or manual scheduling.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Account & Agent Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="twitterAccountId">Twitter Account</Label>
              <Select
                value={form.watch('twitterAccountId')}
                onValueChange={(value) => form.setValue('twitterAccountId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select account..." />
                </SelectTrigger>
                <SelectContent>
                  {twitterAccountsData?.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      @{account.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.twitterAccountId && (
                <p className="text-sm text-red-500">{form.formState.errors.twitterAccountId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="agentId">AI Agent</Label>
              <Select
                value={form.watch('agentId')}
                onValueChange={(value) => form.setValue('agentId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select agent..." />
                </SelectTrigger>
                <SelectContent>
                  {agentsData?.agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.agentId && (
                <p className="text-sm text-red-500">{form.formState.errors.agentId.message}</p>
              )}
            </div>

            <TimezoneSelector
              value={form.watch('timezone')}
              onValueChange={(value) => form.setValue('timezone', value)}
            />
          </div>

          {/* Import/Export Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Import/Export Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('csv-import')?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import CSV
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={exportTemplate}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Template
                </Button>
                <input
                  id="csv-import"
                  type="file"
                  accept=".csv"
                  className="hidden"
                  onChange={importFromCSV}
                />
              </div>
            </CardContent>
          </Card>

          {/* Scheduling Mode */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Scheduling Mode</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant={schedulingMode === 'manual' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSchedulingMode('manual')}
                >
                  Manual Scheduling
                </Button>
                <Button
                  type="button"
                  variant={schedulingMode === 'auto' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSchedulingMode('auto')}
                >
                  Auto Scheduling
                </Button>
              </div>

              {schedulingMode === 'auto' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="startTime">Start Time</Label>
                    <Input
                      id="startTime"
                      type="datetime-local"
                      {...form.register('schedulingOptions.startTime')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="intervalMinutes">Interval (minutes)</Label>
                    <Input
                      id="intervalMinutes"
                      type="number"
                      min="1"
                      {...form.register('schedulingOptions.intervalMinutes', { valueAsNumber: true })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Options</Label>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          {...form.register('schedulingOptions.skipWeekends')}
                        />
                        <span className="text-sm">Skip weekends</span>
                      </label>
                    </div>
                  </div>
                  <div className="md:col-span-3">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={generateAutoSchedule}
                    >
                      <Clock className="h-4 w-4 mr-2" />
                      Generate Schedule
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Separator />

          {/* Tweet List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Tweets ({tweetFields.length})</h3>
              <Button type="button" variant="outline" size="sm" onClick={addTweet}>
                <Plus className="h-4 w-4 mr-2" />
                Add Tweet
              </Button>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {tweetFields.map((field, index) => (
                <Card key={field.id} className="relative">
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <Badge variant="outline">Tweet {index + 1}</Badge>
                        {tweetFields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTweet(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor={`tweets.${index}.content`}>Content</Label>
                        <Textarea
                          {...form.register(`tweets.${index}.content`)}
                          placeholder="What's happening?"
                          className="min-h-[80px]"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>
                            {form.watch(`tweets.${index}.content`)?.length || 0}/280
                          </span>
                          {form.formState.errors.tweets?.[index]?.content && (
                            <span className="text-red-500">
                              {form.formState.errors.tweets[index]?.content?.message}
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`tweets.${index}.scheduledFor`}>Schedule For</Label>
                        <Input
                          type="datetime-local"
                          {...form.register(`tweets.${index}.scheduledFor`)}
                        />
                        {form.formState.errors.tweets?.[index]?.scheduledFor && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.tweets[index]?.scheduledFor?.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">
              Schedule {tweetFields.length} Tweet{tweetFields.length !== 1 ? 's' : ''}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
