'use client'

import * as React from 'react'
import { useState, useMemo } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Clock, Search } from 'lucide-react'

// Common timezones grouped by region
const TIMEZONE_GROUPS = {
  'Popular': [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'America/Chicago',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney',
  ],
  'Americas': [
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'America/Toronto',
    'America/Vancouver',
    'America/Mexico_City',
    'America/Sao_Paulo',
    'America/Argentina/Buenos_Aires',
  ],
  'Europe': [
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Europe/Rome',
    'Europe/Madrid',
    'Europe/Amsterdam',
    'Europe/Stockholm',
    'Europe/Moscow',
    'Europe/Istanbul',
  ],
  'Asia': [
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Asia/Hong_Kong',
    'Asia/Singapore',
    'Asia/Mumbai',
    'Asia/Dubai',
    'Asia/Seoul',
    'Asia/Bangkok',
    'Asia/Jakarta',
  ],
  'Pacific': [
    'Australia/Sydney',
    'Australia/Melbourne',
    'Pacific/Auckland',
    'Pacific/Honolulu',
    'Pacific/Fiji',
  ],
  'Africa': [
    'Africa/Cairo',
    'Africa/Johannesburg',
    'Africa/Lagos',
    'Africa/Casablanca',
  ],
}

interface TimezoneSelectorProps {
  value?: string
  onValueChange: (timezone: string) => void
  className?: string
}

export function TimezoneSelector({ value, onValueChange, className }: TimezoneSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  
  // Get user's current timezone as default
  const userTimezone = useMemo(() => {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone
    } catch {
      return 'UTC'
    }
  }, [])

  // Format timezone display name
  const formatTimezone = (timezone: string) => {
    try {
      const now = new Date()
      const formatter = new Intl.DateTimeFormat('en', {
        timeZone: timezone,
        timeZoneName: 'short',
        hour: '2-digit',
        minute: '2-digit',
      })
      
      const parts = formatter.formatToParts(now)
      const time = parts.find(p => p.type === 'hour')?.value + ':' + 
                   parts.find(p => p.type === 'minute')?.value
      const tzName = parts.find(p => p.type === 'timeZoneName')?.value
      
      return {
        name: timezone.replace(/_/g, ' ').split('/').pop() || timezone,
        offset: tzName,
        time: time,
        full: timezone,
      }
    } catch {
      return {
        name: timezone,
        offset: '',
        time: '',
        full: timezone,
      }
    }
  }

  // Filter timezones based on search
  const filteredTimezones = useMemo(() => {
    if (!searchQuery) return TIMEZONE_GROUPS

    const query = searchQuery.toLowerCase()
    const filtered: typeof TIMEZONE_GROUPS = {}

    Object.entries(TIMEZONE_GROUPS).forEach(([group, timezones]) => {
      const matchingTimezones = timezones.filter(tz => 
        tz.toLowerCase().includes(query) ||
        formatTimezone(tz).name.toLowerCase().includes(query)
      )
      
      if (matchingTimezones.length > 0) {
        filtered[group] = matchingTimezones
      }
    })

    return filtered
  }, [searchQuery])

  const currentTimezone = value || userTimezone
  const currentTimezoneInfo = formatTimezone(currentTimezone)

  return (
    <div className={className}>
      <Label htmlFor="timezone">Timezone</Label>
      <Select value={currentTimezone} onValueChange={onValueChange}>
        <SelectTrigger>
          <SelectValue>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>{currentTimezoneInfo.name}</span>
              <Badge variant="outline" className="text-xs">
                {currentTimezoneInfo.offset} {currentTimezoneInfo.time}
              </Badge>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="max-h-80">
          {/* Search */}
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search timezones..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 h-8"
              />
            </div>
          </div>

          {/* Current timezone highlight */}
          {!searchQuery && (
            <>
              <div className="p-2 text-xs font-medium text-muted-foreground">
                Current
              </div>
              <SelectItem value={userTimezone}>
                <div className="flex items-center justify-between w-full">
                  <span>{formatTimezone(userTimezone).name}</span>
                  <div className="flex items-center space-x-2 ml-4">
                    <Badge variant="secondary" className="text-xs">
                      {formatTimezone(userTimezone).offset}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatTimezone(userTimezone).time}
                    </span>
                  </div>
                </div>
              </SelectItem>
              <div className="border-t my-1" />
            </>
          )}

          {/* Timezone groups */}
          {Object.entries(filteredTimezones).map(([group, timezones]) => (
            <div key={group}>
              <div className="p-2 text-xs font-medium text-muted-foreground">
                {group}
              </div>
              {timezones.map((timezone) => {
                const info = formatTimezone(timezone)
                return (
                  <SelectItem key={timezone} value={timezone}>
                    <div className="flex items-center justify-between w-full">
                      <span>{info.name}</span>
                      <div className="flex items-center space-x-2 ml-4">
                        <Badge variant="outline" className="text-xs">
                          {info.offset}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {info.time}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                )
              })}
            </div>
          ))}

          {Object.keys(filteredTimezones).length === 0 && (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No timezones found matching "{searchQuery}"
            </div>
          )}
        </SelectContent>
      </Select>
    </div>
  )
}

// Utility function to convert time between timezones
export function convertTimezone(
  dateTime: string | Date,
  fromTimezone: string,
  toTimezone: string
): Date {
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  
  // Create a date in the source timezone
  const sourceDate = new Date(date.toLocaleString('en-US', { timeZone: fromTimezone }))
  
  // Get the offset difference
  const sourceOffset = sourceDate.getTimezoneOffset()
  const targetDate = new Date(date.toLocaleString('en-US', { timeZone: toTimezone }))
  const targetOffset = targetDate.getTimezoneOffset()
  
  // Apply the offset difference
  const offsetDiff = targetOffset - sourceOffset
  const convertedDate = new Date(date.getTime() + (offsetDiff * 60 * 1000))
  
  return convertedDate
}

// Utility function to format date in a specific timezone
export function formatDateInTimezone(
  date: Date,
  timezone: string,
  options?: Intl.DateTimeFormatOptions
): string {
  return new Intl.DateTimeFormat('en-US', {
    timeZone: timezone,
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  }).format(date)
}
