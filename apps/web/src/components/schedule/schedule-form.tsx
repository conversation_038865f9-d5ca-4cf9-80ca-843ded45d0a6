import * as React from 'react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Calendar, Clock } from 'lucide-react'
import { scheduleSchema } from '@/lib/validations/schedule'
import { useScheduleTweet } from '@/hooks/use-schedule'
import { useTwitterAccounts } from '@/hooks/use-twitter-accounts'
import type { ScheduleData } from '@/types/schedule'
import type { Draft } from '@/types/draft'
import { z } from 'zod'

interface ScheduleFormProps {
  draft: Draft
  open: boolean
  onOpenChange: (open: boolean) => void
}

type FormData = z.infer<typeof scheduleSchema>

export function ScheduleForm({ draft, open, onOpenChange }: ScheduleFormProps) {
  const scheduleTweet = useScheduleTweet()
  const { data: twitterAccounts = [] } = useTwitterAccounts()

  const form = useForm<FormData>({
    resolver: zodResolver(scheduleSchema),
    defaultValues: {
      draftId: draft.id,
      scheduledFor: '',
      twitterAccountId: twitterAccounts[0]?.id || '',
    },
  })

  const onSubmit = (data: FormData) => {
    scheduleTweet.mutate(data, {
      onSuccess: () => {
        onOpenChange(false)
        form.reset()
      },
    })
  }

  const handleClose = () => {
    onOpenChange(false)
    form.reset()
  }

  // Generate datetime-local input value
  const getMinDateTime = () => {
    const now = new Date()
    now.setMinutes(now.getMinutes() + 5) // Minimum 5 minutes from now
    return now.toISOString().slice(0, 16)
  }

  const isLoading = scheduleTweet.isPending

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Schedule Tweet</span>
          </DialogTitle>
          <DialogDescription>
            Choose when to publish this tweet to Twitter/X.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Tweet Preview */}
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground mb-2">Tweet Preview:</p>
            <p className="text-sm">{draft.content}</p>
            {draft.mediaUrls && draft.mediaUrls.length > 0 && (
              <p className="text-xs text-muted-foreground mt-2">
                + {draft.mediaUrls.length} media file{draft.mediaUrls.length > 1 ? 's' : ''}
              </p>
            )}
          </div>

          {/* Schedule Date & Time */}
          <div className="space-y-2">
            <Label htmlFor="scheduledFor" className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Schedule Date & Time</span>
            </Label>
            <Input
              id="scheduledFor"
              type="datetime-local"
              min={getMinDateTime()}
              {...form.register('scheduledFor')}
              error={form.formState.errors.scheduledFor?.message}
            />
            <p className="text-xs text-muted-foreground">
              Time is in your local timezone
            </p>
          </div>

          {/* Twitter Account Selection */}
          {twitterAccounts.length > 0 ? (
            <div className="space-y-2">
              <Label>Twitter Account</Label>
              <Select
                value={form.watch('twitterAccountId')}
                onValueChange={(value) => form.setValue('twitterAccountId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Twitter account" />
                </SelectTrigger>
                <SelectContent>
                  {twitterAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      @{account.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          ) : (
            <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                No Twitter accounts connected. Please connect a Twitter account to schedule tweets.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" loading={isLoading} disabled={isLoading}>
              Schedule Tweet
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}