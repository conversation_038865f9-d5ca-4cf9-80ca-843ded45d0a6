import * as React from 'react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useScheduleTweet } from '@/hooks/use-schedule';
import { useTwitterAccounts } from '@/hooks/use-twitter-accounts';
import { scheduleSchema } from '@/lib/validations/schedule';
import type { Draft } from '@/types/draft';
import type { z } from 'zod';

interface ScheduleDialogProps {
  draft: Draft;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type FormData = z.infer<typeof scheduleSchema>;

export function ScheduleDialog({ draft, open, onOpenChange }: ScheduleDialogProps) {
  const scheduleTweet = useScheduleTweet();
  const { data: twitterAccountsData } = useTwitterAccounts();

  const form = useForm<FormData>({
    resolver: zodResolver(scheduleSchema),
    defaultValues: {
      draftId: draft.id,
      scheduledFor: new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16),
    },
  });

  const onSubmit = (data: FormData) => {
    scheduleTweet.mutate(data, {
      onSuccess: () => {
        onOpenChange(false);
        form.reset();
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Schedule Tweet</DialogTitle>
          <DialogDescription>
            Select a Twitter account and a date and time to schedule this tweet.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="twitterAccountId">Twitter Account</Label>
            <Select
              onValueChange={(value) => form.setValue('twitterAccountId', value)}
              defaultValue={form.getValues('twitterAccountId')}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an account..." />
              </SelectTrigger>
              <SelectContent>
                {twitterAccountsData?.map((account) => (
                  <SelectItem key={account.id} value={account.id}>
                    {account.username}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.twitterAccountId && (
              <p className="text-sm text-red-500">{form.formState.errors.twitterAccountId.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="scheduledFor">Scheduled For</Label>
            <Input
              id="scheduledFor"
              type="datetime-local"
              {...form.register('scheduledFor')}
            />
            {form.formState.errors.scheduledFor && (
              <p className="text-sm text-red-500">{form.formState.errors.scheduledFor.message}</p>
            )}
          </div>
          <DialogFooter>
            <Button type="button" variant="ghost" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={scheduleTweet.isPending}>
              {scheduleTweet.isPending ? 'Scheduling...' : 'Schedule'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
