import * as React from 'react'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { MoreHorizontal, Calendar, Clock, Edit, Trash2, MessageSquare, ExternalLink } from 'lucide-react'
import { useUnscheduleTweet } from '@/hooks/use-schedule'
import { formatDate } from '@/lib/utils'
import type { ScheduledTweet } from '@/types/schedule'

interface ScheduledTweetCardProps {
  tweet: ScheduledTweet
  onEdit?: (tweet: ScheduledTweet) => void
}

export function ScheduledTweetCard({ tweet, onEdit }: ScheduledTweetCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const unscheduleTweet = useUnscheduleTweet()

  const handleUnschedule = () => {
    unscheduleTweet.mutate(tweet.id, {
      onSuccess: () => setShowDeleteDialog(false),
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-500'
      case 'published':
        return 'bg-green-500'
      case 'failed':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getTimeDisplay = () => {
    if (tweet.status === 'published' && tweet.publishedAt) {
      return {
        label: 'Published',
        time: new Date(tweet.publishedAt),
        icon: <ExternalLink className="h-3 w-3" />,
      }
    }
    
    if (tweet.scheduledFor) {
      return {
        label: 'Scheduled',
        time: new Date(tweet.scheduledFor),
        icon: <Clock className="h-3 w-3" />,
      }
    }

    return null
  }

  const timeDisplay = getTimeDisplay()
  const truncatedContent = tweet.content.length > 100 
    ? `${tweet.content.slice(0, 100)}...` 
    : tweet.content

  return (
    <>
      <Card className="group hover:shadow-lg transition-all duration-200 hover:border-primary-500/20">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-gradient-primary text-white text-xs">
                  {tweet.agent?.name?.slice(0, 2).toUpperCase() || 'TW'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-sm font-medium truncate">
                  {tweet.agent?.name || 'Unknown Agent'}
                </CardTitle>
                <CardDescription className="text-xs">
                  {tweet.twitterAccount ? `@${tweet.twitterAccount.username}` : 'Default Account'}
                </CardDescription>
              </div>
            </div>
            
            {tweet.status === 'scheduled' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(tweet)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Reschedule
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Unschedule
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-3">
          {/* Content Preview */}
          <div className="text-sm text-foreground">
            {truncatedContent}
          </div>

          {/* Media Preview */}
          {tweet.mediaUrls && tweet.mediaUrls.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {tweet.mediaUrls.slice(0, 2).map((url, index) => (
                <div key={index} className="w-12 h-12 rounded overflow-hidden bg-muted">
                  <img
                    src={url}
                    alt={`Media ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
              {tweet.mediaUrls.length > 2 && (
                <div className="w-12 h-12 rounded bg-muted flex items-center justify-center text-xs text-muted-foreground">
                  +{tweet.mediaUrls.length - 2}
                </div>
              )}
            </div>
          )}

          {/* Status and Time */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={`text-xs ${getStatusColor(tweet.status)} text-white border-0`}>
                {tweet.status.charAt(0).toUpperCase() + tweet.status.slice(1)}
              </Badge>
              
              {tweet.threadId && (
                <Badge variant="outline" className="text-xs">
                  <MessageSquare className="mr-1 h-3 w-3" />
                  Thread {tweet.threadOrder !== undefined ? `${tweet.threadOrder + 1}` : ''}
                  {tweet.isThreadStart && ' (Start)'}
                </Badge>
              )}
            </div>
            
            {timeDisplay && (
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                {timeDisplay.icon}
                <span>{formatDate(timeDisplay.time)}</span>
              </div>
            )}
          </div>

          {/* Twitter Link for Published Tweets */}
          {tweet.status === 'published' && tweet.twitterTweetId && (
            <div className="pt-2 border-t">
              <a
                href={`https://twitter.com/i/web/status/${tweet.twitterTweetId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-primary hover:underline flex items-center space-x-1"
              >
                <ExternalLink className="h-3 w-3" />
                <span>View on Twitter</span>
              </a>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Unschedule Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unschedule Tweet</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to unschedule this tweet? It will be moved back to your drafts.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleUnschedule}
              disabled={unscheduleTweet.isPending}
            >
              {unscheduleTweet.isPending ? 'Unscheduling...' : 'Unschedule'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}