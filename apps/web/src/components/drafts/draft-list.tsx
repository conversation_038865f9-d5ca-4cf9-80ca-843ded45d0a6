import * as React from 'react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { DraftCard } from './draft-card';
import { useDrafts } from '@/hooks/use-drafts';
import { useAgents } from '@/hooks/use-agents';
import { Plus, Search, FileText } from 'lucide-react';
import type { Draft } from '@/types/draft';

interface DraftListProps {
  onCreateDraft: () => void;
  onEditDraft: (draft: Draft) => void;
  onViewThread?: (threadId: string) => void;
}

export function DraftList({ onCreateDraft, onEditDraft, onViewThread }: DraftListProps) {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [agentId, setAgentId] = useState<string>('');
  const [sortBy, setSortBy] = useState<'createdAt' | 'updatedAt' | 'threadOrder'>('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const { data: draftsData, isLoading, error } = useDrafts({
    page,
    limit: 12,
    search: search || undefined,
    agentId: agentId || undefined,
    sortBy,
    sortOrder,
  });

  const { data: agentsData } = useAgents({ limit: 100 });

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handleAgentFilter = (value: string) => {
    setAgentId(value);
    setPage(1);
  };

  const handleSort = (value: string) => {
    const [field, order] = value.split('-');
    setSortBy(field as any);
    setSortOrder(order as 'asc' | 'desc');
    setPage(1);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-500 mb-2">Failed to load drafts</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Drafts</h1>
          <p className="text-muted-foreground">Manage your saved tweet drafts and threads</p>
        </div>
        <Button onClick={onCreateDraft} className="flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>New Draft</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search drafts..."
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Agent Filter */}
            <Select value={agentId} onValueChange={handleAgentFilter}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="All agents" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Agents</SelectItem>
                {agentsData?.agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={handleSort}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updatedAt-desc">Recently Updated</SelectItem>
                <SelectItem value="createdAt-desc">Newest First</SelectItem>
                <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                <SelectItem value="threadOrder-asc">Thread Order</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Drafts Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Spinner size="lg" />
        </div>
      ) : draftsData?.drafts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No drafts found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {search || agentId
                ? 'Try adjusting your filters or search terms.'
                : 'Get started by creating your first draft.'}
            </p>
            {!search && !agentId && (
              <Button onClick={onCreateDraft}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Draft
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {draftsData?.drafts.map((draft) => (
              <DraftCard
                key={draft.id}
                draft={draft}
                onEdit={onEditDraft}
                onViewThread={onViewThread}
              />
            ))}
          </div>

          {/* Pagination */}
          {draftsData && draftsData.pagination.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: draftsData.pagination.totalPages }, (_, i) => i + 1)
                  .filter(pageNum => {
                    const current = page;
                    const total = draftsData.pagination.totalPages;
                    return (
                      pageNum === 1 ||
                      pageNum === total ||
                      (pageNum >= current - 1 && pageNum <= current + 1)
                    );
                  })
                  .map((pageNum, index, array) => (
                    <React.Fragment key={pageNum}>
                      {index > 0 && array[index - 1] !== pageNum - 1 && (
                        <span className="px-2 text-muted-foreground">...</span>
                      )}
                      <Button
                        variant={pageNum === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    </React.Fragment>
                  ))}
              </div>

              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === draftsData.pagination.totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}