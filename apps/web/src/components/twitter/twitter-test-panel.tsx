import * as React from 'react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { Twitter, Send, Upload, Link, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TwitterTestPanelProps {
  className?: string
}

interface TestTweetForm {
  tweetText: string
  mediaUrls: string
}

interface TwitterStatus {
  connected: boolean
  accounts: Array<{
    id: string
    username: string
    displayName: string
    isActive: boolean
  }>
  activeAccount?: {
    username: string
    displayName: string
    verified: boolean
  }
  recentTweets?: Array<{
    id: string
    text: string
    createdAt: string
  }>
}

export function TwitterTestPanel({ className }: TwitterTestPanelProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [twitterStatus, setTwitterStatus] = useState<TwitterStatus | null>(null)
  const [testResult, setTestResult] = useState<any>(null)
  const { toast } = useToast()

  const form = useForm<TestTweetForm>({
    defaultValues: {
      tweetText: 'Testing Twitter API v2 integration from XTask! 🚀 #TwitterAPI #XTask',
      mediaUrls: '',
    },
  })

  // Check Twitter connection status
  const checkTwitterStatus = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/twitter/test')
      const data = await response.json()
      
      if (response.ok) {
        setTwitterStatus(data)
        toast({
          variant: 'success',
          title: 'Twitter Status Retrieved',
          description: data.message,
        })
      } else {
        toast({
          variant: 'error',
          title: 'Failed to Check Status',
          description: data.error,
        })
      }
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Connection Error',
        description: 'Failed to check Twitter status',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Post test tweet
  const postTestTweet = async (data: TestTweetForm) => {
    setIsLoading(true)
    setTestResult(null)
    
    try {
      const mediaUrls = data.mediaUrls
        ? data.mediaUrls.split('\n').map(url => url.trim()).filter(url => url)
        : []

      const response = await fetch('/api/twitter/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tweetText: data.tweetText,
          mediaUrls,
        }),
      })

      const result = await response.json()
      
      if (response.ok) {
        setTestResult(result.data)
        toast({
          variant: 'success',
          title: 'Tweet Posted Successfully!',
          description: `Tweet ID: ${result.data.tweetId}`,
        })
      } else {
        toast({
          variant: 'error',
          title: 'Failed to Post Tweet',
          description: result.message || result.error,
        })
      }
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Network Error',
        description: 'Failed to post tweet',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Post test thread
  const postTestThread = async () => {
    setIsLoading(true)
    setTestResult(null)
    
    try {
      const threadTweets = [
        { text: '🧵 Testing Twitter thread functionality from XTask! (1/3)' },
        { text: 'This is the second tweet in our test thread. Twitter API v2 makes threading much easier! (2/3)' },
        { text: 'And this concludes our test thread. Everything working perfectly! 🎉 (3/3)' },
      ]

      const response = await fetch('/api/twitter/thread', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tweets: threadTweets }),
      })

      const result = await response.json()
      
      if (response.ok) {
        setTestResult(result.data)
        toast({
          variant: 'success',
          title: 'Thread Posted Successfully!',
          description: `Posted ${result.data.totalTweets} tweets`,
        })
      } else {
        toast({
          variant: 'error',
          title: 'Failed to Post Thread',
          description: result.message || result.error,
        })
      }
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Network Error',
        description: 'Failed to post thread',
      })
    } finally {
      setIsLoading(false)
    }
  }

  React.useEffect(() => {
    checkTwitterStatus()
  }, [])

  const getStatusIcon = (status?: TwitterStatus) => {
    if (!status) return <AlertCircle className="h-4 w-4 text-yellow-500" />
    if (!status.connected) return <XCircle className="h-4 w-4 text-red-500" />
    if (status.activeAccount?.verified) return <CheckCircle className="h-4 w-4 text-green-500" />
    return <AlertCircle className="h-4 w-4 text-yellow-500" />
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Twitter className="h-5 w-5 text-blue-500" />
          <span>Twitter API v2 Test Panel</span>
        </CardTitle>
        <CardDescription>
          Test Twitter API integration, post tweets, and verify functionality
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Connection Status */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Connection Status</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={checkTwitterStatus}
              loading={isLoading}
            >
              Refresh Status
            </Button>
          </div>
          
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              {getStatusIcon(twitterStatus)}
              <span className="font-medium">
                {twitterStatus?.connected ? 'Connected' : 'Not Connected'}
              </span>
            </div>
            
            {twitterStatus?.activeAccount && (
              <div className="space-y-1 text-sm text-muted-foreground">
                <p>Account: @{twitterStatus.activeAccount.username}</p>
                <p>Name: {twitterStatus.activeAccount.displayName}</p>
                <p>Status: {twitterStatus.activeAccount.verified ? 'Verified' : 'Needs Verification'}</p>
              </div>
            )}
            
            {twitterStatus?.accounts && twitterStatus.accounts.length > 0 && (
              <div className="mt-2">
                <p className="text-xs text-muted-foreground mb-1">Connected Accounts:</p>
                <div className="flex flex-wrap gap-1">
                  {twitterStatus.accounts.map((account) => (
                    <Badge
                      key={account.id}
                      variant={account.isActive ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      @{account.username}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Test Tweet Form */}
        <form onSubmit={form.handleSubmit(postTestTweet)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tweetText">Test Tweet Content</Label>
            <Textarea
              id="tweetText"
              {...form.register('tweetText')}
              placeholder="Enter your test tweet content..."
              rows={3}
              maxLength={280}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Character count: {form.watch('tweetText')?.length || 0}/280</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="mediaUrls">Media URLs (Optional)</Label>
            <Textarea
              id="mediaUrls"
              {...form.register('mediaUrls')}
              placeholder="Enter media URLs, one per line..."
              rows={2}
            />
            <p className="text-xs text-muted-foreground">
              Add image or video URLs, one per line. Max 4 images or 1 video.
            </p>
          </div>

          <div className="flex gap-2">
            <Button
              type="submit"
              loading={isLoading}
              disabled={!twitterStatus?.connected || !form.watch('tweetText')?.trim()}
              className="flex-1"
            >
              <Send className="h-4 w-4 mr-2" />
              Post Test Tweet
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={postTestThread}
              loading={isLoading}
              disabled={!twitterStatus?.connected}
            >
              Post Test Thread
            </Button>
          </div>
        </form>

        {/* Test Results */}
        {testResult && (
          <div className="space-y-3">
            <Label className="text-base font-medium">Test Results</Label>
            <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="space-y-2">
                {testResult.tweetId && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Tweet ID:</span>
                    <code className="text-xs bg-background px-2 py-1 rounded">
                      {testResult.tweetId}
                    </code>
                  </div>
                )}
                
                {testResult.threadId && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Thread ID:</span>
                    <code className="text-xs bg-background px-2 py-1 rounded">
                      {testResult.threadId}
                    </code>
                  </div>
                )}
                
                {testResult.totalTweets && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Tweets Posted:</span>
                    <Badge variant="outline">{testResult.totalTweets}</Badge>
                  </div>
                )}
                
                {testResult.mediaCount !== undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Media Files:</span>
                    <Badge variant="outline">{testResult.mediaCount}</Badge>
                  </div>
                )}
                
                {(testResult.twitterUrl || testResult.threadUrl) && (
                  <div className="pt-2 border-t">
                    <a
                      href={testResult.twitterUrl || testResult.threadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline flex items-center space-x-1"
                    >
                      <Link className="h-3 w-3" />
                      <span>View on Twitter</span>
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Recent Tweets */}
        {twitterStatus?.recentTweets && twitterStatus.recentTweets.length > 0 && (
          <div className="space-y-3">
            <Label className="text-base font-medium">Recent Tweets</Label>
            <div className="space-y-2">
              {twitterStatus.recentTweets.slice(0, 3).map((tweet) => (
                <div key={tweet.id} className="p-3 bg-muted rounded-lg">
                  <p className="text-sm mb-2">{tweet.text}</p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>{new Date(tweet.createdAt).toLocaleString()}</span>
                    <code>{tweet.id}</code>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Testing Instructions</Label>
          <div className="text-sm text-muted-foreground space-y-2">
            <p>1. <strong>Connect Twitter Account:</strong> Use the OAuth flow to connect your Twitter account</p>
            <p>2. <strong>Verify Connection:</strong> Check that your account appears as "Verified" above</p>
            <p>3. <strong>Test Single Tweet:</strong> Post a test tweet with optional media</p>
            <p>4. <strong>Test Thread:</strong> Post a multi-tweet thread to verify threading functionality</p>
            <p>5. <strong>Check Results:</strong> Verify tweets appear on Twitter and results show correct IDs</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}