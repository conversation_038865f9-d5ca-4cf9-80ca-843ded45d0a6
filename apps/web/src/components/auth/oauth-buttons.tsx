"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Spinner } from "@/components/ui/spinner"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"

// Icons for OAuth providers
const GoogleIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24">
    <path
      fill="currentColor"
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
    />
    <path
      fill="currentColor"
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
    />
    <path
      fill="currentColor"
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
    />
    <path
      fill="currentColor"
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
    />
  </svg>
)

const TwitterIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
)

interface OAuthButtonProps {
  provider: "google" | "twitter"
  variant?: "default" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
  className?: string
  disabled?: boolean
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function OAuthButton({
  provider,
  variant = "outline",
  size = "md",
  className,
  disabled = false,
  onSuccess,
  onError,
}: OAuthButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const providerConfig = {
    google: {
      name: "Google",
      icon: GoogleIcon,
      endpoint: "/api/auth/google",
      color: "text-red-600",
    },
    twitter: {
      name: "Twitter",
      icon: TwitterIcon,
      endpoint: "/api/auth/twitter",
      color: "text-blue-500",
    },
  }

  const config = providerConfig[provider]
  const Icon = config.icon

  const handleOAuthLogin = async () => {
    if (isLoading || disabled) return

    setIsLoading(true)

    try {
      // Store callback information for success handling
      if (onSuccess) {
        sessionStorage.setItem("oauth_success_callback", "true")
      }

      // Redirect to OAuth endpoint
      window.location.href = config.endpoint
    } catch (error) {
      setIsLoading(false)
      const errorMessage = `Failed to initiate ${config.name} login`
      
      toast({
        title: "Authentication Error",
        description: errorMessage,
        variant: "destructive",
      })

      onError?.(errorMessage)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleOAuthLogin}
      disabled={isLoading || disabled}
      className={cn(
        "relative flex items-center justify-center gap-2",
        className
      )}
    >
      {isLoading ? (
        <Spinner size="sm" />
      ) : (
        <Icon className={cn("h-4 w-4", config.color)} />
      )}
      <span>
        {isLoading ? "Connecting..." : `Continue with ${config.name}`}
      </span>
    </Button>
  )
}

interface OAuthButtonGroupProps {
  variant?: "default" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
  orientation?: "horizontal" | "vertical"
  className?: string
  disabled?: boolean
  onSuccess?: () => void
  onError?: (error: string) => void
  providers?: ("google" | "twitter")[]
}

export function OAuthButtonGroup({
  variant = "outline",
  size = "md",
  orientation = "vertical",
  className,
  disabled = false,
  onSuccess,
  onError,
  providers = ["google", "twitter"],
}: OAuthButtonGroupProps) {
  return (
    <div
      className={cn(
        "flex gap-3",
        orientation === "horizontal" ? "flex-row" : "flex-col",
        className
      )}
    >
      {providers.map((provider) => (
        <OAuthButton
          key={provider}
          provider={provider}
          variant={variant}
          size={size}
          disabled={disabled}
          onSuccess={onSuccess}
          onError={onError}
          className="w-full"
        />
      ))}
    </div>
  )
}

// Hook for handling OAuth callback success
export function useOAuthCallback() {
  const { toast } = useToast()

  const handleOAuthSuccess = () => {
    const hasCallback = sessionStorage.getItem("oauth_success_callback")
    if (hasCallback) {
      sessionStorage.removeItem("oauth_success_callback")
      toast({
        title: "Welcome!",
        description: "You have successfully signed in.",
        variant: "success",
      })
    }
  }

  return { handleOAuthSuccess }
}

// OAuth status component for displaying current authentication state
interface OAuthStatusProps {
  user?: {
    name?: string
    email: string
    avatar?: string
  } | null
  onSignOut?: () => void
  className?: string
}

export function OAuthStatus({ user, onSignOut, className }: OAuthStatusProps) {
  const { toast } = useToast()

  const handleSignOut = async () => {
    try {
      const response = await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      })

      if (response.ok) {
        toast({
          title: "Signed out",
          description: "You have been successfully signed out.",
        })
        onSignOut?.()
        window.location.href = "/login"
      } else {
        throw new Error("Sign out failed")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (!user) {
    return null
  }

  return (
    <div className={cn("flex items-center gap-3", className)}>
      {user.avatar && (
        <img
          src={user.avatar}
          alt={user.name || user.email}
          className="h-8 w-8 rounded-full"
        />
      )}
      <div className="flex-1 min-w-0">
        {user.name && (
          <p className="text-sm font-medium text-foreground truncate">
            {user.name}
          </p>
        )}
        <p className="text-xs text-muted-foreground truncate">
          {user.email}
        </p>
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleSignOut}
        className="text-muted-foreground hover:text-foreground"
      >
        Sign out
      </Button>
    </div>
  )
}
