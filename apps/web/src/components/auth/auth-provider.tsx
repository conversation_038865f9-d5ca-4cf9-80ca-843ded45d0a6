"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/hooks/use-auth"
import { LoadingOverlay } from "@/components/ui/spinner"
import { useToast } from "@/hooks/use-toast"

interface AuthContextType {
  user: any
  isLoading: boolean
  isAuthenticated: boolean
  login: (provider: "google" | "twitter") => void
  logout: () => void
  isLoggingOut: boolean
  refreshAuth: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export function AuthProvider({ 
  children, 
  requireAuth = false, 
  redirectTo = "/login" 
}: AuthProviderProps) {
  const auth = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { toast } = useToast()
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false)

  // Handle authentication state changes
  useEffect(() => {
    if (!auth.isLoading) {
      setHasCheckedAuth(true)

      // Handle authentication requirements
      if (requireAuth && !auth.isAuthenticated) {
        // Store current path for redirect after login
        const currentPath = pathname !== "/login" ? pathname : undefined
        const loginUrl = currentPath 
          ? `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`
          : redirectTo
        
        router.push(loginUrl)
        return
      }

      // Redirect authenticated users away from login page
      if (auth.isAuthenticated && pathname === "/login") {
        const redirectParam = new URLSearchParams(window.location.search).get("redirect")
        const destination = redirectParam || "/dashboard"
        router.push(destination)
        return
      }
    }
  }, [auth.isLoading, auth.isAuthenticated, requireAuth, pathname, router, redirectTo])

  // Handle OAuth callback success
  useEffect(() => {
    const handleOAuthCallback = () => {
      const urlParams = new URLSearchParams(window.location.search)
      const success = urlParams.get("success")
      const error = urlParams.get("error")
      const provider = urlParams.get("provider")

      if (success === "true") {
        toast({
          title: "Welcome!",
          description: `Successfully signed in${provider ? ` with ${provider}` : ""}.`,
          variant: "success",
        })
        
        // Clean up URL parameters
        const cleanUrl = window.location.pathname
        window.history.replaceState({}, document.title, cleanUrl)
      }

      if (error) {
        const errorMessages: Record<string, string> = {
          oauth_error: "Authentication failed. Please try again.",
          access_denied: "Access was denied. Please try again if this was a mistake.",
          server_error: "Server error occurred. Please try again later.",
        }

        toast({
          title: "Authentication Error",
          description: errorMessages[error] || "An error occurred during authentication.",
          variant: "destructive",
        })

        // Clean up URL parameters
        const cleanUrl = window.location.pathname
        window.history.replaceState({}, document.title, cleanUrl)
      }
    }

    handleOAuthCallback()
  }, [toast])

  const refreshAuth = () => {
    // Force refresh of auth state
    window.location.reload()
  }

  const contextValue: AuthContextType = {
    ...auth,
    refreshAuth,
  }

  // Show loading overlay while checking authentication
  if (!hasCheckedAuth && auth.isLoading) {
    return (
      <LoadingOverlay
        isLoading={true}
        text="Checking authentication..."
        className="min-h-screen"
      >
        <div className="min-h-screen bg-background" />
      </LoadingOverlay>
    )
  }

  // Don't render children if auth is required but user is not authenticated
  if (requireAuth && !auth.isAuthenticated && hasCheckedAuth) {
    return null
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuthContext() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider")
  }
  return context
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { redirectTo?: string } = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthProvider requireAuth={true} redirectTo={options.redirectTo}>
        <Component {...props} />
      </AuthProvider>
    )
  }
}

// Hook for checking specific permissions
export function usePermissions() {
  const { user, isAuthenticated } = useAuthContext()

  const hasPermission = (permission: string) => {
    if (!isAuthenticated || !user) return false
    
    // Add your permission logic here
    // For now, all authenticated users have all permissions
    return true
  }

  const hasRole = (role: string) => {
    if (!isAuthenticated || !user) return false
    
    // Add your role logic here
    // For now, all users have 'user' role
    return role === "user"
  }

  return {
    hasPermission,
    hasRole,
    isAuthenticated,
  }
}

// Component for conditional rendering based on auth state
interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAuth?: boolean
  requirePermission?: string
  requireRole?: string
}

export function AuthGuard({
  children,
  fallback = null,
  requireAuth = true,
  requirePermission,
  requireRole,
}: AuthGuardProps) {
  const { isAuthenticated } = useAuthContext()
  const { hasPermission, hasRole } = usePermissions()

  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>
  }

  if (requirePermission && !hasPermission(requirePermission)) {
    return <>{fallback}</>
  }

  if (requireRole && !hasRole(requireRole)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
