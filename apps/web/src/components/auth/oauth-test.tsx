"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Shield,
  Key,
  Clock,
  Globe
} from "lucide-react"

interface TestResult {
  name: string
  status: "success" | "error" | "warning" | "pending"
  message: string
  details?: any
}

export function OAuthTest() {
  const { toast } = useToast()
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])

  const runTests = async () => {
    setIsRunning(true)
    setResults([])

    const tests = [
      {
        name: "Environment Variables",
        test: testEnvironmentVariables,
      },
      {
        name: "Google OAuth Configuration",
        test: testGoogleOAuth,
      },
      {
        name: "Twitter OAuth Configuration", 
        test: testTwitterOAuth,
      },
      {
        name: "Database Connection",
        test: testDatabaseConnection,
      },
      {
        name: "JWT Token Generation",
        test: testJWTGeneration,
      },
      {
        name: "Session Management",
        test: testSessionManagement,
      },
      {
        name: "Security Headers",
        test: testSecurityHeaders,
      },
    ]

    for (const test of tests) {
      try {
        const result = await test.test()
        setResults(prev => [...prev, { name: test.name, ...result }])
      } catch (error) {
        setResults(prev => [...prev, {
          name: test.name,
          status: "error",
          message: `Test failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        }])
      }
    }

    setIsRunning(false)
  }

  const testEnvironmentVariables = async (): Promise<Omit<TestResult, "name">> => {
    const requiredVars = [
      "NEXTAUTH_SECRET",
      "NEXTAUTH_URL",
      "GOOGLE_CLIENT_ID",
      "GOOGLE_CLIENT_SECRET",
      "TWITTER_CLIENT_ID",
      "TWITTER_CLIENT_SECRET",
      "DATABASE_URL",
    ]

    const response = await fetch("/api/auth/test/env")
    const data = await response.json()

    if (!response.ok) {
      return {
        status: "error",
        message: "Failed to check environment variables",
        details: data,
      }
    }

    const missing = requiredVars.filter(varName => !data.variables[varName])

    if (missing.length > 0) {
      return {
        status: "error",
        message: `Missing environment variables: ${missing.join(", ")}`,
        details: { missing, available: Object.keys(data.variables) },
      }
    }

    return {
      status: "success",
      message: "All required environment variables are set",
      details: { count: requiredVars.length },
    }
  }

  const testGoogleOAuth = async (): Promise<Omit<TestResult, "name">> => {
    try {
      const response = await fetch("/api/auth/test/google")
      const data = await response.json()

      if (!response.ok) {
        return {
          status: "error",
          message: data.error || "Google OAuth test failed",
          details: data,
        }
      }

      return {
        status: "success",
        message: "Google OAuth configuration is valid",
        details: data,
      }
    } catch (error) {
      return {
        status: "error",
        message: "Failed to test Google OAuth configuration",
      }
    }
  }

  const testTwitterOAuth = async (): Promise<Omit<TestResult, "name">> => {
    try {
      const response = await fetch("/api/auth/test/twitter")
      const data = await response.json()

      if (!response.ok) {
        return {
          status: "error",
          message: data.error || "Twitter OAuth test failed",
          details: data,
        }
      }

      return {
        status: "success",
        message: "Twitter OAuth configuration is valid",
        details: data,
      }
    } catch (error) {
      return {
        status: "error",
        message: "Failed to test Twitter OAuth configuration",
      }
    }
  }

  const testDatabaseConnection = async (): Promise<Omit<TestResult, "name">> => {
    try {
      const response = await fetch("/api/auth/test/database")
      const data = await response.json()

      if (!response.ok) {
        return {
          status: "error",
          message: data.error || "Database connection test failed",
          details: data,
        }
      }

      return {
        status: "success",
        message: "Database connection is working",
        details: data,
      }
    } catch (error) {
      return {
        status: "error",
        message: "Failed to test database connection",
      }
    }
  }

  const testJWTGeneration = async (): Promise<Omit<TestResult, "name">> => {
    try {
      const response = await fetch("/api/auth/test/jwt")
      const data = await response.json()

      if (!response.ok) {
        return {
          status: "error",
          message: data.error || "JWT test failed",
          details: data,
        }
      }

      return {
        status: "success",
        message: "JWT generation and verification working",
        details: data,
      }
    } catch (error) {
      return {
        status: "error",
        message: "Failed to test JWT functionality",
      }
    }
  }

  const testSessionManagement = async (): Promise<Omit<TestResult, "name">> => {
    try {
      const response = await fetch("/api/auth/test/session")
      const data = await response.json()

      if (!response.ok) {
        return {
          status: "error",
          message: data.error || "Session management test failed",
          details: data,
        }
      }

      return {
        status: "success",
        message: "Session management is working",
        details: data,
      }
    } catch (error) {
      return {
        status: "error",
        message: "Failed to test session management",
      }
    }
  }

  const testSecurityHeaders = async (): Promise<Omit<TestResult, "name">> => {
    try {
      const response = await fetch("/api/auth/test/security")
      const data = await response.json()

      if (!response.ok) {
        return {
          status: "warning",
          message: "Some security headers may be missing",
          details: data,
        }
      }

      return {
        status: "success",
        message: "Security headers are properly configured",
        details: data,
      }
    } catch (error) {
      return {
        status: "error",
        message: "Failed to test security headers",
      }
    }
  }

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case "pending":
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
    }
  }

  const getStatusBadge = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <Badge variant="secondary" className="text-green-600">Pass</Badge>
      case "error":
        return <Badge variant="destructive">Fail</Badge>
      case "warning":
        return <Badge variant="outline" className="text-yellow-600">Warning</Badge>
      case "pending":
        return <Badge variant="outline">Running</Badge>
    }
  }

  const successCount = results.filter(r => r.status === "success").length
  const errorCount = results.filter(r => r.status === "error").length
  const warningCount = results.filter(r => r.status === "warning").length

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          OAuth Configuration Test
        </CardTitle>
        <CardDescription>
          Test your OAuth 2.0 configuration to ensure everything is working correctly
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Test Controls */}
        <div className="flex items-center justify-between">
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Key className="h-4 w-4" />
            )}
            {isRunning ? "Running Tests..." : "Run OAuth Tests"}
          </Button>

          {results.length > 0 && (
            <div className="flex items-center gap-4 text-sm">
              <span className="flex items-center gap-1 text-green-600">
                <CheckCircle className="h-4 w-4" />
                {successCount} passed
              </span>
              {warningCount > 0 && (
                <span className="flex items-center gap-1 text-yellow-600">
                  <AlertTriangle className="h-4 w-4" />
                  {warningCount} warnings
                </span>
              )}
              {errorCount > 0 && (
                <span className="flex items-center gap-1 text-red-600">
                  <XCircle className="h-4 w-4" />
                  {errorCount} failed
                </span>
              )}
            </div>
          )}
        </div>

        {/* Test Results */}
        {results.length > 0 && (
          <div className="space-y-4">
            <Separator />
            <h3 className="font-medium">Test Results</h3>
            
            <div className="space-y-3">
              {results.map((result, index) => (
                <div key={index} className="flex items-start justify-between p-3 border rounded-lg">
                  <div className="flex items-start gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{result.name}</h4>
                        {getStatusBadge(result.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {result.message}
                      </p>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-muted-foreground cursor-pointer">
                            View details
                          </summary>
                          <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Help Information */}
        <Alert>
          <Globe className="h-4 w-4" />
          <AlertDescription>
            This test suite validates your OAuth 2.0 configuration including environment variables,
            provider settings, database connectivity, and security measures. Run these tests after
            making configuration changes to ensure everything is working correctly.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
}
