'use client'

import * as React from 'react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Spinner } from '@/components/ui/spinner'
import { 
  Sparkles, 
  Copy, 
  RefreshCw, 
  Lightbulb, 
  MessageSquare, 
  Edit, 
  Brain,
  Zap,
  Cpu,
  Bo<PERSON>
} from 'lucide-react'
import { useEnhancedGeneration, useEnhancedCapabilities } from '@/hooks/use-agent-memory'
import { useAgents } from '@/hooks/use-agents'
import { useContentSuggestions } from '@/hooks/use-ai-generation'
import { useToast } from '@/hooks/use-toast'
import { z } from 'zod'

const generateFormSchema = z.object({
  type: z.enum(['tweet', 'thread', 'improve', 'custom', 'contextual']),
  agentId: z.string().min(1, 'Please select an agent'),
  topic: z.string().optional(),
  content: z.string().optional(),
  tweetCount: z.coerce.number().min(2).max(25).optional(),
  improvementType: z.enum(['engagement', 'clarity', 'tone', 'length']).optional(),
  customPrompt: z.string().optional(),
  useMemory: z.boolean().default(true),
  storeMemory: z.boolean().default(true),
  model: z.string().optional(),
})

type FormData = z.infer<typeof generateFormSchema>

interface EnhancedContentGeneratorProps {
  onContentGenerated?: (content: string, type: string) => void
  defaultAgentId?: string
  defaultModel?: string
  className?: string
}

export function EnhancedContentGenerator({ 
  onContentGenerated, 
  defaultAgentId,
  defaultModel,
  className 
}: EnhancedContentGeneratorProps) {
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [activeTab, setActiveTab] = useState('tweet')
  
  const generateContent = useEnhancedGeneration()
  const getSuggestions = useContentSuggestions()
  const { data: agentsData } = useAgents({ limit: 100 })
  const { data: capabilities } = useEnhancedCapabilities()
  const { toast } = useToast()

  const form = useForm<FormData>({
    resolver: zodResolver(generateFormSchema),
    defaultValues: {
      type: 'tweet',
      agentId: defaultAgentId || '',
      model: defaultModel || '',
      improvementType: 'engagement',
      useMemory: true,
      storeMemory: true,
    },
  })

  const selectedType = form.watch('type')
  const selectedAgentId = form.watch('agentId')
  const useMemory = form.watch('useMemory')
  const storeMemory = form.watch('storeMemory')

  const onSubmit = (data: FormData) => {
    generateContent.mutate(data, {
      onSuccess: (response) => {
        setGeneratedContent(response.data.content)
        onContentGenerated?.(response.data.content, data.type)
      },
    })
  }

  const handleGetSuggestions = () => {
    if (!selectedAgentId) {
      toast({
        variant: 'error',
        title: 'Agent Required',
        description: 'Please select an agent to get content suggestions.',
      })
      return
    }

    getSuggestions.mutate({
      agentId: selectedAgentId,
      count: 5,
    })
  }

  const handleCopyContent = () => {
    if (generatedContent) {
      navigator.clipboard.writeText(generatedContent)
      toast({
        variant: 'success',
        title: 'Copied!',
        description: 'Content copied to clipboard.',
      })
    }
  }

  const handleUseContent = () => {
    if (generatedContent) {
      onContentGenerated?.(generatedContent, selectedType)
    }
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    form.setValue('type', value as any)
    setGeneratedContent('')
  }

  const availableModels = React.useMemo(() => {
    if (!capabilities) return []
    
    const models = [
      ...(capabilities.data.models.openai || []),
      ...(capabilities.data.models.gemini || []),
    ]
    
    return models
  }, [capabilities])

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <span>Enhanced AI Content Generator</span>
          <Badge variant="outline" className="ml-2 text-xs">
            <Brain className="h-3 w-3 mr-1 text-primary" />
            Memory-Powered
          </Badge>
        </CardTitle>
        <CardDescription>
          Generate engaging content using AI with vector memory and multimodal capabilities
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Agent Selection */}
        <div className="space-y-2">
          <Label>Select Agent</Label>
          <Select
            value={form.watch('agentId')}
            onValueChange={(value) => form.setValue('agentId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose an agent..." />
            </SelectTrigger>
            <SelectContent>
              {agentsData?.agents.map((agent) => (
                <SelectItem key={agent.id} value={agent.id}>
                  <div className="flex items-center space-x-2">
                    <span>{agent.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {agent.tweetsGenerated} tweets
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Memory Options */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6">
          <div className="flex items-center space-x-2">
            <Switch
              checked={useMemory}
              onCheckedChange={(checked) => form.setValue('useMemory', checked)}
              id="use-memory"
            />
            <Label htmlFor="use-memory" className="cursor-pointer">
              Use Agent Memory
            </Label>
            <span className="text-xs text-muted-foreground ml-1">
              (for context)
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              checked={storeMemory}
              onCheckedChange={(checked) => form.setValue('storeMemory', checked)}
              id="store-memory"
            />
            <Label htmlFor="store-memory" className="cursor-pointer">
              Store as Memory
            </Label>
            <span className="text-xs text-muted-foreground ml-1">
              (for future use)
            </span>
          </div>
        </div>

        {/* Generation Type Tabs */}
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="tweet" className="text-xs">
              <MessageSquare className="h-3 w-3 mr-1" />
              Tweet
            </TabsTrigger>
            <TabsTrigger value="thread" className="text-xs">
              Thread
            </TabsTrigger>
            <TabsTrigger value="improve" className="text-xs">
              <Edit className="h-3 w-3 mr-1" />
              Improve
            </TabsTrigger>
            <TabsTrigger value="contextual" className="text-xs">
              <Brain className="h-3 w-3 mr-1" />
              Contextual
            </TabsTrigger>
            <TabsTrigger value="custom" className="text-xs">
              Custom
            </TabsTrigger>
          </TabsList>

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-4">
            <TabsContent value="tweet" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topic">Topic (Optional)</Label>
                <Input
                  id="topic"
                  {...form.register('topic')}
                  placeholder="e.g., AI trends, productivity tips, tech news..."
                />
                <p className="text-xs text-muted-foreground">
                  Leave empty to generate based on agent's interests and memories
                </p>
              </div>
            </TabsContent>

            <TabsContent value="thread" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="thread-topic">Topic *</Label>
                <Input
                  id="thread-topic"
                  {...form.register('topic')}
                  placeholder="e.g., How to build better habits..."
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tweetCount">Number of Tweets</Label>
                <Select
                  value={form.watch('tweetCount')?.toString() || '3'}
                  onValueChange={(value) => form.setValue('tweetCount', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 8 }, (_, i) => i + 2).map((count) => (
                      <SelectItem key={count} value={count.toString()}>
                        {count} tweets
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="improve" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="content">Content to Improve *</Label>
                <Textarea
                  id="content"
                  {...form.register('content')}
                  placeholder="Paste your existing content here..."
                  rows={3}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Improvement Focus</Label>
                <Select
                  value={form.watch('improvementType')}
                  onValueChange={(value) => form.setValue('improvementType', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="engagement">Engagement</SelectItem>
                    <SelectItem value="clarity">Clarity</SelectItem>
                    <SelectItem value="tone">Tone</SelectItem>
                    <SelectItem value="length">Length</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="contextual" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="contextual-query">Query *</Label>
                <Input
                  id="contextual-query"
                  {...form.register('topic')}
                  placeholder="Ask a question or provide a prompt..."
                  required
                />
                <p className="text-xs text-muted-foreground">
                  The agent will respond based on its memories and knowledge
                </p>
              </div>
              <div className="p-3 bg-primary-500/10 rounded-lg border border-primary-500/20">
                <div className="flex items-center mb-2">
                  <Brain className="h-4 w-4 text-primary mr-2" />
                  <span className="text-sm font-medium">Memory-Powered Response</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  This mode uses vector similarity search to find relevant memories and generate a contextually appropriate response.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="customPrompt">Custom Prompt *</Label>
                <Textarea
                  id="customPrompt"
                  {...form.register('customPrompt')}
                  placeholder="Describe what you want the AI to create..."
                  rows={3}
                  required
                />
              </div>
            </TabsContent>

            {/* Model Selection */}
            {availableModels.length > 0 && (
              <div className="space-y-2">
                <Label>AI Model (Optional)</Label>
                <Select
                  value={form.watch('model') || ''}
                  onValueChange={(value) => form.setValue('model', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Auto-select optimal model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Auto-select optimal model</SelectItem>
                    {availableModels.map((model) => (
                      <SelectItem key={model} value={model}>
                        <div className="flex items-center">
                          {model.includes('gemini') ? (
                            <Bot className="h-3.5 w-3.5 mr-2 text-green-500" />
                          ) : (
                            <Cpu className="h-3.5 w-3.5 mr-2 text-blue-500" />
                          )}
                          {model}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Leave empty to automatically select the most cost-effective model for your task
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                type="submit"
                loading={generateContent.isPending}
                disabled={!selectedAgentId}
                className="flex-1"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Content
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={handleGetSuggestions}
                loading={getSuggestions.isPending}
                disabled={!selectedAgentId}
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                Get Ideas
              </Button>
            </div>
          </form>
        </Tabs>

        {/* Content Suggestions */}
        {getSuggestions.data && (
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <Lightbulb className="h-4 w-4" />
              <span>Content Ideas</span>
            </Label>
            <div className="space-y-2">
              {getSuggestions.data.suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="p-3 bg-muted rounded-lg cursor-pointer hover:bg-muted/80 transition-colors"
                  onClick={() => form.setValue('topic', suggestion)}
                >
                  <p className="text-sm">{suggestion}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Generated Content */}
        {generatedContent && (
          <div className="space-y-3">
            <Label>Generated Content</Label>
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{generatedContent}</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyContent}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              {onContentGenerated && (
                <Button
                  size="sm"
                  onClick={handleUseContent}
                >
                  Use Content
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => form.handleSubmit(onSubmit)()}
                loading={generateContent.isPending}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Regenerate
              </Button>
            </div>
          </div>
        )}

        {/* Usage Information */}
        {generateContent.data && (
          <div className="space-y-3">
            <div className="text-xs text-muted-foreground space-y-1">
              <p>Model: {generateContent.data.data.model}</p>
              <p>Tokens used: {generateContent.data.data.usage?.totalTokens || 'N/A'}</p>
              <p>Memories used: {generateContent.data.data.memoriesUsed}</p>
            </div>
            
            {/* Enhanced Features */}
            {generateContent.data.data.enhancedFeatures && (
              <div className="flex flex-wrap gap-2">
                {generateContent.data.data.enhancedFeatures.memoryIntegration && (
                  <Badge variant="outline" className="text-xs">
                    <Brain className="h-3 w-3 mr-1 text-primary" />
                    Memory Integration
                  </Badge>
                )}
                {generateContent.data.data.enhancedFeatures.geminiPowered && (
                  <Badge variant="outline" className="text-xs">
                    <Bot className="h-3 w-3 mr-1 text-green-500" />
                    Gemini Powered
                  </Badge>
                )}
                {generateContent.data.data.enhancedFeatures.costOptimized && (
                  <Badge variant="outline" className="text-xs">
                    <Zap className="h-3 w-3 mr-1 text-yellow-500" />
                    Cost Optimized
                  </Badge>
                )}
              </div>
            )}
          </div>
        )}

        {/* Capabilities Information */}
        {capabilities && (
          <div className="mt-4 pt-4 border-t border-dark-border">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium">Enhanced Capabilities</h4>
              <Badge variant="secondary" className="text-xs">
                <Cpu className="h-3 w-3 mr-1" />
                {capabilities.data.capabilities.geminiSupport ? 'Gemini Available' : 'OpenAI Only'}
              </Badge>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
              {capabilities.data.features.map((feature, index) => (
                <div key={index} className="p-2 rounded bg-dark-surface">
                  <p className="font-medium">{feature}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}