"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { 
  RefreshCw, 
  Zap, 
  Brain, 
  Cpu, 
  CheckCircle,
  AlertCircle,
  ExternalLink
} from 'lucide-react'

interface ModelInfo {
  id: string
  name: string
  description?: string
  provider: string
  contextLength?: number
  capabilities?: string[]
}

interface ModelsData {
  models: Record<string, ModelInfo[]>
  summary: {
    total: number
    byProvider: Record<string, number>
    capabilities: {
      openaiAvailable: boolean
      geminiAvailable: boolean
    }
  }
}

interface ModelSelectorProps {
  selectedModel?: string
  onModelSelect?: (modelId: string, provider: string) => void
  className?: string
  showTestButton?: boolean
}

export function ModelSelector({ 
  selectedModel, 
  onModelSelect, 
  className,
  showTestButton = false 
}: ModelSelectorProps) {
  const { toast } = useToast()
  const [models, setModels] = useState<ModelsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [testing, setTesting] = useState<string | null>(null)
  const [selectedProvider, setSelectedProvider] = useState<string>('')

  const fetchModels = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/ai/models')
      const data = await response.json()

      if (data.success) {
        setModels(data.data)
        
        // Auto-select first available provider if none selected
        if (!selectedProvider && data.data.models) {
          const providers = Object.keys(data.data.models)
          if (providers.length > 0) {
            setSelectedProvider(providers[0])
          }
        }
      } else {
        throw new Error(data.error || 'Failed to fetch models')
      }
    } catch (error) {
      console.error('Failed to fetch models:', error)
      toast({
        title: "Failed to load models",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const testModel = async (modelId: string, provider: string) => {
    try {
      setTesting(modelId)
      const response = await fetch('/api/ai/models/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          modelId,
          provider,
          testPrompt: "Hello! Please respond with a brief greeting.",
        }),
      })

      const data = await response.json()

      if (data.success && !data.data.error) {
        toast({
          title: "Model test successful",
          description: `${modelId} is working correctly`,
          variant: "success",
        })
      } else {
        throw new Error(data.data.error || 'Test failed')
      }
    } catch (error) {
      console.error('Model test failed:', error)
      toast({
        title: "Model test failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setTesting(null)
    }
  }

  const handleModelSelect = (modelId: string) => {
    const provider = selectedProvider
    onModelSelect?.(modelId, provider)
    toast({
      title: "Model selected",
      description: `Selected ${modelId} from ${provider}`,
    })
  }

  useEffect(() => {
    fetchModels()
  }, [])

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Loading Models...
          </CardTitle>
        </CardHeader>
      </Card>
    )
  }

  if (!models) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            Failed to Load Models
          </CardTitle>
          <CardDescription>
            Unable to fetch available models. Please check your API configuration.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={fetchModels} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  const providers = Object.keys(models.models)
  const currentModels = selectedProvider ? models.models[selectedProvider] || [] : []

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          AI Model Selection
        </CardTitle>
        <CardDescription>
          Choose from {models.summary.total} available models across {providers.length} providers
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Provider Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Provider</label>
          <Select value={selectedProvider} onValueChange={setSelectedProvider}>
            <SelectTrigger>
              <SelectValue placeholder="Select a provider" />
            </SelectTrigger>
            <SelectContent>
              {providers.map((provider) => (
                <SelectItem key={provider} value={provider}>
                  <div className="flex items-center gap-2">
                    <span className="capitalize">{provider}</span>
                    <Badge variant="secondary">
                      {models.models[provider].length} models
                    </Badge>
                    {provider === 'openai' && models.summary.capabilities.openaiAvailable && (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    )}
                    {provider === 'gemini' && models.summary.capabilities.geminiAvailable && (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Model Selection */}
        {selectedProvider && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Model</label>
            <Select value={selectedModel} onValueChange={handleModelSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {currentModels.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <span>{model.name}</span>
                        {model.capabilities?.includes('multimodal') && (
                          <Badge variant="outline" className="text-xs">
                            Multimodal
                          </Badge>
                        )}
                        {model.capabilities?.includes('function-calling') && (
                          <Badge variant="outline" className="text-xs">
                            Functions
                          </Badge>
                        )}
                      </div>
                      {model.description && (
                        <span className="text-xs text-muted-foreground">
                          {model.description}
                        </span>
                      )}
                      {model.contextLength && (
                        <span className="text-xs text-muted-foreground">
                          Context: {model.contextLength.toLocaleString()} tokens
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Selected Model Info */}
        {selectedModel && selectedProvider && (
          <div className="space-y-2">
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium">Selected Model</p>
                <p className="text-xs text-muted-foreground">
                  {selectedModel} from {selectedProvider}
                </p>
              </div>
              {showTestButton && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => testModel(selectedModel, selectedProvider)}
                  disabled={testing === selectedModel}
                >
                  {testing === selectedModel ? (
                    <RefreshCw className="h-3 w-3 animate-spin mr-2" />
                  ) : (
                    <Zap className="h-3 w-3 mr-2" />
                  )}
                  Test
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Provider Status */}
        <div className="space-y-2">
          <Separator />
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-2">
              <span>OpenAI:</span>
              {models.summary.capabilities.openaiAvailable ? (
                <Badge variant="default" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Available
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Not configured
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span>Gemini:</span>
              {models.summary.capabilities.geminiAvailable ? (
                <Badge variant="default" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Available
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Not configured
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Refresh Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={fetchModels}
          disabled={loading}
          className="w-full"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Models
        </Button>
      </CardContent>
    </Card>
  )
}

export default ModelSelector
