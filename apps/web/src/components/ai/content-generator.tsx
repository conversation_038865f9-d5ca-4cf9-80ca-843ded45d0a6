'use client'

import * as React from 'react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Sparkles, Copy, RefreshCw, Lightbulb, MessageSquare, Edit } from 'lucide-react'
import { useGenerateContent, useContentSuggestions } from '@/hooks/use-ai-generation'
import { useAgents } from '@/hooks/use-agents'
import { useToast } from '@/hooks/use-toast'
import { useModelsByProvider, formatModelName } from '@/hooks/use-models'
import { z } from 'zod'

const generateFormSchema = z.object({
  type: z.enum(['tweet', 'thread', 'improve', 'custom']),
  agentId: z.string().min(1, 'Please select an agent'),
  model: z.string().optional(),
  provider: z.string().optional(),
  topic: z.string().optional(),
  content: z.string().optional(),
  tweetCount: z.coerce.number().min(2).max(25).optional(),
  improvementType: z.enum(['engagement', 'clarity', 'tone', 'length']).optional(),
  customPrompt: z.string().optional(),
})

type FormData = z.infer<typeof generateFormSchema>

interface ContentGeneratorProps {
  onContentGenerated?: (content: string, type: string) => void
  defaultAgentId?: string
  defaultModel?: string
  className?: string
}

export function ContentGenerator({
  onContentGenerated,
  defaultAgentId,
  defaultModel,
  className
}: ContentGeneratorProps) {
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [activeTab, setActiveTab] = useState('tweet')

  const generateContent = useGenerateContent()
  const getSuggestions = useContentSuggestions()
  const { data: agentsData } = useAgents({ limit: 100 })
  const { data: modelsData, getModelsByProvider, getProviders, isProviderAvailable } = useModelsByProvider()
  const { toast } = useToast()

  const form = useForm<FormData>({
    resolver: zodResolver(generateFormSchema),
    defaultValues: {
      type: 'tweet',
      agentId: defaultAgentId || '',
      model: defaultModel || '',
      provider: 'openai',
      improvementType: 'engagement',
    },
  })

  const selectedType = form.watch('type')
  const selectedAgentId = form.watch('agentId')

  const onSubmit = (data: FormData) => {
    generateContent.mutate(data, {
      onSuccess: (response) => {
        setGeneratedContent(response.data.content)
        onContentGenerated?.(response.data.content, data.type)
      },
    })
  }

  const handleGetSuggestions = () => {
    if (!selectedAgentId) {
      toast({
        variant: 'error',
        title: 'Agent Required',
        description: 'Please select an agent to get content suggestions.',
      })
      return
    }

    getSuggestions.mutate({
      agentId: selectedAgentId,
      count: 5,
    })
  }

  const handleCopyContent = () => {
    if (generatedContent) {
      navigator.clipboard.writeText(generatedContent)
      toast({
        variant: 'success',
        title: 'Copied!',
        description: 'Content copied to clipboard.',
      })
    }
  }

  const handleUseContent = () => {
    if (generatedContent) {
      onContentGenerated?.(generatedContent, selectedType)
    }
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    form.setValue('type', value as any)
    setGeneratedContent('')
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <span>AI Content Generator</span>
        </CardTitle>
        <CardDescription>
          Generate engaging content using AI based on your agent's persona
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Agent Selection */}
        <div className="space-y-2">
          <Label>Select Agent</Label>
          <Select
            value={form.watch('agentId')}
            onValueChange={(value) => form.setValue('agentId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose an agent..." />
            </SelectTrigger>
            <SelectContent>
              {agentsData?.agents.map((agent) => (
                <SelectItem key={agent.id} value={agent.id}>
                  <div className="flex items-center space-x-2">
                    <span>{agent.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {agent.tweetsGenerated} tweets
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Model Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>AI Provider</Label>
            <Select
              value={form.watch('provider')}
              onValueChange={(value) => {
                form.setValue('provider', value)
                // Reset model when provider changes
                form.setValue('model', '')
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose provider..." />
              </SelectTrigger>
              <SelectContent>
                {getProviders().map((provider) => (
                  <SelectItem key={provider} value={provider}>
                    <div className="flex items-center space-x-2">
                      <span className="capitalize">{provider}</span>
                      {isProviderAvailable(provider) ? (
                        <Badge variant="default" className="text-xs">Available</Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">Not configured</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>AI Model</Label>
            <Select
              value={form.watch('model')}
              onValueChange={(value) => form.setValue('model', value)}
              disabled={!form.watch('provider')}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose model..." />
              </SelectTrigger>
              <SelectContent>
                {form.watch('provider') && getModelsByProvider(form.watch('provider')).map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex flex-col gap-1">
                      <span>{formatModelName(model)}</span>
                      {model.description && (
                        <span className="text-xs text-muted-foreground">
                          {model.description}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Generation Type Tabs */}
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tweet" className="text-xs">
              <MessageSquare className="h-3 w-3 mr-1" />
              Tweet
            </TabsTrigger>
            <TabsTrigger value="thread" className="text-xs">
              Thread
            </TabsTrigger>
            <TabsTrigger value="improve" className="text-xs">
              <Edit className="h-3 w-3 mr-1" />
              Improve
            </TabsTrigger>
            <TabsTrigger value="custom" className="text-xs">
              Custom
            </TabsTrigger>
          </TabsList>

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <TabsContent value="tweet" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topic">Topic (Optional)</Label>
                <Input
                  id="topic"
                  {...form.register('topic')}
                  placeholder="e.g., AI trends, productivity tips, tech news..."
                />
                <p className="text-xs text-muted-foreground">
                  Leave empty to generate based on agent's interests
                </p>
              </div>
            </TabsContent>

            <TabsContent value="thread" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="thread-topic">Topic *</Label>
                <Input
                  id="thread-topic"
                  {...form.register('topic')}
                  placeholder="e.g., How to build better habits..."
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tweetCount">Number of Tweets</Label>
                <Select
                  value={form.watch('tweetCount')?.toString() || '3'}
                  onValueChange={(value) => form.setValue('tweetCount', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 8 }, (_, i) => i + 2).map((count) => (
                      <SelectItem key={count} value={count.toString()}>
                        {count} tweets
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="improve" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="content">Content to Improve *</Label>
                <Textarea
                  id="content"
                  {...form.register('content')}
                  placeholder="Paste your existing content here..."
                  rows={3}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Improvement Focus</Label>
                <Select
                  value={form.watch('improvementType')}
                  onValueChange={(value) => form.setValue('improvementType', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="engagement">Engagement</SelectItem>
                    <SelectItem value="clarity">Clarity</SelectItem>
                    <SelectItem value="tone">Tone</SelectItem>
                    <SelectItem value="length">Length</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="customPrompt">Custom Prompt *</Label>
                <Textarea
                  id="customPrompt"
                  {...form.register('customPrompt')}
                  placeholder="Describe what you want the AI to create..."
                  rows={3}
                  required
                />
              </div>
            </TabsContent>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                type="submit"
                loading={generateContent.isPending}
                disabled={!selectedAgentId}
                className="flex-1"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Content
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={handleGetSuggestions}
                loading={getSuggestions.isPending}
                disabled={!selectedAgentId}
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                Get Ideas
              </Button>
            </div>
          </form>
        </Tabs>

        {/* Content Suggestions */}
        {getSuggestions.data && (
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <Lightbulb className="h-4 w-4" />
              <span>Content Ideas</span>
            </Label>
            <div className="space-y-2">
              {getSuggestions.data.suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="p-3 bg-muted rounded-lg cursor-pointer hover:bg-muted/80 transition-colors"
                  onClick={() => form.setValue('topic', suggestion)}
                >
                  <p className="text-sm">{suggestion}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Generated Content */}
        {generatedContent && (
          <div className="space-y-3">
            <Label>Generated Content</Label>
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{generatedContent}</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyContent}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              {onContentGenerated && (
                <Button
                  size="sm"
                  onClick={handleUseContent}
                >
                  Use Content
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => form.handleSubmit(onSubmit)()}
                loading={generateContent.isPending}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Regenerate
              </Button>
            </div>
          </div>
        )}

        {/* Usage Information */}
        {generateContent.data && (
          <div className="text-xs text-muted-foreground space-y-1">
            <p>Model: {generateContent.data.data.model}</p>
            <p>Tokens used: {generateContent.data.data.usage.totalTokens}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}