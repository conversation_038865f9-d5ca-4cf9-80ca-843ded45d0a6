'use client'

import * as React from 'react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ContentGenerator } from './content-generator'
import { Sparkles } from 'lucide-react'

interface AIAssistantButtonProps {
  onContentGenerated?: (content: string, type: string) => void
  agentId?: string
  model?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function AIAssistantButton({
  onContentGenerated,
  agentId,
  model,
  variant = 'outline',
  size = 'sm',
  className,
}: AIAssistantButtonProps) {
  const [open, setOpen] = useState(false)

  const handleContentGenerated = (content: string, type: string) => {
    onContentGenerated?.(content, type)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Sparkles className="h-4 w-4 mr-2" />
          AI Assistant
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>AI Content Assistant</DialogTitle>
          <DialogDescription>
            Generate engaging content using AI based on your agent's persona and preferences.
          </DialogDescription>
        </DialogHeader>
        <ContentGenerator
          onContentGenerated={handleContentGenerated}
          defaultAgentId={agentId}
          defaultModel={model}
        />
      </DialogContent>
    </Dialog>
  )
}