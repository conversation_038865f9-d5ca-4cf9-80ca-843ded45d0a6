import * as React from 'react';
import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Plus, X, MessageSquare, Move } from 'lucide-react';
import { createThreadSchema } from '@/lib/validations/draft';
import { useCreateThread } from '@/hooks/use-threads';
import { useAgents } from '@/hooks/use-agents';
import { MediaPreview } from '@/components/media/media-preview';
import { UploadButton } from '@/components/ui/upload-button';
import { useMediaUpload } from '@/hooks/use-media-upload';
import type { CreateThreadData } from '@/types/draft';
import { z } from 'zod';

interface ThreadComposerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialAgentId?: string;
}

type FormData = z.infer<typeof createThreadSchema>;

export function ThreadComposer({ open, onOpenChange, initialAgentId }: ThreadComposerProps) {
  const createThread = useCreateThread();
  const { data: agentsData } = useAgents({ limit: 100 });
  const [tweetMediaFiles, setTweetMediaFiles] = useState<Record<number, Array<{ url: string; fileId: string; fileType?: string }>>>({});

  const form = useForm<FormData>({
    resolver: zodResolver(createThreadSchema),
    defaultValues: {
      tweets: [
        { content: '', mediaUrls: [] },
        { content: '', mediaUrls: [] },
      ],
      agentId: initialAgentId || '',
    },
  });

  const {
    fields: tweetFields,
    append: appendTweet,
    remove: removeTweet,
    move: moveTweet,
  } = useFieldArray({
    control: form.control,
    name: 'tweets',
  });

  const onSubmit = (data: FormData) => {
    // Add media URLs to each tweet
    const tweetsWithMedia = data.tweets.map((tweet, index) => ({
      ...tweet,
      mediaUrls: tweetMediaFiles[index]?.map(file => file.url) || [],
    }));

    createThread.mutate(
      { ...data, tweets: tweetsWithMedia },
      {
        onSuccess: () => {
          onOpenChange(false);
          form.reset();
          setTweetMediaFiles({});
        },
      }
    );
  };

  const handleClose = () => {
    onOpenChange(false);
    form.reset();
    setTweetMediaFiles({});
  };

  const addTweet = () => {
    appendTweet({ content: '', mediaUrls: [] });
  };

  const removeTweetAt = (index: number) => {
    if (tweetFields.length > 2) {
      removeTweet(index);
      // Remove media files for this tweet
      const newMediaFiles = { ...tweetMediaFiles };
      delete newMediaFiles[index];
      // Reindex remaining media files
      const reindexed: typeof tweetMediaFiles = {};
      Object.entries(newMediaFiles).forEach(([key, value]) => {
        const oldIndex = parseInt(key);
        const newIndex = oldIndex > index ? oldIndex - 1 : oldIndex;
        reindexed[newIndex] = value;
      });
      setTweetMediaFiles(reindexed);
    }
  };

  const moveTweetUp = (index: number) => {
    if (index > 0) {
      moveTweet(index, index - 1);
      // Swap media files
      const newMediaFiles = { ...tweetMediaFiles };
      const temp = newMediaFiles[index];
      newMediaFiles[index] = newMediaFiles[index - 1];
      newMediaFiles[index - 1] = temp;
      setTweetMediaFiles(newMediaFiles);
    }
  };

  const moveTweetDown = (index: number) => {
    if (index < tweetFields.length - 1) {
      moveTweet(index, index + 1);
      // Swap media files
      const newMediaFiles = { ...tweetMediaFiles };
      const temp = newMediaFiles[index];
      newMediaFiles[index] = newMediaFiles[index + 1];
      newMediaFiles[index + 1] = temp;
      setTweetMediaFiles(newMediaFiles);
    }
  };

  const handleMediaUpload = (tweetIndex: number, files: Array<{ url: string; fileId: string; fileType?: string }>) => {
    setTweetMediaFiles(prev => ({
      ...prev,
      [tweetIndex]: [...(prev[tweetIndex] || []), ...files],
    }));
  };

  const handleMediaRemove = (tweetIndex: number, fileId: string) => {
    setTweetMediaFiles(prev => ({
      ...prev,
      [tweetIndex]: (prev[tweetIndex] || []).filter(file => file.fileId !== fileId),
    }));
  };

  const isLoading = createThread.isPending;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Create Thread</span>
          </DialogTitle>
          <DialogDescription>
            Compose a multi-tweet thread. Each tweet can have up to 280 characters and 4 media files.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Agent Selection */}
          <div className="space-y-2">
            <Label htmlFor="agentId">Select Agent</Label>
            <Select
              value={form.watch('agentId')}
              onValueChange={(value) => form.setValue('agentId', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose an agent..." />
              </SelectTrigger>
              <SelectContent>
                {agentsData?.agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.agentId && (
              <p className="text-sm text-red-500">{form.formState.errors.agentId.message}</p>
            )}
          </div>

          <Separator />

          {/* Thread Tweets */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Thread Tweets ({tweetFields.length})</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTweet}
                disabled={tweetFields.length >= 25}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Tweet
              </Button>
            </div>

            <div className="space-y-4">
              {tweetFields.map((field, index) => {
                const content = form.watch(`tweets.${index}.content`);
                const characterCount = content?.length || 0;
                const isOverLimit = characterCount > 280;
                const mediaFiles = tweetMediaFiles[index] || [];

                return (
                  <Card key={field.id} className={`relative ${index === 0 ? 'border-primary-500/50' : ''}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm flex items-center space-x-2">
                          <span>Tweet {index + 1}</span>
                          {index === 0 && (
                            <span className="text-xs bg-primary-500 text-white px-2 py-1 rounded">
                              Thread Start
                            </span>
                          )}
                        </CardTitle>
                        <div className="flex items-center space-x-1">
                          {/* Move buttons */}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => moveTweetUp(index)}
                            disabled={index === 0}
                          >
                            <Move className="h-3 w-3 rotate-180" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => moveTweetDown(index)}
                            disabled={index === tweetFields.length - 1}
                          >
                            <Move className="h-3 w-3" />
                          </Button>
                          {/* Remove button */}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-red-500 hover:text-red-600"
                            onClick={() => removeTweetAt(index)}
                            disabled={tweetFields.length <= 2}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Tweet Content */}
                      <div className="space-y-2">
                        <Textarea
                          {...form.register(`tweets.${index}.content`)}
                          placeholder={`What's happening in tweet ${index + 1}?`}
                          rows={3}
                          className={isOverLimit ? 'border-red-500' : ''}
                        />
                        <div className="flex items-center justify-between">
                          <span className={`text-sm ${isOverLimit ? 'text-red-500' : 'text-muted-foreground'}`}>
                            {characterCount}/280 characters
                          </span>
                          <UploadButton
                            endpoint="mediaUploader"
                            onUploadComplete={(files) => handleMediaUpload(index, files)}
                            className="h-8 px-3 text-xs"
                            disabled={mediaFiles.length >= 4}
                          />
                        </div>
                        {form.formState.errors.tweets?.[index]?.content && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.tweets[index]?.content?.message}
                          </p>
                        )}
                      </div>

                      {/* Media Preview */}
                      {mediaFiles.length > 0 && (
                        <MediaPreview
                          files={mediaFiles}
                          onRemove={(fileId) => handleMediaRemove(index, fileId)}
                          maxHeight="120px"
                        />
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" loading={isLoading} disabled={isLoading}>
              Create Thread ({tweetFields.length} tweets)
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}