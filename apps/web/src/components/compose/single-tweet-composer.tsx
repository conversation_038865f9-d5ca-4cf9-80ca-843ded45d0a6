import * as React from 'react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { createDraftSchema } from '@/lib/validations/draft';
import { useCreateDraft, useUpdateDraft } from '@/hooks/use-drafts';
import { useAgents } from '@/hooks/use-agents';
import { MediaPreview } from '@/components/media/media-preview';
import { UploadButton } from '@/components/ui/upload-button';
import { useMediaUpload } from '@/hooks/use-media-upload';
import { ScheduleForm } from '@/components/schedule/schedule-form';
import { AIAssistantButton } from '@/components/ai/ai-assistant-button';
import type { CreateDraftData, Draft } from '@/types/draft';
import { z } from 'zod';

interface SingleTweetComposerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  draft?: Draft;
  initialAgentId?: string;
}

type FormData = z.infer<typeof createDraftSchema>;

export function SingleTweetComposer({ 
  open, 
  onOpenChange, 
  draft, 
  initialAgentId 
}: SingleTweetComposerProps) {
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [savedDraft, setSavedDraft] = useState<Draft | null>(null);
  const isEditing = !!draft;
  const createDraft = useCreateDraft();
  const updateDraft = useUpdateDraft();
  const { data: agentsData } = useAgents({ limit: 100 });
  
  const {
    files: mediaFiles,
    addFiles,
    removeFile,
    clearFiles,
    handleUploadComplete,
  } = useMediaUpload({
    maxFiles: 4,
  });

  const form = useForm<FormData>({
    resolver: zodResolver(createDraftSchema),
    defaultValues: draft ? {
      content: draft.content,
      mediaUrls: draft.mediaUrls,
      agentId: draft.agentId,
      threadId: draft.threadId,
      threadOrder: draft.threadOrder,
      isThreadStart: draft.isThreadStart,
    } : {
      content: '',
      mediaUrls: [],
      agentId: initialAgentId || '',
      isThreadStart: false,
    },
  });

  // Initialize media files from draft
  React.useEffect(() => {
    if (draft?.mediaUrls) {
      const files = draft.mediaUrls.map((url, index) => ({
        url,
        fileId: `draft-${draft.id}-${index}`,
        fileType: url.includes('image') ? 'image' : 'video',
        filename: url.split('/').pop() || 'Media file',
      }));
      addFiles(files);
    }
  }, [draft, addFiles]);

  const onSubmit = (data: FormData) => {
    const submitData = {
      ...data,
      mediaUrls: mediaFiles.map(file => file.url),
    };

    if (isEditing && draft) {
      updateDraft.mutate(
        { id: draft.id, data: submitData },
        {
          onSuccess: () => {
            onOpenChange(false);
            form.reset();
            clearFiles();
          },
        }
      );
    } else {
      createDraft.mutate(submitData, {
        onSuccess: () => {
          setSavedDraft(newDraft);
          onOpenChange(false);
          form.reset();
          clearFiles();
        },
      });
    }
  };

  const handleSchedule = (data: FormData) => {
    const submitData = {
      ...data,
      mediaUrls: mediaFiles.map(file => file.url),
    };

    if (isEditing && draft) {
      updateDraft.mutate(
        { id: draft.id, data: submitData },
        {
          onSuccess: (updatedDraft) => {
            setSavedDraft(updatedDraft);
            setShowScheduleForm(true);
          },
        }
      );
    } else {
      createDraft.mutate(submitData, {
        onSuccess: (newDraft) => {
          setSavedDraft(newDraft);
          setShowScheduleForm(true);
        },
      });
    }
  };
  const handleClose = () => {
    onOpenChange(false);
    form.reset();
    clearFiles();
  };

  const content = form.watch('content');
  const characterCount = content?.length || 0;
  const isOverLimit = characterCount > 280;
  const isLoading = createDraft.isPending || updateDraft.isPending;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Draft' : 'Compose Tweet'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update your draft tweet content and settings.'
              : 'Create a new tweet draft with text and media.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Agent Selection */}
          <div className="space-y-2">
            <Label htmlFor="agentId">Select Agent</Label>
            <Select
              value={form.watch('agentId')}
              onValueChange={(value) => form.setValue('agentId', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose an agent..." />
              </SelectTrigger>
              <SelectContent>
                {agentsData?.agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.agentId && (
              <p className="text-sm text-red-500">{form.formState.errors.agentId.message}</p>
            )}
          </div>

          {/* Tweet Content */}
          <div className="space-y-2">
            <Label htmlFor="content">Tweet Content</Label>
            <Textarea
              id="content"
              {...form.register('content')}
              placeholder="What's happening?"
              rows={4}
              className={isOverLimit ? 'border-red-500' : ''}
            />
            <div className="flex items-center justify-between">
              <span className={`text-sm ${isOverLimit ? 'text-red-500' : 'text-muted-foreground'}`}>
                {characterCount}/280 characters
              </span>
              <div className="flex items-center space-x-2">
                <AIAssistantButton
                  onContentGenerated={(content) => form.setValue('content', content)}
                  agentId={form.watch('agentId')}
                  size="sm"
                />
                <UploadButton
                  endpoint="mediaUploader"
                  onUploadComplete={handleUploadComplete}
                  className="h-8 px-3 text-xs"
                  disabled={mediaFiles.length >= 4}
                />
              </div>
            </div>
            {form.formState.errors.content && (
              <p className="text-sm text-red-500">{form.formState.errors.content.message}</p>
            )}
          </div>

          {/* Media Preview */}
          {mediaFiles.length > 0 && (
            <div className="space-y-2">
              <Label>Media Files ({mediaFiles.length}/4)</Label>
              <MediaPreview
                files={mediaFiles}
                onRemove={removeFile}
              />
            </div>
          )}

          {/* Thread Info (if editing thread tweet) */}
          {draft?.threadId && (
            <Card className="bg-muted/50">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Thread Information</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-muted-foreground">
                <p>This tweet is part of a thread.</p>
                <p>Position: Tweet {(draft.threadOrder || 0) + 1}</p>
                {draft.isThreadStart && <p>This is the first tweet in the thread.</p>}
              </CardContent>
            </Card>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="button"
              variant="outline"
              onClick={form.handleSubmit(handleSchedule)}
              loading={isLoading} 
              disabled={isLoading || isOverLimit || !content?.trim()}
            >
              Schedule
            </Button>
            <Button 
              type="submit" 
              loading={isLoading} 
              disabled={isLoading || isOverLimit || !content?.trim()}
            >
              {isEditing ? 'Update Draft' : 'Save Draft'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
      
      {/* Schedule Form */}
      {savedDraft && (
        <ScheduleForm
          draft={savedDraft}
          open={showScheduleForm}
          onOpenChange={(open) => {
            setShowScheduleForm(open);
            if (!open) {
              setSavedDraft(null);
              onOpenChange(false);
            }
          }}
        />
      )}
    </Dialog>
  );
}