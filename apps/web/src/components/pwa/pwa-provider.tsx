'use client'

import * as React from 'react'
import { useToast } from '@/hooks/use-toast'

interface PWAProviderProps {
  children: React.ReactNode
}

export function PWAProvider({ children }: PWAProviderProps) {
  const { toast } = useToast()
  const [isOnline, setIsOnline] = React.useState(true)
  const [installPrompt, setInstallPrompt] = React.useState<any>(null)

  React.useEffect(() => {
    // Register service worker
    if ('serviceWorker' in navigator) {
      registerServiceWorker()
    }

    // Handle install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setInstallPrompt(e)
    }

    // Handle online/offline status
    const handleOnline = () => {
      setIsOnline(true)
      toast({
        title: "Back online",
        description: "Your internet connection has been restored.",
      })
    }

    const handleOffline = () => {
      setIsOnline(false)
      toast({
        title: "You're offline",
        description: "Some features may be limited while offline.",
        variant: "destructive",
      })
    }

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Set initial online status
    setIsOnline(navigator.onLine)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [toast])

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      })

      console.log('Service Worker registered successfully:', registration)

      // Handle service worker updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              toast({
                title: "App updated",
                description: "A new version is available. Refresh to update.",
                action: (
                  <button
                    onClick={() => window.location.reload()}
                    className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm"
                  >
                    Refresh
                  </button>
                ),
              })
            }
          })
        }
      })

    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }

  const installApp = async () => {
    if (!installPrompt) return

    try {
      const result = await installPrompt.prompt()
      console.log('Install prompt result:', result)
      
      if (result.outcome === 'accepted') {
        toast({
          title: "App installed",
          description: "XTask has been installed on your device.",
        })
      }
      
      setInstallPrompt(null)
    } catch (error) {
      console.error('Install prompt failed:', error)
    }
  }

  // Provide PWA context
  const pwaContext = {
    isOnline,
    installPrompt,
    installApp,
    canInstall: !!installPrompt,
  }

  return (
    <PWAContext.Provider value={pwaContext}>
      {children}
      
      {/* Offline indicator */}
      {!isOnline && (
        <div className="fixed bottom-4 left-4 right-4 z-50 bg-yellow-500/90 text-yellow-900 px-4 py-2 rounded-lg text-sm font-medium text-center backdrop-blur-sm">
          You're currently offline. Some features may be limited.
        </div>
      )}
      
      {/* Install prompt */}
      {installPrompt && (
        <div className="fixed bottom-4 left-4 right-4 z-50 bg-primary/90 text-primary-foreground px-4 py-3 rounded-lg backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Install XTask</p>
              <p className="text-sm opacity-90">Add to your home screen for quick access</p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setInstallPrompt(null)}
                className="px-3 py-1 text-sm opacity-75 hover:opacity-100"
              >
                Later
              </button>
              <button
                onClick={installApp}
                className="px-3 py-1 bg-white/20 text-sm rounded hover:bg-white/30"
              >
                Install
              </button>
            </div>
          </div>
        </div>
      )}
    </PWAContext.Provider>
  )
}

// PWA Context
interface PWAContextType {
  isOnline: boolean
  installPrompt: any
  installApp: () => Promise<void>
  canInstall: boolean
}

const PWAContext = React.createContext<PWAContextType | undefined>(undefined)

export function usePWA() {
  const context = React.useContext(PWAContext)
  if (!context) {
    throw new Error('usePWA must be used within a PWAProvider')
  }
  return context
}

// Hook for offline detection
export function useOnlineStatus() {
  const { isOnline } = usePWA()
  return isOnline
}

// Hook for app installation
export function useAppInstall() {
  const { canInstall, installApp } = usePWA()
  return { canInstall, installApp }
}
