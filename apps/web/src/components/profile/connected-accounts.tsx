'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useProfile } from '@/hooks/use-profile'
import { useToast } from '@/hooks/use-toast'
import { ConnectedAccount } from '@/lib/validations/user'
import { Twitter, Mail, Unlink, ExternalLink, Loader2 } from 'lucide-react'

// Simple date formatting function
const formatTimeAgo = (date: Date | string) => {
  const now = new Date()
  const past = new Date(date)
  const diffInMs = now.getTime() - past.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

  if (diffInDays === 0) return 'today'
  if (diffInDays === 1) return '1 day ago'
  if (diffInDays < 30) return `${diffInDays} days ago`
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`
  return `${Math.floor(diffInDays / 365)} years ago`
}

export function ConnectedAccounts() {
  const { profile, isLoading, refreshProfile } = useProfile()
  const { toast } = useToast()
  const [disconnectingId, setDisconnectingId] = React.useState<string | null>(null)

  const handleConnect = (provider: 'twitter' | 'google') => {
    // Redirect to OAuth flow
    window.location.href = `/api/auth/${provider}?action=connect`
  }

  const handleDisconnect = async (accountId: string, provider: string) => {
    setDisconnectingId(accountId)
    try {
      const response = await fetch(`/api/auth/accounts/${accountId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to disconnect account')
      }

      toast({
        title: 'Account disconnected',
        description: `Your ${provider} account has been disconnected successfully.`,
      })

      await refreshProfile()
    } catch (error) {
      toast({
        title: 'Disconnect failed',
        description: 'Failed to disconnect the account. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setDisconnectingId(null)
    }
  }

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'twitter':
        return <Twitter className="h-5 w-5" />
      case 'google':
        return <Mail className="h-5 w-5" />
      default:
        return <ExternalLink className="h-5 w-5" />
    }
  }

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'twitter':
        return 'bg-blue-500'
      case 'google':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  const connectedAccounts = profile?.connectedAccounts || []
  const hasTwitter = connectedAccounts.some(account => account.provider === 'twitter')
  const hasGoogle = connectedAccounts.some(account => account.provider === 'google')

  return (
    <div className="space-y-6">
      {/* Connected Accounts */}
      <Card>
        <CardHeader>
          <CardTitle>Connected Accounts</CardTitle>
          <CardDescription>
            Manage your connected social media and service accounts.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {connectedAccounts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No accounts connected yet.</p>
              <p className="text-sm text-muted-foreground mt-1">
                Connect your social media accounts to get started.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {connectedAccounts.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-full text-white ${getProviderColor(account.provider)}`}>
                      {getProviderIcon(account.provider)}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium capitalize">{account.provider}</h4>
                        {account.isActive ? (
                          <Badge variant="default" className="text-xs">Active</Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">Inactive</Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>{account.displayName || account.username}</p>
                        {account.username && account.displayName && (
                          <p className="text-xs">@{account.username}</p>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Connected {formatTimeAgo(account.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {account.profileImageUrl && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={account.profileImageUrl} alt={account.displayName || account.username} />
                        <AvatarFallback>
                          {(account.displayName || account.username || '?')[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect(account.id, account.provider)}
                      disabled={disconnectingId === account.id}
                    >
                      {disconnectingId === account.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <Unlink className="h-4 w-4 mr-1" />
                          Disconnect
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Available Connections */}
      <Card>
        <CardHeader>
          <CardTitle>Available Connections</CardTitle>
          <CardDescription>
            Connect additional accounts to expand your capabilities.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!hasTwitter && (
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="p-2 rounded-full text-white bg-blue-500">
                  <Twitter className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium">Twitter / X</h4>
                  <p className="text-sm text-muted-foreground">
                    Connect your Twitter account to post and manage tweets.
                  </p>
                </div>
              </div>
              <Button onClick={() => handleConnect('twitter')}>
                Connect
              </Button>
            </div>
          )}

          {!hasGoogle && (
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="p-2 rounded-full text-white bg-red-500">
                  <Mail className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium">Google</h4>
                  <p className="text-sm text-muted-foreground">
                    Connect your Google account for additional features.
                  </p>
                </div>
              </div>
              <Button onClick={() => handleConnect('google')}>
                Connect
              </Button>
            </div>
          )}

          {hasTwitter && hasGoogle && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">All available accounts are connected.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
