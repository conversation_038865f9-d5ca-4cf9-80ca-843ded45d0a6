'use client'

import * as React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { useProfile } from '@/hooks/use-profile'
import { updateUserProfileSchema, UpdateUserProfileData } from '@/lib/validations/user'
import { Loader2, Upload } from 'lucide-react'

export function ProfileForm() {
  const { profile, isLoading, updateProfile } = useProfile()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  const form = useForm<UpdateUserProfileData>({
    resolver: zodResolver(updateUserProfileSchema),
    defaultValues: {
      name: profile?.name || '',
      email: profile?.email || '',
      avatar: profile?.avatar || '',
      preferences: {
        timezone: profile?.preferences?.timezone || '',
        notifications: {
          email: profile?.preferences?.notifications?.email ?? true,
          push: profile?.preferences?.notifications?.push ?? true,
          marketing: profile?.preferences?.notifications?.marketing ?? false,
        },
        theme: profile?.preferences?.theme || 'system',
        language: profile?.preferences?.language || 'en',
      },
    },
  })

  // Update form when profile data loads
  React.useEffect(() => {
    if (profile) {
      form.reset({
        name: profile.name || '',
        email: profile.email || '',
        avatar: profile.avatar || '',
        preferences: {
          timezone: profile.preferences?.timezone || '',
          notifications: {
            email: profile.preferences?.notifications?.email ?? true,
            push: profile.preferences?.notifications?.push ?? true,
            marketing: profile.preferences?.notifications?.marketing ?? false,
          },
          theme: profile.preferences?.theme || 'system',
          language: profile.preferences?.language || 'en',
        },
      })
    }
  }, [profile, form])

  const onSubmit = async (data: UpdateUserProfileData) => {
    setIsSubmitting(true)
    try {
      const success = await updateProfile(data)
      if (success) {
        toast({
          title: 'Profile updated',
          description: 'Your profile has been updated successfully.',
        })
      } else {
        toast({
          title: 'Update failed',
          description: 'Failed to update your profile. Please try again.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Update failed',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  const initials = profile?.name
    ? profile.name
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
    : profile?.email[0].toUpperCase()

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Profile Picture */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Picture</CardTitle>
          <CardDescription>
            Update your profile picture to personalize your account.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={form.watch('avatar')} alt={profile?.name || profile?.email} />
              <AvatarFallback className="text-lg">{initials}</AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <Label htmlFor="avatar">Avatar URL</Label>
              <Input
                id="avatar"
                placeholder="https://example.com/avatar.jpg"
                {...form.register('avatar')}
              />
              <p className="text-sm text-muted-foreground">
                Enter a URL to your profile picture or upload one using the button below.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Update your personal information and contact details.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              placeholder="Enter your full name"
              {...form.register('name')}
            />
            {form.formState.errors.name && (
              <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email address"
              {...form.register('email')}
            />
            {form.formState.errors.email && (
              <p className="text-sm text-destructive">{form.formState.errors.email.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Preferences</CardTitle>
          <CardDescription>
            Customize your experience with these preference settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Input
              id="timezone"
              placeholder="America/New_York"
              {...form.register('preferences.timezone')}
            />
            <p className="text-sm text-muted-foreground">
              Enter your timezone in IANA format (e.g., America/New_York, Europe/London).
            </p>
          </div>

          <div className="space-y-2">
            <Label>Theme</Label>
            <Select
              value={form.watch('preferences.theme')}
              onValueChange={(value) => form.setValue('preferences.theme', value as 'light' | 'dark' | 'system')}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
                <SelectItem value="system">System</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Language</Label>
            <Select
              value={form.watch('preferences.language')}
              onValueChange={(value) => form.setValue('preferences.language', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="space-y-4">
            <Label className="text-base font-medium">Notifications</Label>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications via email
                </p>
              </div>
              <Switch
                id="email-notifications"
                checked={form.watch('preferences.notifications.email')}
                onCheckedChange={(checked) => form.setValue('preferences.notifications.email', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="push-notifications">Push Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive push notifications in your browser
                </p>
              </div>
              <Switch
                id="push-notifications"
                checked={form.watch('preferences.notifications.push')}
                onCheckedChange={(checked) => form.setValue('preferences.notifications.push', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="marketing-notifications">Marketing Emails</Label>
                <p className="text-sm text-muted-foreground">
                  Receive marketing and promotional emails
                </p>
              </div>
              <Switch
                id="marketing-notifications"
                checked={form.watch('preferences.notifications.marketing')}
                onCheckedChange={(checked) => form.setValue('preferences.notifications.marketing', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Changes
        </Button>
      </div>
    </form>
  )
}
