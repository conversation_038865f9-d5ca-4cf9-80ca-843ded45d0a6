import { NextRequest, NextResponse } from 'next/server'
import { validateSessionFromRequest, refreshSession } from '@/lib/auth/session'
import { applySecurityMiddleware, createSecurityHeaders } from '@/lib/security/middleware'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/agents',
  '/compose',
  '/schedule',
  '/analytics',
  '/settings',
  '/api/agents',
  '/api/tweets',
  '/api/schedule',
  '/api/analytics'
]

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/api/auth/google',
  '/api/auth/twitter',
  '/api/auth/google/callback',
  '/api/auth/twitter/callback',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/refresh',
  '/api/auth/logout',
  '/api/auth/me',
  '/api/health',
  '/api/csrf-token',
  '/api/rate-limit-status',
  '/api/test-security',
  '/security-test',
  '/components-showcase',
  '/styling-test',
  '/test-styling'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next()
  }
  
  // Apply security middleware for API routes
  if (pathname.startsWith('/api/')) {
    const securityResponse = await applySecurityMiddleware(request)
    if (securityResponse) {
      return securityResponse
    }
  }
  
  // Check if route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  )
  
  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route)
  )
  
  // Allow public routes
  if (isPublicRoute && !isProtectedRoute) {
    const response = NextResponse.next()
    
    // Add security headers to all responses
    const securityHeaders = createSecurityHeaders()
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value)
    })
    
    return response
  }
  
  // For protected routes, validate session
  if (isProtectedRoute) {
    const session = await validateSessionFromRequest(request)
    
    if (!session) {
      // Try to refresh session
      const refreshed = await refreshSession(request)
      
      if (!refreshed) {
        // Redirect to login for web pages
        if (!pathname.startsWith('/api')) {
          const loginUrl = new URL('/login', request.url)
          loginUrl.searchParams.set('redirect', pathname)
          return NextResponse.redirect(loginUrl)
        }
        
        // Return 401 for API routes
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
    }
    
    // Add user info to request headers for API routes
    if (pathname.startsWith('/api') && session) {
      const requestHeaders = new Headers(request.headers)
      requestHeaders.set('x-user-id', session.userId)
      requestHeaders.set('x-user-email', session.email)
      
      const response = NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
      
      // Add security headers
      const securityHeaders = createSecurityHeaders()
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value)
      })
      
      return response
    }
  }
  
  const response = NextResponse.next()
  
  // Add security headers to all responses
  const securityHeaders = createSecurityHeaders()
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}