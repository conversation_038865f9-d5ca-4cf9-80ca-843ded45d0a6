export interface PersonaData {
  personality: string
  tone: string
  topics: string[]
  writingStyle: string
  restrictions: string[]
  expertise?: string[]
  communicationStyle?: string
  targetAudience?: string
  contentThemes?: string[]
  avoidTopics?: string[]
}

export interface Agent {
  id: string
  name: string
  description: string
  personaData: PersonaData
  aiProvider: 'openai' | 'google'
  aiModel: string
  isActive: boolean
  tweetsGenerated: number
  engagementRate: number
  maxDailyTweets: number
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateAgentData {
  name: string
  description: string
  personaData: PersonaData
  aiProvider: 'openai' | 'google'
  aiModel: string
  maxDailyTweets: number
}

export interface UpdateAgentData {
  name?: string
  description?: string
  personaData?: PersonaData
  aiProvider?: 'openai' | 'google'
  aiModel?: string
  isActive?: boolean
  maxDailyTweets?: number
}

export interface AgentStats {
  totalAgents: number
  activeAgents: number
  totalTweets: number
  avgEngagementRate: number
}

export interface AgentsListResponse {
  agents: Agent[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export const AI_PROVIDERS = {
  openai: {
    name: 'OpenAI',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']
  },
  google: {
    name: 'Google',
    models: ['gemini-pro', 'gemini-pro-vision']
  }
} as const