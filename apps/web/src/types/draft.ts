export interface Draft {
  agent: any;
  agent: any;
  id: string;
  content: string;
  mediaUrls: string[];
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  threadId?: string;
  threadOrder?: number;
  isThreadStart: boolean;
  agentId: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Thread {
  id: string;
  tweets: Draft[];
  totalTweets: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDraftData {
  content: string;
  mediaUrls?: string[];
  agentId: string;
  threadId?: string;
  threadOrder?: number;
  isThreadStart?: boolean;
}

export interface UpdateDraftData {
  content?: string;
  mediaUrls?: string[];
  threadOrder?: number;
}

export interface CreateThreadData {
  tweets: Array<{
    content: string;
    mediaUrls?: string[];
  }>;
  agentId: string;
}

export interface DraftListResponse {
  drafts: Draft[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}