export interface AIGenerationRequest {
  type: 'tweet' | 'thread' | 'improve' | 'custom'
  agentId: string
  model?: string
  topic?: string
  content?: string
  tweetCount?: number
  improvementType?: 'engagement' | 'clarity' | 'tone' | 'length'
  customPrompt?: string
}

export interface AIGenerationResponse {
  success: boolean
  data: {
    content: string
    usage: {
      promptTokens: number
      completionTokens: number
      totalTokens: number
    }
    model: string
    type: string
    agentId: string
  }
}

export interface ContentSuggestion {
  suggestions: string[]
  agentId: string
  topics: string[]
  count: number
}

export interface AIUsageMetrics {
  totalTokens: number
  totalRequests: number
  averageTokensPerRequest: number
  costEstimate: number
  lastUsed: Date
}

export interface AIError {
  code: string
  message: string
  type: 'api_error' | 'rate_limit' | 'invalid_request' | 'authentication' | 'timeout'
}

export interface ModelInfo {
  id: string
  name: string
  description?: string
  contextLength?: number
  provider?: string
}

export interface ModelsResponse {
  models: ModelInfo[]
  provider: string
  defaultModel: string
}