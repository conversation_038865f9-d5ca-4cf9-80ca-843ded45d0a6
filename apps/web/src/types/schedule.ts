export interface ScheduledTweet {
  id: string
  content: string
  mediaUrls: string[]
  scheduledFor: Date | null
  publishedAt: Date | null
  status: 'draft' | 'scheduled' | 'published' | 'failed'
  twitterTweetId: string | null
  threadId: string | null
  threadOrder: number | null
  isThreadStart: boolean
  agentId: string
  userId: string
  twitterAccountId: string | null
  createdAt: Date
  updatedAt: Date
  agent?: {
    id: string
    name: string
  }
  twitterAccount?: {
    id: string
    username: string
  }
}

export interface ScheduledTweetListResponse {
  tweets: ScheduledTweet[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface ScheduleData {
  draftId: string
  scheduledFor: string
  twitterAccountId?: string
}

export interface RescheduleData {
  scheduledFor?: string
  twitterAccountId?: string
}