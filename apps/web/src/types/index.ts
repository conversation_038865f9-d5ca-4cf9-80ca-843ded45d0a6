export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface Agent {
  id: string
  name: string
  description: string
  personaData: PersonaSchema
  aiProvider: 'openai' | 'google'
  aiModel: string
  isActive: boolean
  tweetsGenerated: number
  engagementRate: number
  maxDailyTweets: number
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface PersonaSchema {
  personality: string
  tone: string
  topics: string[]
  writingStyle: string
  restrictions: string[]
}

export interface Tweet {
  id: string
  content: string
  mediaUrls?: string[]
  scheduledFor?: Date
  publishedAt?: Date
  status: 'draft' | 'scheduled' | 'published' | 'failed'
  agentId: string
  userId: string
  metrics?: TweetMetrics
  createdAt: Date
  updatedAt: Date
}

export interface TweetMetrics {
  likes: number
  retweets: number
  replies: number
  impressions: number
  engagementRate: number
}

export interface Schedule {
  id: string
  tweetId: string
  scheduledFor: Date
  timezone: string
  status: 'pending' | 'sent' | 'failed'
  userId: string
  createdAt: Date
}

export interface Analytics {
  totalTweets: number
  totalEngagement: number
  followerGrowth: number
  topPerformingTweet: Tweet
  engagementRate: number
  reachMetrics: {
    impressions: number
    reach: number
    profileVisits: number
  }
}