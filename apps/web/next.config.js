/** @type {import('next').NextConfig} */
const nextConfig = {
  // Production optimizations
  compress: true,
  poweredByHeader: false,

  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Transpile packages if needed
  transpilePackages: [],

  // Environment variables (automatically loaded from .env)
  env: {
    NODE_ENV: process.env.NODE_ENV,
  },
};

module.exports = nextConfig;
