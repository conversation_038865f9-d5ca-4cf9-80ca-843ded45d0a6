const path = require('path')
const { config } = require('dotenv')

// Load environment variables from the monorepo root
config({ path: path.resolve(__dirname, '../../.env') })

// Get environment-aware configuration
const { getEnvConfig } = require('../../scripts/env-config')
const envConfig = getEnvConfig()

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Allow cross-origin requests in development
  allowedDevOrigins: ['*************'],

  // Environment variables configuration (automatically switches based on NODE_ENV in .env)
  env: {
    NODE_ENV: envConfig.NODE_ENV,
    FRONTEND_PORT: envConfig.FRONTEND_PORT,
    BACKEND_PORT: envConfig.PORT,
    FRONTEND_URL: envConfig.FRONTEND_URL,
    BACKEND_URL: envConfig.BACKEND_URL,
    NEXTAUTH_URL: envConfig.NEXTAUTH_URL,
    GOOGLE_CALLBACK_URL: envConfig.GOOGLE_CALLBACK_URL,
    TWITTER_CALLBACK_URL: envConfig.TWITTER_CALLBACK_URL,
  },

  // Allow external access in development
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },
};

module.exports = nextConfig;
