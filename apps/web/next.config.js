const path = require('path')
const { config } = require('dotenv')

// Load environment variables from the monorepo root
config({ path: path.resolve(__dirname, '../../.env') })

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Allow cross-origin requests in development
  allowedDevOrigins: ['*************'],

  // Environment variables configuration
  env: {
    FRONTEND_PORT: process.env.FRONTEND_PORT || '3030',
    BACKEND_PORT: process.env.PORT || '3000',
    FRONTEND_URL: process.env.FRONTEND_URL || `http://localhost:${process.env.FRONTEND_PORT || '3030'}`,
    BACKEND_URL: process.env.BACKEND_URL || `http://localhost:${process.env.PORT || '3000'}`,
  },

  // Allow external access in development
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },
};

module.exports = nextConfig;
