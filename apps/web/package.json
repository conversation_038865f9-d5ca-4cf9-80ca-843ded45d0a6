{"name": "xtask-web", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 npx next dev", "build": "npx next build", "start": "npx next start", "lint": "npx next lint", "type-check": "tsc --noEmit", "db:setup": "cd ../.. && npm run db:setup", "db:migrate": "cd ../.. && npm run db:migrate", "db:generate": "cd ../.. && npm run db:generate", "db:studio": "cd ../.. && npm run db:studio", "db:reset": "cd ../.. && npm run db:reset"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@prisma/client": "^5.8.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.59.0", "@uploadthing/react": "^6.8.0", "@vscode/openssl-prebuilt": "^0.0.11", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto": "^1.0.1", "framer-motion": "^11.11.1", "ioredis": "^5.3.2", "jose": "^5.2.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.460.0", "next": "15.3.3", "next-themes": "^0.2.1", "openssl-nodejs": "^1.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.13.3", "sonner": "^1.4.41", "openai": "^4.24.0", "ai": "^3.0.0", "twitter-api-v2": "^1.17.2", "zod": "^3.23.8", "zustand": "^4.5.5", "@tiptap/react": "^2.1.13", "@tiptap/pm": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-character-count": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@google/genai": "^0.3.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/ioredis": "^4.28.5", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^22.7.4", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.1", "eslint-config-next": "15.3.3", "postcss": "^8.4.47", "prisma": "^5.8.0", "tailwindcss": "^3.4.13", "typescript": "^5.6.2"}}