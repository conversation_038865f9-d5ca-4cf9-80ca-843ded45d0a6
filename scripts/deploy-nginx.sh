#!/bin/bash

# Nginx Deployment Script for Tasker Application
# This script sets up nginx configurations for both frontend and backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
NGINX_CONF_DIR="/etc/nginx"
LOG_DIR="/var/log/nginx"

echo -e "${GREEN}🚀 Tasker Nginx Deployment Script${NC}"
echo "=================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (use sudo)${NC}"
   exit 1
fi

# Check if nginx is installed
if ! command -v nginx &> /dev/null; then
    echo -e "${YELLOW}⚠️  Nginx not found. Installing nginx...${NC}"
    apt update
    apt install -y nginx
fi

# Create log directory if it doesn't exist
mkdir -p $LOG_DIR

# Backup existing configurations
echo -e "${YELLOW}📦 Backing up existing configurations...${NC}"
if [ -f "$NGINX_SITES_AVAILABLE/tasker.violetmethods.com" ]; then
    cp "$NGINX_SITES_AVAILABLE/tasker.violetmethods.com" "$NGINX_SITES_AVAILABLE/tasker.violetmethods.com.backup.$(date +%Y%m%d_%H%M%S)"
fi

if [ -f "$NGINX_SITES_AVAILABLE/api.tasker.violetmethods.com" ]; then
    cp "$NGINX_SITES_AVAILABLE/api.tasker.violetmethods.com" "$NGINX_SITES_AVAILABLE/api.tasker.violetmethods.com.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Copy configuration files
echo -e "${YELLOW}📋 Copying nginx configurations...${NC}"
cp "$(dirname "$0")/../nginx/tasker.violetmethods.com.conf" "$NGINX_SITES_AVAILABLE/tasker.violetmethods.com"
cp "$(dirname "$0")/../nginx/api.tasker.violetmethods.com.conf" "$NGINX_SITES_AVAILABLE/api.tasker.violetmethods.com"

# Set proper permissions
chmod 644 "$NGINX_SITES_AVAILABLE/tasker.violetmethods.com"
chmod 644 "$NGINX_SITES_AVAILABLE/api.tasker.violetmethods.com"

# Enable sites
echo -e "${YELLOW}🔗 Enabling sites...${NC}"
ln -sf "$NGINX_SITES_AVAILABLE/tasker.violetmethods.com" "$NGINX_SITES_ENABLED/"
ln -sf "$NGINX_SITES_AVAILABLE/api.tasker.violetmethods.com" "$NGINX_SITES_ENABLED/"

# Remove default nginx site if it exists
if [ -f "$NGINX_SITES_ENABLED/default" ]; then
    echo -e "${YELLOW}🗑️  Removing default nginx site...${NC}"
    rm -f "$NGINX_SITES_ENABLED/default"
fi

# Test nginx configuration
echo -e "${YELLOW}🧪 Testing nginx configuration...${NC}"
if nginx -t; then
    echo -e "${GREEN}✅ Nginx configuration test passed${NC}"
else
    echo -e "${RED}❌ Nginx configuration test failed${NC}"
    exit 1
fi

# Check if SSL certificates exist
echo -e "${YELLOW}🔒 Checking SSL certificates...${NC}"
if [ ! -f "/etc/letsencrypt/live/tasker.violetmethods.com/fullchain.pem" ]; then
    echo -e "${YELLOW}⚠️  SSL certificate for tasker.violetmethods.com not found${NC}"
    echo -e "${YELLOW}   Run: certbot --nginx -d tasker.violetmethods.com -d www.tasker.violetmethods.com${NC}"
fi

if [ ! -f "/etc/letsencrypt/live/api.tasker.violetmethods.com/fullchain.pem" ]; then
    echo -e "${YELLOW}⚠️  SSL certificate for api.tasker.violetmethods.com not found${NC}"
    echo -e "${YELLOW}   Run: certbot --nginx -d api.tasker.violetmethods.com${NC}"
fi

# Reload nginx
echo -e "${YELLOW}🔄 Reloading nginx...${NC}"
systemctl reload nginx

# Enable nginx to start on boot
systemctl enable nginx

echo -e "${GREEN}✅ Nginx deployment completed successfully!${NC}"
echo ""
echo -e "${GREEN}📋 Summary:${NC}"
echo "• Frontend: https://tasker.violetmethods.com"
echo "• Backend API: https://api.tasker.violetmethods.com"
echo "• Nginx configurations installed and enabled"
echo "• Log files: $LOG_DIR/tasker.violetmethods.com.*.log"
echo "• Log files: $LOG_DIR/api.tasker.violetmethods.com.*.log"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Ensure your applications are running:"
echo "   - Frontend on port 3030"
echo "   - Backend on port 3000"
echo "2. Set up SSL certificates if not already done:"
echo "   - certbot --nginx -d tasker.violetmethods.com -d www.tasker.violetmethods.com"
echo "   - certbot --nginx -d api.tasker.violetmethods.com"
echo "3. Update your DNS records to point to this server"
echo "4. Test the applications in your browser"
echo ""
echo -e "${GREEN}🎉 Your Tasker application is ready for production!${NC}"
