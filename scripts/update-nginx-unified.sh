#!/bin/bash

# Script to update nginx configuration for unified port 3030 setup
# This replaces the current nginx config with the unified version

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔄 Updating Nginx Configuration for Unified Port 3030${NC}"
echo "============================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (use sudo)${NC}"
   exit 1
fi

# Backup current configuration
echo -e "${YELLOW}📦 Backing up current nginx configuration...${NC}"
cp /etc/nginx/sites-available/tasker.violetmethods.com.conf /etc/nginx/sites-available/tasker.violetmethods.com.conf.backup.$(date +%Y%m%d_%H%M%S)

# Copy new unified configuration
echo -e "${YELLOW}📋 Installing new unified nginx configuration...${NC}"
cp "$(dirname "$0")/../nginx/tasker.violetmethods.com.conf" /etc/nginx/sites-available/tasker.violetmethods.com.conf

# Set proper permissions
chmod 644 /etc/nginx/sites-available/tasker.violetmethods.com.conf

# Test nginx configuration
echo -e "${YELLOW}🧪 Testing nginx configuration...${NC}"
if nginx -t; then
    echo -e "${GREEN}✅ Nginx configuration test passed${NC}"
else
    echo -e "${RED}❌ Nginx configuration test failed${NC}"
    echo -e "${YELLOW}🔄 Restoring backup...${NC}"
    cp /etc/nginx/sites-available/tasker.violetmethods.com.conf.backup.$(date +%Y%m%d_%H%M%S) /etc/nginx/sites-available/tasker.violetmethods.com.conf
    exit 1
fi

# Reload nginx
echo -e "${YELLOW}🔄 Reloading nginx...${NC}"
systemctl reload nginx

echo -e "${GREEN}✅ Nginx configuration updated successfully!${NC}"
echo ""
echo -e "${GREEN}📋 Configuration Summary:${NC}"
echo "• Frontend: https://tasker.violetmethods.com → localhost:3030"
echo "• Backend API: https://tasker.violetmethods.com/api → localhost:3030"
echo "• WebSocket: https://tasker.violetmethods.com/ws → localhost:3030"
echo "• Health Check: https://tasker.violetmethods.com/health → localhost:3030"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Make sure your unified application is running on port 3030"
echo "2. Test the endpoints:"
echo "   - curl https://tasker.violetmethods.com/health"
echo "   - curl https://tasker.violetmethods.com/api/test"
echo "3. Check nginx logs if needed:"
echo "   - tail -f /var/log/nginx/tasker.violetmethods.com.access.log"
echo "   - tail -f /var/log/nginx/tasker.violetmethods.com.error.log"
echo ""
echo -e "${GREEN}🎉 Your unified Tasker application is ready!${NC}"
