#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

const envPath = path.resolve(__dirname, '../.env')
const targetEnv = process.argv[2]

if (!targetEnv || !['development', 'production'].includes(targetEnv)) {
  console.error('❌ Usage: node set-env.js [development|production]')
  process.exit(1)
}

try {
  // Read current .env file
  let envContent = fs.readFileSync(envPath, 'utf8')
  
  // Update NODE_ENV line
  envContent = envContent.replace(
    /^NODE_ENV=.*/m,
    `NODE_ENV=${targetEnv}`
  )
  
  // Write back to .env file
  fs.writeFileSync(envPath, envContent)
  
  console.log(`✅ Environment set to: ${targetEnv}`)
  console.log('')
  
  // Show current configuration
  const { getEnvConfig, printConfig } = require('./env-config')
  printConfig()
  
} catch (error) {
  console.error('❌ Error updating .env file:', error.message)
  process.exit(1)
}
