#!/usr/bin/env node

/**
 * Security Audit Script for Environment Variables
 * 
 * This script audits the codebase to ensure:
 * 1. No sensitive environment variables are exposed to the client
 * 2. Only properly prefixed variables are used on the client-side
 * 3. Server-side variables are not accessible from client code
 * 
 * Usage:
 *   node scripts/security-audit.js
 *   npm run security-audit
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

// Sensitive environment variables that should NEVER be exposed to client
const SENSITIVE_VARS = [
  'DATABASE_URL',
  'DIRECT_URL',
  'NEXTAUTH_SECRET',
  'GOOGLE_CLIENT_SECRET',
  'TWITTER_CLIENT_SECRET',
  'UPLOADTHING_SECRET',
  'UPLOADTHING_TOKEN',
  'OPENAI_API_KEY',
  'GEMINI_API_KEY',
  'MISTRAL_API_KEY',
  'HUGGINGFACE_API_KEY',
  'GROQ_API_KEY',
  'OPENROUTER_API_KEY',
  'REDIS_URL'
];

// Variables that can be safely exposed to client (with NEXT_PUBLIC_ prefix)
const PUBLIC_VARS = [
  'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
  'NEXT_PUBLIC_NEXTAUTH_URL',
  'NEXT_PUBLIC_FRONTEND_URL',
  'NEXT_PUBLIC_UPLOADTHING_APP_ID'
];

// Directories to scan
const SCAN_DIRS = [
  'apps/web/src',
  'packages/server/src',
  'lib'
];

// File extensions to scan
const SCAN_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

function printStatus(message, color = 'blue') {
  console.log(`${colors[color]}[INFO]${colors.reset} ${message}`);
}

function printWarning(message) {
  console.log(`${colors.yellow}[WARN]${colors.reset} ${message}`);
}

function printError(message) {
  console.log(`${colors.red}[ERROR]${colors.reset} ${message}`);
}

function printSuccess(message) {
  console.log(`${colors.green}[SUCCESS]${colors.reset} ${message}`);
}

function printHeader(message) {
  console.log(`\n${colors.blue}${'='.repeat(80)}${colors.reset}`);
  console.log(`${colors.blue} ${message}${colors.reset}`);
  console.log(`${colors.blue}${'='.repeat(80)}${colors.reset}\n`);
}

// Function to recursively find files
function findFiles(dir, extensions) {
  const files = [];
  
  if (!fs.existsSync(dir)) {
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...findFiles(fullPath, extensions));
    } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to scan file for environment variable usage
function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    // Check for process.env usage
    const envMatches = line.match(/process\.env\.([A-Z_][A-Z0-9_]*)/g);
    if (envMatches) {
      envMatches.forEach(match => {
        const varName = match.replace('process.env.', '');
        
        // Check if sensitive variable is used in client-side code
        if (SENSITIVE_VARS.includes(varName)) {
          const isClientSide = filePath.includes('apps/web/src') && 
                              !filePath.includes('app/api/') &&
                              !filePath.includes('middleware.ts');
          
          if (isClientSide) {
            issues.push({
              type: 'SENSITIVE_VAR_IN_CLIENT',
              file: filePath,
              line: lineNumber,
              variable: varName,
              content: line.trim(),
              severity: 'HIGH'
            });
          }
        }
        
        // Check for unprefixed public variables
        if (!varName.startsWith('NEXT_PUBLIC_') && 
            !SENSITIVE_VARS.includes(varName) &&
            filePath.includes('apps/web/src') && 
            !filePath.includes('app/api/') &&
            !filePath.includes('middleware.ts')) {
          
          issues.push({
            type: 'UNPREFIXED_CLIENT_VAR',
            file: filePath,
            line: lineNumber,
            variable: varName,
            content: line.trim(),
            severity: 'MEDIUM'
          });
        }
      });
    }
    
    // Check for hardcoded secrets (basic patterns)
    const secretPatterns = [
      /sk_[a-zA-Z0-9]{32,}/g,  // API keys starting with sk_
      /pk_[a-zA-Z0-9]{32,}/g,  // Public keys starting with pk_
      /[a-zA-Z0-9]{32,}/g      // Long strings that might be secrets
    ];
    
    secretPatterns.forEach(pattern => {
      const matches = line.match(pattern);
      if (matches) {
        matches.forEach(match => {
          // Skip common false positives
          if (match.length > 20 && 
              !match.includes('example') && 
              !match.includes('placeholder') &&
              !match.includes('your-') &&
              !line.includes('//') &&
              !line.includes('*')) {
            
            issues.push({
              type: 'POTENTIAL_HARDCODED_SECRET',
              file: filePath,
              line: lineNumber,
              variable: match,
              content: line.trim(),
              severity: 'HIGH'
            });
          }
        });
      }
    });
  });
  
  return issues;
}

// Function to check Next.js build output for exposed variables
function checkBuildOutput() {
  const buildDir = 'apps/web/.next';
  const issues = [];
  
  if (!fs.existsSync(buildDir)) {
    printWarning('Next.js build output not found. Run "npm run build" first for complete audit.');
    return issues;
  }
  
  try {
    // Search for sensitive variables in build output
    SENSITIVE_VARS.forEach(varName => {
      try {
        const result = execSync(`grep -r "${varName}" ${buildDir} 2>/dev/null || true`, { encoding: 'utf8' });
        if (result.trim()) {
          issues.push({
            type: 'SENSITIVE_VAR_IN_BUILD',
            variable: varName,
            details: 'Found in build output',
            severity: 'CRITICAL'
          });
        }
      } catch (error) {
        // Ignore grep errors
      }
    });
  } catch (error) {
    printWarning('Could not scan build output: ' + error.message);
  }
  
  return issues;
}

// Function to generate security report
function generateReport(allIssues) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: allIssues.length,
      critical: allIssues.filter(i => i.severity === 'CRITICAL').length,
      high: allIssues.filter(i => i.severity === 'HIGH').length,
      medium: allIssues.filter(i => i.severity === 'MEDIUM').length,
      low: allIssues.filter(i => i.severity === 'LOW').length
    },
    issues: allIssues
  };
  
  // Write report to file
  const reportPath = 'security-audit-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  printStatus(`Security report written to ${reportPath}`);
  
  return report;
}

// Main audit function
function runSecurityAudit() {
  printHeader('Security Audit for Environment Variables');
  
  const allIssues = [];
  
  // Scan source files
  printStatus('Scanning source files...');
  SCAN_DIRS.forEach(dir => {
    if (fs.existsSync(dir)) {
      const files = findFiles(dir, SCAN_EXTENSIONS);
      printStatus(`Scanning ${files.length} files in ${dir}`);
      
      files.forEach(file => {
        const issues = scanFile(file);
        allIssues.push(...issues);
      });
    } else {
      printWarning(`Directory ${dir} not found, skipping`);
    }
  });
  
  // Check build output
  printStatus('Checking build output...');
  const buildIssues = checkBuildOutput();
  allIssues.push(...buildIssues);
  
  // Generate report
  const report = generateReport(allIssues);
  
  // Print summary
  printHeader('Security Audit Results');
  
  if (report.summary.total === 0) {
    printSuccess('✅ No security issues found!');
  } else {
    printError(`❌ Found ${report.summary.total} security issues:`);
    
    if (report.summary.critical > 0) {
      printError(`  🔴 Critical: ${report.summary.critical}`);
    }
    if (report.summary.high > 0) {
      printError(`  🟠 High: ${report.summary.high}`);
    }
    if (report.summary.medium > 0) {
      printWarning(`  🟡 Medium: ${report.summary.medium}`);
    }
    if (report.summary.low > 0) {
      printStatus(`  🔵 Low: ${report.summary.low}`);
    }
    
    // Print first few issues
    console.log('\nTop Issues:');
    allIssues.slice(0, 5).forEach(issue => {
      const severity = issue.severity === 'CRITICAL' ? '🔴' : 
                      issue.severity === 'HIGH' ? '🟠' : 
                      issue.severity === 'MEDIUM' ? '🟡' : '🔵';
      
      console.log(`${severity} ${issue.type}: ${issue.variable || 'N/A'}`);
      if (issue.file) {
        console.log(`   File: ${issue.file}:${issue.line || 'N/A'}`);
      }
      if (issue.content) {
        console.log(`   Code: ${issue.content}`);
      }
      console.log('');
    });
    
    if (allIssues.length > 5) {
      console.log(`... and ${allIssues.length - 5} more issues. See security-audit-report.json for details.`);
    }
  }
  
  // Exit with error code if critical or high severity issues found
  const criticalOrHigh = report.summary.critical + report.summary.high;
  if (criticalOrHigh > 0) {
    printError(`\n❌ Security audit failed with ${criticalOrHigh} critical/high severity issues.`);
    process.exit(1);
  } else {
    printSuccess('\n✅ Security audit passed!');
    process.exit(0);
  }
}

// Run the audit
runSecurityAudit();
