#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')
const { config } = require('dotenv')

// Load environment variables from the monorepo root
config({ path: path.resolve(__dirname, '../.env') })

// Get the port from environment or use default
const port = process.env.FRONTEND_PORT || '3030'
const useTurbo = process.argv.includes('--turbo')
const isProduction = process.argv.includes('--production')

// Determine the command
const command = isProduction ? 'start' : 'dev'
const args = [
  'next',
  command,
  ...(useTurbo && !isProduction ? ['--turbo'] : []),
  '--port',
  port
]

console.log(`🚀 Starting frontend on port ${port}${useTurbo && !isProduction ? ' with Turbo Pack' : ''}${isProduction ? ' (production)' : ''}`)

// Start the Next.js development server
const child = spawn('npx', args, {
  cwd: path.resolve(__dirname, '../apps/web'),
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_OPTIONS: '--max-old-space-size=4096',
    FRONTEND_PORT: port
  }
})

child.on('error', (error) => {
  console.error('Failed to start frontend:', error)
  process.exit(1)
})

child.on('exit', (code) => {
  process.exit(code)
})

// Handle graceful shutdown
process.on('SIGINT', () => {
  child.kill('SIGINT')
})

process.on('SIGTERM', () => {
  child.kill('SIGTERM')
})
