#!/bin/bash

# =============================================================================
# Taskmaster Environment Setup Script
# =============================================================================
# This script helps set up environment variables for different deployment platforms
# Usage: ./scripts/deploy-env-setup.sh [platform] [environment]
# Platforms: vercel, aws, gcp, azure
# Environments: staging, production
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PLATFORM=""
ENVIRONMENT=""
ENV_FILE=".env"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
}

# Function to check if required tools are installed
check_dependencies() {
    print_header "Checking Dependencies"
    
    # Check if .env file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        print_error ".env file not found. Please copy .env.example to .env and configure it."
        exit 1
    fi
    
    # Check if validation script works
    if ! npm run validate-env > /dev/null 2>&1; then
        print_error "Environment validation failed. Please fix your .env file."
        exit 1
    fi
    
    print_status "Environment validation passed"
}

# Function to load environment variables from .env file
load_env_vars() {
    print_status "Loading environment variables from $ENV_FILE"
    
    # Source the .env file
    set -a
    source "$ENV_FILE"
    set +a
    
    # Verify required variables are set
    required_vars=(
        "DATABASE_URL"
        "NEXTAUTH_SECRET"
        "NEXTAUTH_URL"
        "GOOGLE_CLIENT_ID"
        "GOOGLE_CLIENT_SECRET"
        "TWITTER_CLIENT_ID"
        "TWITTER_CLIENT_SECRET"
        "UPLOADTHING_SECRET"
        "UPLOADTHING_APP_ID"
        "UPLOADTHING_TOKEN"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required environment variables: ${missing_vars[*]}"
        exit 1
    fi
    
    print_status "All required environment variables are set"
}

# Function to deploy to Vercel
deploy_vercel() {
    print_header "Deploying to Vercel - $ENVIRONMENT"
    
    # Check if Vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        print_error "Vercel CLI not found. Install with: npm i -g vercel"
        exit 1
    fi
    
    # Set environment variables
    print_status "Setting environment variables in Vercel"
    
    # Required variables
    echo "$DATABASE_URL" | vercel env add DATABASE_URL "$ENVIRONMENT"
    echo "$DIRECT_URL" | vercel env add DIRECT_URL "$ENVIRONMENT"
    echo "$NEXTAUTH_SECRET" | vercel env add NEXTAUTH_SECRET "$ENVIRONMENT"
    echo "$NEXTAUTH_URL" | vercel env add NEXTAUTH_URL "$ENVIRONMENT"
    echo "$GOOGLE_CLIENT_ID" | vercel env add GOOGLE_CLIENT_ID "$ENVIRONMENT"
    echo "$GOOGLE_CLIENT_SECRET" | vercel env add GOOGLE_CLIENT_SECRET "$ENVIRONMENT"
    echo "$TWITTER_CLIENT_ID" | vercel env add TWITTER_CLIENT_ID "$ENVIRONMENT"
    echo "$TWITTER_CLIENT_SECRET" | vercel env add TWITTER_CLIENT_SECRET "$ENVIRONMENT"
    echo "$UPLOADTHING_SECRET" | vercel env add UPLOADTHING_SECRET "$ENVIRONMENT"
    echo "$UPLOADTHING_APP_ID" | vercel env add UPLOADTHING_APP_ID "$ENVIRONMENT"
    echo "$UPLOADTHING_TOKEN" | vercel env add UPLOADTHING_TOKEN "$ENVIRONMENT"
    
    # AI Provider variables (if set)
    [[ -n "$OPENAI_API_KEY" ]] && echo "$OPENAI_API_KEY" | vercel env add OPENAI_API_KEY "$ENVIRONMENT"
    [[ -n "$GEMINI_API_KEY" ]] && echo "$GEMINI_API_KEY" | vercel env add GEMINI_API_KEY "$ENVIRONMENT"
    [[ -n "$MISTRAL_API_KEY" ]] && echo "$MISTRAL_API_KEY" | vercel env add MISTRAL_API_KEY "$ENVIRONMENT"
    [[ -n "$HUGGINGFACE_API_KEY" ]] && echo "$HUGGINGFACE_API_KEY" | vercel env add HUGGINGFACE_API_KEY "$ENVIRONMENT"
    [[ -n "$GROQ_API_KEY" ]] && echo "$GROQ_API_KEY" | vercel env add GROQ_API_KEY "$ENVIRONMENT"
    [[ -n "$OPENROUTER_API_KEY" ]] && echo "$OPENROUTER_API_KEY" | vercel env add OPENROUTER_API_KEY "$ENVIRONMENT"
    
    # Optional variables
    [[ -n "$REDIS_URL" ]] && echo "$REDIS_URL" | vercel env add REDIS_URL "$ENVIRONMENT"
    
    print_status "Environment variables set successfully"
    
    # Deploy
    if [[ "$ENVIRONMENT" == "production" ]]; then
        vercel --prod
    else
        vercel
    fi
}

# Function to deploy to AWS
deploy_aws() {
    print_header "Deploying to AWS - $ENVIRONMENT"
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI not found. Please install and configure AWS CLI"
        exit 1
    fi
    
    # Create secrets in AWS Secrets Manager
    print_status "Creating secrets in AWS Secrets Manager"
    
    # Database secret
    aws secretsmanager create-secret \
        --name "taskmaster/$ENVIRONMENT/database" \
        --description "Database connection strings" \
        --secret-string "{\"DATABASE_URL\":\"$DATABASE_URL\",\"DIRECT_URL\":\"$DIRECT_URL\"}" \
        --region "${AWS_REGION:-us-east-1}" || true
    
    # OAuth secrets
    aws secretsmanager create-secret \
        --name "taskmaster/$ENVIRONMENT/oauth" \
        --description "OAuth provider credentials" \
        --secret-string "{\"GOOGLE_CLIENT_ID\":\"$GOOGLE_CLIENT_ID\",\"GOOGLE_CLIENT_SECRET\":\"$GOOGLE_CLIENT_SECRET\",\"TWITTER_CLIENT_ID\":\"$TWITTER_CLIENT_ID\",\"TWITTER_CLIENT_SECRET\":\"$TWITTER_CLIENT_SECRET\"}" \
        --region "${AWS_REGION:-us-east-1}" || true
    
    # Application secrets
    aws secretsmanager create-secret \
        --name "taskmaster/$ENVIRONMENT/app" \
        --description "Application secrets" \
        --secret-string "{\"NEXTAUTH_SECRET\":\"$NEXTAUTH_SECRET\",\"UPLOADTHING_SECRET\":\"$UPLOADTHING_SECRET\",\"UPLOADTHING_APP_ID\":\"$UPLOADTHING_APP_ID\",\"UPLOADTHING_TOKEN\":\"$UPLOADTHING_TOKEN\"}" \
        --region "${AWS_REGION:-us-east-1}" || true
    
    # AI provider secrets
    ai_secrets="{}"
    [[ -n "$OPENAI_API_KEY" ]] && ai_secrets=$(echo "$ai_secrets" | jq --arg key "$OPENAI_API_KEY" '. + {"OPENAI_API_KEY": $key}')
    [[ -n "$GEMINI_API_KEY" ]] && ai_secrets=$(echo "$ai_secrets" | jq --arg key "$GEMINI_API_KEY" '. + {"GEMINI_API_KEY": $key}')
    
    if [[ "$ai_secrets" != "{}" ]]; then
        aws secretsmanager create-secret \
            --name "taskmaster/$ENVIRONMENT/ai-providers" \
            --description "AI provider API keys" \
            --secret-string "$ai_secrets" \
            --region "${AWS_REGION:-us-east-1}" || true
    fi
    
    # Create parameters in Systems Manager Parameter Store
    print_status "Creating parameters in Systems Manager Parameter Store"
    
    aws ssm put-parameter \
        --name "/taskmaster/$ENVIRONMENT/NEXTAUTH_URL" \
        --value "$NEXTAUTH_URL" \
        --type "String" \
        --region "${AWS_REGION:-us-east-1}" \
        --overwrite || true
    
    print_status "AWS secrets and parameters created successfully"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [platform] [environment]"
    echo ""
    echo "Platforms:"
    echo "  vercel    - Deploy to Vercel"
    echo "  aws       - Deploy to AWS"
    echo ""
    echo "Environments:"
    echo "  staging     - Staging environment"
    echo "  production  - Production environment"
    echo ""
    echo "Examples:"
    echo "  $0 vercel production"
    echo "  $0 aws staging"
    echo ""
    echo "Environment variables will be loaded from .env file"
}

# Main function
main() {
    # Parse arguments
    PLATFORM="$1"
    ENVIRONMENT="$2"
    
    # Validate arguments
    if [[ -z "$PLATFORM" || -z "$ENVIRONMENT" ]]; then
        show_usage
        exit 1
    fi
    
    if [[ "$PLATFORM" != "vercel" && "$PLATFORM" != "aws" ]]; then
        print_error "Unsupported platform: $PLATFORM"
        show_usage
        exit 1
    fi
    
    if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
        print_error "Unsupported environment: $ENVIRONMENT"
        show_usage
        exit 1
    fi
    
    print_header "Taskmaster Environment Setup"
    print_status "Platform: $PLATFORM"
    print_status "Environment: $ENVIRONMENT"
    
    # Run setup steps
    check_dependencies
    load_env_vars
    
    # Deploy based on platform
    case "$PLATFORM" in
        "vercel")
            deploy_vercel
            ;;
        "aws")
            deploy_aws
            ;;
        *)
            print_error "Unsupported platform: $PLATFORM"
            exit 1
            ;;
    esac
    
    print_status "Deployment setup completed successfully!"
}

# Run main function with all arguments
main "$@"
