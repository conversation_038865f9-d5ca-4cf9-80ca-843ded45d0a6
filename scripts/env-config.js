#!/usr/bin/env node

const path = require('path')
const { config } = require('dotenv')

// Load environment variables from the monorepo root
config({ path: path.resolve(__dirname, '../.env') })

/**
 * Get environment-aware configuration
 * Automatically switches between development and production URLs based on NODE_ENV from .env file
 */
function getEnvConfig() {
  // Always use NODE_ENV from .env file, not from process.env
  const nodeEnv = process.env.NODE_ENV || 'development'
  const isDevelopment = nodeEnv === 'development'
  const isProduction = nodeEnv === 'production'
  
  // Base configuration
  const baseConfig = {
    NODE_ENV: nodeEnv,

    // Port configuration - can use same port for both frontend and backend
    PORT: process.env.PORT || '3030',
    FRONTEND_PORT: process.env.FRONTEND_PORT || '3030',

    // Option to use single port for both frontend and backend
    USE_SINGLE_PORT: process.env.USE_SINGLE_PORT === 'true',
    
    // Database
    DATABASE_URL: process.env.DATABASE_URL,
    REDIS_URL: process.env.REDIS_URL,
    
    // Authentication
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    JWT_SECRET: process.env.JWT_SECRET,
    
    // OAuth
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    TWITTER_API_KEY: process.env.TWITTER_API_KEY,
    TWITTER_API_SECRET: process.env.TWITTER_API_SECRET,
    TWITTER_BEARER_TOKEN: process.env.TWITTER_BEARER_TOKEN,
    
    // External Services
    UPLOADTHING_SECRET: process.env.UPLOADTHING_SECRET,
    UPLOADTHING_APP_ID: process.env.UPLOADTHING_APP_ID,
    UPLOADTHING_TOKEN: process.env.UPLOADTHING_TOKEN,
    
    // OpenAI
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    OPENAI_BASE_URL: process.env.OPENAI_BASE_URL,
    OPENAI_DEFAULT_MODEL: process.env.OPENAI_DEFAULT_MODEL,
    OPENAI_MAX_TOKENS: process.env.OPENAI_MAX_TOKENS,
    OPENAI_TEMPERATURE: process.env.OPENAI_TEMPERATURE,
    
    // Scheduling
    SCHEDULER_ENABLED: process.env.SCHEDULER_ENABLED,
    SCHEDULER_INTERVAL: process.env.SCHEDULER_INTERVAL,
  }
  
  // Environment-specific URL overrides
  if (isDevelopment) {
    // In development, override production URLs with localhost
    const frontendPort = baseConfig.FRONTEND_PORT
    const backendPort = baseConfig.USE_SINGLE_PORT ? frontendPort : baseConfig.PORT

    return {
      ...baseConfig,
      FRONTEND_URL: `http://localhost:${frontendPort}`,
      BACKEND_URL: `http://localhost:${backendPort}`,
      NEXTAUTH_URL: `http://localhost:${frontendPort}`,

      // OAuth Callbacks (Development - override production URLs)
      GOOGLE_CALLBACK_URL: `http://localhost:${frontendPort}/api/auth/google/callback`,
      TWITTER_CALLBACK_URL: `http://localhost:${frontendPort}/api/auth/twitter/callback`,
      GOOGLE_REDIRECT_URI: `http://localhost:${frontendPort}/api/auth/google/callback`,
    }
  } else {
    // In production, use the URLs from .env as-is
    const frontendUrl = process.env.FRONTEND_URL || 'https://tasker.violetmethods.com'
    const backendUrl = baseConfig.USE_SINGLE_PORT
      ? frontendUrl  // Use same URL if single port
      : (process.env.BACKEND_URL || 'http://localhost:3030')

    return {
      ...baseConfig,
      FRONTEND_URL: frontendUrl,
      BACKEND_URL: backendUrl,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL || frontendUrl,

      // OAuth Callbacks (Production - use .env values)
      GOOGLE_CALLBACK_URL: process.env.GOOGLE_CALLBACK_URL || `${frontendUrl}/api/auth/google/callback`,
      TWITTER_CALLBACK_URL: process.env.TWITTER_CALLBACK_URL || `${frontendUrl}/api/auth/twitter/callback`,
      GOOGLE_REDIRECT_URI: process.env.GOOGLE_REDIRECT_URI || `${frontendUrl}/api/auth/google/callback`,
    }
  }
}

/**
 * Print current environment configuration
 */
function printConfig() {
  const config = getEnvConfig()
  
  console.log('\n🔧 Environment Configuration:')
  console.log('================================')
  console.log(`Environment: ${config.NODE_ENV}`)
  console.log(`Frontend URL: ${config.FRONTEND_URL}`)
  console.log(`Backend URL: ${config.BACKEND_URL}`)
  console.log(`NextAuth URL: ${config.NEXTAUTH_URL}`)
  console.log(`Google Callback: ${config.GOOGLE_CALLBACK_URL}`)
  console.log(`Twitter Callback: ${config.TWITTER_CALLBACK_URL}`)
  
  if (config.NODE_ENV === 'development') {
    console.log(`Frontend Port: ${config.FRONTEND_PORT}`)
    console.log(`Backend Port: ${config.PORT}`)
  }
  
  console.log('================================\n')
}

// If called directly, print the configuration
if (require.main === module) {
  printConfig()
}

module.exports = {
  getEnvConfig,
  printConfig
}
