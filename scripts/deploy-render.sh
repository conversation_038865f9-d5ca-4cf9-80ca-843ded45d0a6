#!/bin/bash

# Render Deployment Script for Tasker Application
# This script prepares the application for Render deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Preparing Tasker for Render Deployment${NC}"
echo "=============================================="

# Set production environment
echo -e "${YELLOW}🔧 Setting production environment...${NC}"
npm run env:prod

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm install

# Build the application
echo -e "${YELLOW}🏗️  Building application with Turbo Pack...${NC}"
npm run build

# Run type checking
echo -e "${YELLOW}🔍 Running type checks...${NC}"
npm run type-check

echo -e "${GREEN}✅ Build completed successfully!${NC}"
echo ""
echo -e "${GREEN}📋 Render Deployment Configuration:${NC}"
echo ""
echo -e "${YELLOW}Frontend Service (Static Site):${NC}"
echo "• Build Command: npm run build"
echo "• Publish Directory: apps/web/.next"
echo "• Environment: Node.js 18+"
echo ""
echo -e "${YELLOW}Backend Service (Web Service):${NC}"
echo "• Build Command: npm install && npm run build"
echo "• Start Command: npm start"
echo "• Environment: Node.js 18+"
echo "• Port: 3030"
echo ""
echo -e "${YELLOW}Environment Variables to set in Render:${NC}"
echo "• NODE_ENV=production"
echo "• DATABASE_URL=your-production-database-url"
echo "• NEXTAUTH_SECRET=your-nextauth-secret"
echo "• GOOGLE_CLIENT_ID=your-google-client-id"
echo "• GOOGLE_CLIENT_SECRET=your-google-client-secret"
echo "• TWITTER_API_KEY=your-twitter-api-key"
echo "• TWITTER_API_SECRET=your-twitter-api-secret"
echo ""
echo -e "${GREEN}🎉 Ready for Render deployment!${NC}"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Push your code to GitHub"
echo "2. Connect your GitHub repo to Render"
echo "3. Create a new Web Service on Render"
echo "4. Set the environment variables listed above"
echo "5. Deploy!"
