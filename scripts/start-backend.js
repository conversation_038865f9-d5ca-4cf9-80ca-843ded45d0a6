#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')
const { getEnvConfig } = require('./env-config')

// Get environment-aware configuration
const envConfig = getEnvConfig()
const port = envConfig.PORT

console.log(`🚀 Starting backend on port ${port}`)

// Start the backend server
const child = spawn('npx', ['tsx', 'src/server-simple.ts'], {
  cwd: path.resolve(__dirname, '../packages/server'),
  stdio: 'inherit',
  env: {
    ...process.env,
    ...envConfig,
  }
})

child.on('error', (error) => {
  console.error('Failed to start backend:', error)
  process.exit(1)
})

child.on('exit', (code) => {
  process.exit(code)
})

// Handle graceful shutdown
process.on('SIGINT', () => {
  child.kill('SIGINT')
})

process.on('SIGTERM', () => {
  child.kill('SIGTERM')
})
