#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')
const { config } = require('dotenv')

// Load environment variables from the monorepo root
config({ path: path.resolve(__dirname, '../.env') })

// Get the port from environment or use default
const port = process.env.PORT || '3000'

console.log(`🚀 Starting backend on port ${port}`)

// Start the backend server
const child = spawn('npx', ['tsx', 'src/server-simple.ts'], {
  cwd: path.resolve(__dirname, '../packages/server'),
  stdio: 'inherit',
  env: {
    ...process.env,
    PORT: port
  }
})

child.on('error', (error) => {
  console.error('Failed to start backend:', error)
  process.exit(1)
})

child.on('exit', (code) => {
  process.exit(code)
})

// Handle graceful shutdown
process.on('SIGINT', () => {
  child.kill('SIGINT')
})

process.on('SIGTERM', () => {
  child.kill('SIGTERM')
})
