require('dotenv').config({ path: '.env.development' });
const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Setting up database...');

try {
  // Generate Prisma client
  console.log('📦 Generating Prisma client...');
  execSync('npx prisma generate --schema=prisma/schema.prisma', { stdio: 'inherit', cwd: process.cwd() });

  // Run database migrations
  console.log('🗄️  Running database migrations...');
  execSync('npx prisma migrate deploy --schema=prisma/schema.prisma', { stdio: 'inherit', cwd: process.cwd() });

  console.log('✅ Database setup completed successfully!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Run `npm run dev` to start the development servers');
  console.log('2. Visit http://localhost:3030/api/health to check database connection');
  console.log('3. Visit http://localhost:3030/api/stats to see database statistics');

} catch (error) {
  console.error('❌ Database setup failed:', error.message);
  process.exit(1);
}