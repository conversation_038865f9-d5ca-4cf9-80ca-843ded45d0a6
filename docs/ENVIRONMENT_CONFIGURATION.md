# Environment Configuration Guide

This project uses a unified environment configuration system that automatically switches between development and production settings based on the `NODE_ENV` variable.

## Quick Start

### Development (Default)
```bash
npm run dev          # Start with Webpack (default)
npm run dev:turbo    # Start with Turbo Pack (faster)
```

### Check Current Configuration
```bash
npm run env:check    # Show development config
npm run env:prod     # Show production config
```

## Port Configuration

All ports are easily configurable via environment variables in the root `.env` file:

```env
# Port Configuration (easily changeable)
PORT=3000                    # Backend server port
FRONTEND_PORT=3030           # Frontend Next.js port
```

### Current Setup
- **Frontend (Next.js)**: http://localhost:3030
- **Backend (Express)**: http://localhost:3000

### To Change Ports
Simply update the `.env` file:
```env
PORT=8000                    # Backend will run on port 8000
FRONTEND_PORT=8080           # Frontend will run on port 8080
```

## Environment-Aware URLs

The system automatically configures URLs based on the environment:

### Development URLs
- Frontend: `http://localhost:3030`
- Backend: `http://localhost:3000`
- OAuth Callbacks: `http://localhost:3030/api/auth/[provider]/callback`

### Production URLs
- Frontend: `https://tasker.violetmethods.com`
- Backend: `https://api.tasker.violetmethods.com`
- OAuth Callbacks: `https://tasker.violetmethods.com/api/auth/[provider]/callback`

## OAuth Configuration

### Google Cloud Console Setup

You need to add these redirect URIs to your Google Cloud Console:

**Development:**
```
http://localhost:3030/api/auth/google/callback
```

**Production:**
```
https://tasker.violetmethods.com/api/auth/google/callback
```

### Twitter Developer Portal Setup

Add these callback URLs to your Twitter app:

**Development:**
```
http://localhost:3030/api/auth/twitter/callback
```

**Production:**
```
https://tasker.violetmethods.com/api/auth/twitter/callback
```

## Environment Variables Reference

### Core Configuration
```env
NODE_ENV=development         # Environment mode
PORT=3000                   # Backend port
FRONTEND_PORT=3030          # Frontend port
```

### URLs (Auto-configured)
```env
# Development
FRONTEND_URL=http://localhost:3030
BACKEND_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3030

# Production
PRODUCTION_FRONTEND_URL=https://tasker.violetmethods.com
PRODUCTION_BACKEND_URL=https://api.tasker.violetmethods.com
NEXTAUTH_URL_PROD=https://tasker.violetmethods.com
```

### OAuth Credentials
```env
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_BEARER_TOKEN=your-twitter-bearer-token
```

### Database & Services
```env
DATABASE_URL=your-database-url
REDIS_URL=your-redis-url
UPLOADTHING_SECRET=your-uploadthing-secret
UPLOADTHING_APP_ID=your-uploadthing-app-id
OPENAI_API_KEY=your-openai-api-key
```

## Deployment

### Production Deployment

1. Set environment variables on your hosting platform:
```env
NODE_ENV=production
DATABASE_URL=your-production-database-url
REDIS_URL=your-production-redis-url
# ... other production variables
```

2. The system will automatically use production URLs:
- Frontend: `https://tasker.violetmethods.com`
- Backend: `https://api.tasker.violetmethods.com`

### Vercel Deployment (Frontend)

Add these environment variables in your Vercel dashboard:
```env
NODE_ENV=production
NEXTAUTH_URL=https://tasker.violetmethods.com
NEXTAUTH_SECRET=your-nextauth-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
# ... other variables
```

### Railway/Heroku Deployment (Backend)

Add these environment variables:
```env
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://tasker.violetmethods.com
DATABASE_URL=your-database-url
# ... other variables
```

## Troubleshooting

### Port Already in Use
If you get port conflicts, change the ports in `.env`:
```env
PORT=3001                    # Change backend port
FRONTEND_PORT=3031           # Change frontend port
```

### OAuth Redirect Mismatch
1. Check your current configuration: `npm run env:check`
2. Update your OAuth provider settings to match the displayed URLs
3. Restart the development server

### Environment Not Loading
The system loads environment variables from the root `.env` file. Make sure:
1. The `.env` file is in the project root
2. Variables are properly formatted (no spaces around `=`)
3. Restart the development server after changes

## Advanced Configuration

### Custom Environment
You can override any URL by setting it explicitly:
```env
FRONTEND_URL=http://custom-domain.local:3030
BACKEND_URL=http://api.custom-domain.local:3000
```

### Multiple Environments
Create environment-specific files:
- `.env.development`
- `.env.production`
- `.env.staging`

The system will automatically load the appropriate file based on `NODE_ENV`.
