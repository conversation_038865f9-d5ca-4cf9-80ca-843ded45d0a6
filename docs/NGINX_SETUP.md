# Nginx Configuration for Tasker Application

This document explains the nginx setup for your Tasker application with the domains:
- **Frontend**: `https://tasker.violetmethods.com`
- **Backend API**: `https://api.tasker.violetmethods.com`

## Quick Deployment

### Automated Setup
```bash
sudo ./scripts/deploy-nginx.sh
```

### Manual Setup
1. Copy configuration files to nginx:
```bash
sudo cp nginx/tasker.violetmethods.com.conf /etc/nginx/sites-available/
sudo cp nginx/api.tasker.violetmethods.com.conf /etc/nginx/sites-available/
```

2. Enable the sites:
```bash
sudo ln -s /etc/nginx/sites-available/tasker.violetmethods.com /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/api.tasker.violetmethods.com /etc/nginx/sites-enabled/
```

3. Test and reload:
```bash
sudo nginx -t
sudo systemctl reload nginx
```

## Configuration Overview

### Frontend Configuration (`tasker.violetmethods.com.conf`)

**Key Features:**
- **HTTP to HTTPS redirect**: All traffic redirected to secure connection
- **www redirect**: `www.tasker.violetmethods.com` → `tasker.violetmethods.com`
- **Static asset caching**: 1-year cache for JS, CSS, images
- **Next.js support**: Proper routing for Next.js application
- **API proxying**: `/api/*` routes proxied to backend
- **Security headers**: CSP, HSTS, XSS protection
- **Rate limiting**: 20 requests/second for pages, 50 for static assets

**Upstream Configuration:**
```nginx
upstream tasker_frontend {
    server 127.0.0.1:3030;  # Your Next.js app
    keepalive 32;
}
```

### Backend Configuration (`api.tasker.violetmethods.com.conf`)

**Key Features:**
- **API-focused**: Optimized for REST API traffic
- **CORS headers**: Configured for frontend domain
- **Rate limiting**: 10 requests/second for API, 5 for auth
- **WebSocket support**: For real-time features
- **Health checks**: Dedicated `/health` endpoint
- **Auth protection**: Stricter limits on `/api/auth/*`

**Upstream Configuration:**
```nginx
upstream tasker_backend {
    server 127.0.0.1:3000;  # Your Express API
    keepalive 32;
}
```

## SSL Certificate Setup

### Using Certbot (Let's Encrypt)

1. **Install Certbot:**
```bash
sudo apt update
sudo apt install certbot python3-certbot-nginx
```

2. **Generate certificates:**
```bash
# Frontend certificate
sudo certbot --nginx -d tasker.violetmethods.com -d www.tasker.violetmethods.com

# Backend certificate  
sudo certbot --nginx -d api.tasker.violetmethods.com
```

3. **Auto-renewal:**
```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

### Manual Certificate Setup

If you have existing certificates, update the paths in the nginx configs:
```nginx
ssl_certificate /path/to/your/fullchain.pem;
ssl_certificate_key /path/to/your/privkey.pem;
```

## Security Features

### Headers Applied
- **HSTS**: Force HTTPS for 1 year
- **CSP**: Content Security Policy
- **X-Frame-Options**: Prevent clickjacking
- **X-Content-Type-Options**: Prevent MIME sniffing
- **Referrer-Policy**: Control referrer information

### Rate Limiting
- **Frontend**: 20 req/s (burst 50)
- **API**: 10 req/s (burst 20)
- **Auth**: 5 req/s (burst 10)
- **Static**: 50 req/s (burst 100)

### CORS Configuration
```nginx
Access-Control-Allow-Origin: https://tasker.violetmethods.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH
Access-Control-Allow-Credentials: true
```

## Performance Optimizations

### Caching Strategy
- **Static assets**: 1 year cache
- **Next.js builds**: 1 year cache (immutable)
- **API responses**: No cache (dynamic content)

### Compression
- **Gzip enabled** for text-based content
- **Compression level**: 6 (balanced)
- **Min size**: 1024 bytes

### Connection Management
- **Keepalive**: 32 connections upstream
- **HTTP/2**: Enabled for better performance
- **Buffer optimization**: Tuned for API responses

## Monitoring & Logging

### Log Files
```bash
# Frontend logs
/var/log/nginx/tasker.violetmethods.com.access.log
/var/log/nginx/tasker.violetmethods.com.error.log

# Backend logs
/var/log/nginx/api.tasker.violetmethods.com.access.log
/var/log/nginx/api.tasker.violetmethods.com.error.log
```

### Log Rotation
Nginx logs are automatically rotated by logrotate. Check:
```bash
sudo cat /etc/logrotate.d/nginx
```

### Monitoring Commands
```bash
# Check nginx status
sudo systemctl status nginx

# Test configuration
sudo nginx -t

# Reload configuration
sudo systemctl reload nginx

# View real-time logs
sudo tail -f /var/log/nginx/tasker.violetmethods.com.access.log
```

## Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   - Check if your applications are running on correct ports
   - Verify upstream server configuration
   ```bash
   # Check if apps are running
   netstat -tlnp | grep -E ":(3000|3030)"
   ```

2. **SSL Certificate Errors**
   - Verify certificate paths exist
   - Check certificate validity
   ```bash
   sudo certbot certificates
   ```

3. **CORS Issues**
   - Verify frontend domain in CORS headers
   - Check browser developer tools for specific errors

4. **Rate Limiting**
   - Check nginx error logs for rate limit messages
   - Adjust limits in configuration if needed

### Testing Configuration

```bash
# Test frontend
curl -I https://tasker.violetmethods.com

# Test backend API
curl -I https://api.tasker.violetmethods.com/health

# Test CORS
curl -H "Origin: https://tasker.violetmethods.com" \
     -H "Access-Control-Request-Method: POST" \
     -X OPTIONS https://api.tasker.violetmethods.com/api/test
```

## Production Checklist

- [ ] Applications running on correct ports (3030, 3000)
- [ ] SSL certificates installed and valid
- [ ] DNS records pointing to server
- [ ] Nginx configurations deployed
- [ ] Sites enabled in nginx
- [ ] Nginx configuration tested (`nginx -t`)
- [ ] Nginx reloaded/restarted
- [ ] Firewall configured (ports 80, 443 open)
- [ ] Log rotation configured
- [ ] Monitoring set up
- [ ] Backup strategy in place

## Environment Variables

Make sure your applications use the correct URLs:

```env
# Production environment
NODE_ENV=production
FRONTEND_URL=https://tasker.violetmethods.com
BACKEND_URL=https://api.tasker.violetmethods.com
NEXTAUTH_URL=https://tasker.violetmethods.com
```

Your nginx configuration is now ready for production deployment!
