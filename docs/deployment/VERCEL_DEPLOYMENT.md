# Vercel Deployment Guide

This guide covers deploying the Taskmaster application to Vercel with proper environment variable configuration.

## 🚀 Prerequisites

- Vercel account
- GitHub repository connected to Vercel
- All required credentials obtained (see [Environment Setup](../ENVIRONMENT_SETUP.md))

## 📋 Environment Variables Configuration

### Required Environment Variables

#### Database Configuration
```
DATABASE_URL=************************************************************
DIRECT_URL=************************************************************
```

#### Authentication & Session Management
```
NEXTAUTH_SECRET=your-32-char-secret
NEXTAUTH_URL=https://your-domain.vercel.app
```

#### OAuth Providers
```
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret
```

#### Media Upload Service
```
UPLOADTHING_SECRET=your-uploadthing-secret
UPLOADTHING_APP_ID=your-uploadthing-app-id
UPLOADTHING_TOKEN=your-uploadthing-token
```

#### AI Providers (at least one required)
```
OPENAI_API_KEY=your-openai-api-key
GEMINI_API_KEY=your-gemini-api-key
```

### Optional Environment Variables
```
REDIS_URL=your-redis-url
MISTRAL_API_KEY=your-mistral-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
GROQ_API_KEY=your-groq-api-key
OPENROUTER_API_KEY=your-openrouter-api-key
```

## 🛠️ Deployment Steps

### 1. Project Setup

1. **Connect Repository**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository

2. **Configure Build Settings**
   ```
   Framework Preset: Next.js
   Root Directory: apps/web
   Build Command: npm run build
   Output Directory: .next
   Install Command: npm install
   ```

### 2. Environment Variables Setup

#### Via Vercel Dashboard

1. Go to Project Settings → Environment Variables
2. Add each variable with appropriate environment scope:
   - **Production**: Live environment
   - **Preview**: Staging/preview deployments
   - **Development**: Local development (optional)

#### Via Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Set environment variables
vercel env add NEXTAUTH_SECRET production
vercel env add DATABASE_URL production
vercel env add GOOGLE_CLIENT_ID production
vercel env add GOOGLE_CLIENT_SECRET production
# ... continue for all variables
```

#### Bulk Environment Variable Setup

Create a script to set all variables:

```bash
#!/bin/bash
# deploy/vercel-env-setup.sh

# Production environment variables
vercel env add NEXTAUTH_SECRET production
vercel env add NEXTAUTH_URL production
vercel env add DATABASE_URL production
vercel env add DIRECT_URL production
vercel env add GOOGLE_CLIENT_ID production
vercel env add GOOGLE_CLIENT_SECRET production
vercel env add TWITTER_CLIENT_ID production
vercel env add TWITTER_CLIENT_SECRET production
vercel env add UPLOADTHING_SECRET production
vercel env add UPLOADTHING_APP_ID production
vercel env add UPLOADTHING_TOKEN production
vercel env add OPENAI_API_KEY production
vercel env add GEMINI_API_KEY production

# Preview environment variables (staging)
vercel env add NEXTAUTH_SECRET preview
vercel env add NEXTAUTH_URL preview
vercel env add DATABASE_URL preview
# ... continue for staging values
```

### 3. Domain Configuration

1. **Custom Domain Setup**
   ```bash
   # Add custom domain
   vercel domains add yourdomain.com
   
   # Configure DNS
   # Add CNAME record: www -> cname.vercel-dns.com
   # Add A record: @ -> 76.76.19.61
   ```

2. **Update OAuth Callbacks**
   - Google OAuth: Add `https://yourdomain.com/api/auth/google/callback`
   - Twitter OAuth: Add `https://yourdomain.com/api/auth/twitter/callback`

### 4. Deployment

```bash
# Deploy to production
vercel --prod

# Deploy to preview
vercel
```

## 🔧 Configuration Files

### vercel.json
```json
{
  "version": 2,
  "builds": [
    {
      "src": "apps/web/package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "apps/web/api/$1"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  },
  "functions": {
    "apps/web/src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

### .vercelignore
```
node_modules
.env*
.git
*.log
.DS_Store
```

## 🔍 Verification

### 1. Health Check
```bash
# Check application health
curl https://yourdomain.com/api/health

# Expected response
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "production",
  "database": {
    "connected": true,
    "provider": "postgresql"
  },
  "features": {
    "database": true,
    "googleOAuth": true,
    "twitterOAuth": true,
    "uploadThing": true,
    "openai": true,
    "gemini": true
  },
  "aiProviders": {
    "count": 2,
    "available": ["openai", "gemini"]
  }
}
```

### 2. Environment Validation
```bash
# Check environment variables are properly set
vercel env ls
```

### 3. Functionality Testing
- [ ] User authentication works
- [ ] Database connections successful
- [ ] File uploads functional
- [ ] AI generation working
- [ ] OAuth flows complete

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs
   vercel logs
   
   # Common fixes
   - Verify all environment variables are set
   - Check package.json scripts
   - Ensure dependencies are installed
   ```

2. **Environment Variable Issues**
   ```bash
   # List all environment variables
   vercel env ls
   
   # Remove incorrect variable
   vercel env rm VARIABLE_NAME production
   
   # Add correct variable
   vercel env add VARIABLE_NAME production
   ```

3. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check database server accessibility
   - Ensure SSL is enabled

4. **OAuth Issues**
   - Verify callback URLs match exactly
   - Check client IDs and secrets
   - Ensure domains are authorized

## 📊 Monitoring

### Vercel Analytics
- Enable Vercel Analytics in project settings
- Monitor performance and usage
- Set up alerts for errors

### Custom Monitoring
```javascript
// Add to your application
if (process.env.NODE_ENV === 'production') {
  // Log important events
  console.log('Application started successfully');
}
```

## 🔄 Updates and Maintenance

### Automatic Deployments
- Vercel automatically deploys on git push to main branch
- Preview deployments for pull requests
- Rollback capability through dashboard

### Environment Variable Updates
```bash
# Update production variable
vercel env rm VARIABLE_NAME production
vercel env add VARIABLE_NAME production

# Redeploy to apply changes
vercel --prod
```

## 📚 Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Environment Variables Guide](https://vercel.com/docs/concepts/projects/environment-variables)
