# AWS Deployment Guide

This guide covers deploying the Taskmaster application to AWS using various services with proper secret management.

## 🏗️ Architecture Overview

### Recommended AWS Services
- **AWS Amplify** or **EC2** - Application hosting
- **AWS Secrets Manager** - Secret management
- **AWS Systems Manager Parameter Store** - Configuration management
- **Amazon RDS** - Database (if not using external)
- **Amazon ElastiCache** - Redis cache
- **AWS CloudFront** - CDN
- **AWS Certificate Manager** - SSL certificates

## 🔐 Secret Management Setup

### 1. AWS Secrets Manager

#### Create Secrets
```bash
# Install AWS CLI
aws configure

# Create database secret
aws secretsmanager create-secret \
  --name "taskmaster/production/database" \
  --description "Database connection string" \
  --secret-string '{"DATABASE_URL":"postgresql://..."}'

# Create OAuth secrets
aws secretsmanager create-secret \
  --name "taskmaster/production/oauth" \
  --secret-string '{
    "GOOGLE_CLIENT_ID":"your-google-client-id",
    "GOOGLE_CLIENT_SECRET":"your-google-client-secret",
    "TWITTER_CLIENT_ID":"your-twitter-client-id",
    "TWITTER_CLIENT_SECRET":"your-twitter-client-secret"
  }'

# Create AI provider secrets
aws secretsmanager create-secret \
  --name "taskmaster/production/ai-providers" \
  --secret-string '{
    "OPENAI_API_KEY":"your-openai-api-key",
    "GEMINI_API_KEY":"your-gemini-api-key"
  }'

# Create application secrets
aws secretsmanager create-secret \
  --name "taskmaster/production/app" \
  --secret-string '{
    "NEXTAUTH_SECRET":"your-32-char-secret",
    "UPLOADTHING_SECRET":"your-uploadthing-secret",
    "UPLOADTHING_APP_ID":"your-uploadthing-app-id",
    "UPLOADTHING_TOKEN":"your-uploadthing-token"
  }'
```

### 2. Systems Manager Parameter Store

#### Create Parameters
```bash
# Application configuration
aws ssm put-parameter \
  --name "/taskmaster/production/NEXTAUTH_URL" \
  --value "https://yourdomain.com" \
  --type "String"

aws ssm put-parameter \
  --name "/taskmaster/production/FRONTEND_URL" \
  --value "https://yourdomain.com" \
  --type "String"

aws ssm put-parameter \
  --name "/taskmaster/production/NODE_ENV" \
  --value "production" \
  --type "String"
```

## 🚀 Deployment Options

### Option 1: AWS Amplify (Recommended for Next.js)

#### 1. Setup Amplify App
```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Initialize Amplify
amplify init

# Add hosting
amplify add hosting
```

#### 2. Configure Build Settings
```yaml
# amplify.yml
version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    appRoot: apps/web
```

#### 3. Environment Variables
```bash
# Set environment variables in Amplify Console
# Or use CLI
amplify env add production
```

### Option 2: EC2 Deployment

#### 1. EC2 Instance Setup
```bash
# Launch EC2 instance (Ubuntu 22.04 LTS)
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install nginx for reverse proxy
sudo apt-get install nginx
```

#### 2. Application Deployment
```bash
# Clone repository
git clone https://github.com/your-repo/taskmaster.git
cd taskmaster

# Install dependencies
npm run install:all

# Build application
npm run build

# Create PM2 ecosystem file
```

#### 3. PM2 Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'taskmaster-web',
      script: 'npm',
      args: 'start',
      cwd: './apps/web',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    {
      name: 'taskmaster-server',
      script: 'npm',
      args: 'start',
      cwd: './packages/server',
      env: {
        NODE_ENV: 'production',
        PORT: 3030
      }
    }
  ]
}
```

## 🔧 Environment Variable Integration

### 1. Create IAM Role
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue",
        "ssm:GetParameter",
        "ssm:GetParameters"
      ],
      "Resource": [
        "arn:aws:secretsmanager:region:account:secret:taskmaster/*",
        "arn:aws:ssm:region:account:parameter/taskmaster/*"
      ]
    }
  ]
}
```

### 2. Environment Loading Script
```javascript
// lib/aws-env.js
const AWS = require('aws-sdk');

const secretsManager = new AWS.SecretsManager();
const ssm = new AWS.SSM();

async function loadEnvironment() {
  try {
    // Load secrets from Secrets Manager
    const secrets = await Promise.all([
      secretsManager.getSecretValue({ SecretId: 'taskmaster/production/database' }).promise(),
      secretsManager.getSecretValue({ SecretId: 'taskmaster/production/oauth' }).promise(),
      secretsManager.getSecretValue({ SecretId: 'taskmaster/production/ai-providers' }).promise(),
      secretsManager.getSecretValue({ SecretId: 'taskmaster/production/app' }).promise(),
    ]);

    // Parse and set environment variables
    secrets.forEach(secret => {
      const values = JSON.parse(secret.SecretString);
      Object.assign(process.env, values);
    });

    // Load parameters from Parameter Store
    const parameters = await ssm.getParameters({
      Names: [
        '/taskmaster/production/NEXTAUTH_URL',
        '/taskmaster/production/FRONTEND_URL',
        '/taskmaster/production/NODE_ENV'
      ]
    }).promise();

    parameters.Parameters.forEach(param => {
      const key = param.Name.split('/').pop();
      process.env[key] = param.Value;
    });

    console.log('✅ Environment loaded from AWS');
  } catch (error) {
    console.error('❌ Failed to load environment from AWS:', error);
    process.exit(1);
  }
}

module.exports = { loadEnvironment };
```

### 3. Application Integration
```javascript
// At the top of your main application file
if (process.env.NODE_ENV === 'production' && process.env.AWS_REGION) {
  const { loadEnvironment } = require('./lib/aws-env');
  await loadEnvironment();
}
```

## 🔍 Monitoring and Logging

### CloudWatch Integration
```javascript
// lib/cloudwatch-logger.js
const AWS = require('aws-sdk');
const cloudWatchLogs = new AWS.CloudWatchLogs();

class CloudWatchLogger {
  constructor(logGroupName, logStreamName) {
    this.logGroupName = logGroupName;
    this.logStreamName = logStreamName;
  }

  async log(level, message, metadata = {}) {
    const logEvent = {
      timestamp: Date.now(),
      message: JSON.stringify({
        level,
        message,
        metadata,
        timestamp: new Date().toISOString()
      })
    };

    try {
      await cloudWatchLogs.putLogEvents({
        logGroupName: this.logGroupName,
        logStreamName: this.logStreamName,
        logEvents: [logEvent]
      }).promise();
    } catch (error) {
      console.error('Failed to send log to CloudWatch:', error);
    }
  }
}

module.exports = CloudWatchLogger;
```

## 🔄 CI/CD Pipeline

### GitHub Actions with AWS
```yaml
# .github/workflows/deploy-aws.yml
name: Deploy to AWS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Deploy to Amplify
        run: |
          amplify publish --yes
```

## 🛡️ Security Best Practices

### 1. Network Security
- Use VPC with private subnets
- Configure security groups with minimal access
- Enable AWS WAF for web application firewall

### 2. Access Control
- Use IAM roles instead of access keys
- Implement least privilege principle
- Enable AWS CloudTrail for audit logging

### 3. Encryption
- Enable encryption at rest for RDS
- Use SSL/TLS for all communications
- Encrypt secrets in Secrets Manager

## 📊 Cost Optimization

### 1. Resource Sizing
- Use appropriate EC2 instance types
- Implement auto-scaling
- Use spot instances for non-critical workloads

### 2. Monitoring
- Set up billing alerts
- Use AWS Cost Explorer
- Implement resource tagging

## 🚨 Troubleshooting

### Common Issues
1. **IAM Permission Errors**
   - Verify IAM roles have correct permissions
   - Check resource ARNs in policies

2. **Secret Access Issues**
   - Ensure secrets exist in correct region
   - Verify secret names and paths

3. **Network Connectivity**
   - Check security group rules
   - Verify VPC configuration

## 📚 Additional Resources

- [AWS Amplify Documentation](https://docs.amplify.aws/)
- [AWS Secrets Manager](https://docs.aws.amazon.com/secretsmanager/)
- [AWS Systems Manager Parameter Store](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-store.html)
