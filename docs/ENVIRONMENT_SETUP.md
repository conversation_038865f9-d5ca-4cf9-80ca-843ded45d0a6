# Environment Setup & Credential Management

This document provides comprehensive guidelines for secure storage, access, and rotation of all OAuth, database, media upload, AI provider credentials, and other sensitive environment variables across development, staging, and production environments.

## 🔐 Security Principles

### Core Security Guidelines

1. **Never commit secrets to version control**
2. **Use environment-specific secret management**
3. **Apply principle of least privilege**
4. **Rotate credentials regularly**
5. **Monitor and audit access**
6. **Use secure transmission methods**

## 📋 Environment Variable Categories

### Required Variables (Application will not start without these)

#### Database Configuration
```env
DATABASE_URL="************************************************************"
DIRECT_URL="************************************************************"
```

#### Authentication & Session Management
```env
NEXTAUTH_SECRET="32-character-minimum-secret"  # Generate with: openssl rand -base64 32
NEXTAUTH_URL="https://yourdomain.com"         # Production URL
```

#### OAuth Providers
```env
# Google OAuth (Required)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Twitter/X OAuth (Required)
TWITTER_CLIENT_ID="your-twitter-client-id"
TWITTER_CLIENT_SECRET="your-twitter-client-secret"
```

#### Media Upload Service
```env
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
UPLOADTHING_TOKEN="your-uploadthing-token"
```

#### AI Providers (At least one required)
```env
OPENAI_API_KEY="your-openai-api-key"
GEMINI_API_KEY="your-gemini-api-key"
# Optional: MISTRAL_API_KEY, HUGGINGFACE_API_KEY, GROQ_API_KEY, OPENROUTER_API_KEY
```

### Optional Variables

#### Redis Configuration
```env
REDIS_URL="rediss://username:password@host:port"
```

#### Application Configuration
```env
NODE_ENV="production"
PORT="3030"
FRONTEND_URL="https://yourdomain.com"
```

## 🏗️ Environment-Specific Setup

### Development Environment

1. **Local Setup**
   ```bash
   # Copy example file
   cp .env.example .env
   
   # Edit with your development credentials
   nano .env
   
   # Validate configuration
   npm run validate-env
   ```

2. **Development Best Practices**
   - Use development-specific credentials
   - Never use production secrets in development
   - Use localhost URLs for callbacks
   - Enable debug logging

### Staging Environment

1. **Platform-Specific Configuration**

   **Vercel:**
   ```bash
   # Set environment variables via CLI
   vercel env add NEXTAUTH_SECRET staging
   vercel env add DATABASE_URL staging
   ```

   **AWS/EC2:**
   ```bash
   # Use AWS Systems Manager Parameter Store
   aws ssm put-parameter --name "/app/staging/NEXTAUTH_SECRET" --value "..." --type "SecureString"
   ```

   **Google Cloud:**
   ```bash
   # Use Google Secret Manager
   gcloud secrets create nextauth-secret --data-file=-
   ```

2. **Staging Best Practices**
   - Use staging-specific databases
   - Use staging OAuth applications
   - Mirror production configuration structure
   - Test credential rotation procedures

### Production Environment

1. **Secure Secret Management Services**

   **Vercel (Recommended for Next.js):**
   - Use Vercel Environment Variables dashboard
   - Enable "Encrypted" option for sensitive values
   - Use different values per environment

   **AWS:**
   - AWS Secrets Manager for automatic rotation
   - AWS Systems Manager Parameter Store for configuration
   - IAM roles for secure access

   **Google Cloud:**
   - Google Secret Manager
   - Cloud KMS for encryption
   - Service accounts for access control

   **Azure:**
   - Azure Key Vault
   - Managed Identity for access

2. **Production Best Practices**
   - Use managed secret services
   - Enable automatic rotation where possible
   - Implement secret versioning
   - Monitor secret access
   - Use separate secrets per service

## 🔄 Credential Rotation

### Rotation Schedule

| Credential Type | Rotation Frequency | Method |
|----------------|-------------------|---------|
| Database passwords | 90 days | Automated via cloud provider |
| API keys | 180 days | Manual rotation |
| OAuth secrets | 365 days | Manual via provider dashboard |
| JWT secrets | 90 days | Automated deployment |

### Rotation Procedure

1. **Pre-rotation**
   - Document current credentials
   - Prepare rollback plan
   - Schedule maintenance window

2. **Rotation**
   - Generate new credentials
   - Update secret management system
   - Deploy new configuration
   - Verify functionality

3. **Post-rotation**
   - Revoke old credentials
   - Update documentation
   - Monitor for issues

## 🛡️ Access Control

### Server-Side Only Variables
These must NEVER be exposed to the client:
- `GOOGLE_CLIENT_SECRET`
- `TWITTER_CLIENT_SECRET`
- `NEXTAUTH_SECRET`
- `DATABASE_URL`
- `UPLOADTHING_SECRET`
- All AI API keys

### Client-Side Variables
These can be safely exposed (use `NEXT_PUBLIC_` prefix):
- `NEXT_PUBLIC_GOOGLE_CLIENT_ID`
- `NEXT_PUBLIC_NEXTAUTH_URL`
- `NEXT_PUBLIC_FRONTEND_URL`

## 🔍 Monitoring & Auditing

### Health Checks
```bash
# Validate environment configuration
npm run validate-env

# Check application health
curl https://yourdomain.com/api/health
```

### Security Auditing
1. Regular credential access reviews
2. Monitor for exposed secrets in logs
3. Scan code for hardcoded credentials
4. Review environment variable access patterns

## 🚨 Incident Response

### Compromised Credentials
1. **Immediate Actions**
   - Revoke compromised credentials
   - Generate new credentials
   - Update all environments
   - Monitor for unauthorized access

2. **Investigation**
   - Determine scope of compromise
   - Review access logs
   - Identify attack vector
   - Document findings

3. **Recovery**
   - Implement additional security measures
   - Update rotation procedures
   - Conduct security training
   - Review and update documentation

## ✅ Validation & Testing

### Environment Validation
```bash
# Run validation script
npm run validate-env

# Check specific features
curl localhost:3000/api/health
```

### Security Testing
1. Verify secrets are not in client bundles
2. Test credential rotation procedures
3. Validate access controls
4. Monitor for secret exposure

## 📚 Additional Resources

- [OWASP Secrets Management Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html)
- [12-Factor App Config](https://12factor.net/config)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
