{"name": "xtask-monorepo", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run --workspace=apps/web dev:turbopack\" \"npm run --workspace=packages/server dev\"", "build": "npm run --workspace=apps/web build:turbopack", "start": "npm run --workspace=packages/server start", "lint": "npm run --workspace=apps/web lint", "type-check": "npm run --workspace=apps/web type-check && npm run --workspace=packages/server type-check", "install:all": "npm install && npm install --workspace=apps/web && npm install --workspace=packages/server", "validate-env": "node scripts/validate-env.js", "security-audit": "node scripts/security-audit.js", "deploy:vercel:staging": "./scripts/deploy-env-setup.sh vercel staging", "deploy:vercel:production": "./scripts/deploy-env-setup.sh vercel production", "deploy:aws:staging": "./scripts/deploy-env-setup.sh aws staging", "deploy:aws:production": "./scripts/deploy-env-setup.sh aws production", "db:setup": "node scripts/setup-database.js", "db:migrate": "prisma migrate dev --schema=prisma/schema.prisma", "db:generate": "prisma generate --schema=prisma/schema.prisma", "db:studio": "prisma studio --schema=prisma/schema.prisma", "db:reset": "prisma migrate reset --schema=prisma/schema.prisma"}, "devDependencies": {"@types/node": "^22.7.4", "concurrently": "^9.0.1", "dotenv": "^16.5.0", "prisma": "^5.8.0", "typescript": "^5.6.2"}, "dependencies": {"@prisma/client": "^5.8.0", "node-cron": "^3.0.3", "openai": "^4.24.0", "@google/genai": "1.5.1", "twitter-api-v2": "^1.17.2"}}