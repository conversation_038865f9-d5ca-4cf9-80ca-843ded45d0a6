/**
 * Environment Configuration
 * 
 * This file defines which environment variables are safe for client-side use
 * and provides type-safe access to environment variables.
 */

// Server-side only environment variables (NEVER expose to client)
export const SERVER_ONLY_VARS = [
  'DATABASE_URL',
  'DIRECT_URL',
  'NEXTAUTH_SECRET',
  'GOOGLE_CLIENT_SECRET',
  'TWITTER_CLIENT_SECRET',
  'UPLOADTHING_SECRET',
  'UPLOADTHING_TOKEN',
  'OPENAI_API_KEY',
  'GEMINI_API_KEY',
  'MISTRAL_API_KEY',
  'HUGGINGFACE_API_KEY',
  'GROQ_API_KEY',
  'OPENROUTER_API_KEY',
  'REDIS_URL',
] as const;

// Client-safe environment variables (must use NEXT_PUBLIC_ prefix)
export const CLIENT_SAFE_VARS = [
  'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
  'NEXT_PUBLIC_NEXTAUTH_URL',
  'NEXT_PUBLIC_FRONTEND_URL',
  'NEXT_PUBLIC_UPLOADTHING_APP_ID',
] as const;

/**
 * Server-side environment configuration
 * Only use this on the server side (API routes, middleware, server components)
 */
export const serverEnv = {
  // Database
  DATABASE_URL: process.env.DATABASE_URL!,
  DIRECT_URL: process.env.DIRECT_URL,
  
  // Authentication
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET!,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL!,
  
  // OAuth Secrets (Server-side only)
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET!,
  TWITTER_CLIENT_SECRET: process.env.TWITTER_CLIENT_SECRET!,
  
  // Media Upload Secrets
  UPLOADTHING_SECRET: process.env.UPLOADTHING_SECRET!,
  UPLOADTHING_TOKEN: process.env.UPLOADTHING_TOKEN!,
  
  // AI Provider Keys
  OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  GEMINI_API_KEY: process.env.GEMINI_API_KEY,
  MISTRAL_API_KEY: process.env.MISTRAL_API_KEY,
  HUGGINGFACE_API_KEY: process.env.HUGGINGFACE_API_KEY,
  GROQ_API_KEY: process.env.GROQ_API_KEY,
  OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
  
  // Optional
  REDIS_URL: process.env.REDIS_URL,
  
  // Application Configuration
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: process.env.PORT || '3030',
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3000',
  
  // Scheduling
  SCHEDULER_ENABLED: process.env.SCHEDULER_ENABLED !== 'false',
  SCHEDULER_INTERVAL: process.env.SCHEDULER_INTERVAL || '*/1 * * * *',
  
  // Debug
  DEBUG: process.env.DEBUG === 'true',
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
} as const;

/**
 * Client-side environment configuration
 * Only includes variables that are safe to expose to the browser
 */
export const clientEnv = {
  // OAuth Client IDs (safe to expose)
  GOOGLE_CLIENT_ID: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
  
  // Application URLs
  NEXTAUTH_URL: process.env.NEXT_PUBLIC_NEXTAUTH_URL!,
  FRONTEND_URL: process.env.NEXT_PUBLIC_FRONTEND_URL!,
  
  // UploadThing App ID (safe to expose)
  UPLOADTHING_APP_ID: process.env.NEXT_PUBLIC_UPLOADTHING_APP_ID!,
  
  // Environment
  NODE_ENV: process.env.NODE_ENV || 'development',
} as const;

/**
 * Type-safe environment variable access for server-side code
 */
export function getServerEnv<K extends keyof typeof serverEnv>(key: K): typeof serverEnv[K] {
  // Ensure we're on the server side
  if (typeof window !== 'undefined') {
    throw new Error(`Attempted to access server environment variable "${key}" on the client side. This is a security violation.`);
  }
  
  const value = serverEnv[key];
  if (value === undefined || value === '') {
    throw new Error(`Server environment variable "${key}" is not defined`);
  }
  
  return value;
}

/**
 * Type-safe environment variable access for client-side code
 */
export function getClientEnv<K extends keyof typeof clientEnv>(key: K): typeof clientEnv[K] {
  const value = clientEnv[key];
  if (value === undefined || value === '') {
    throw new Error(`Client environment variable "${key}" is not defined`);
  }
  
  return value;
}

/**
 * Check if we're running on the server side
 */
export function isServer(): boolean {
  return typeof window === 'undefined';
}

/**
 * Check if we're running on the client side
 */
export function isClient(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Get environment-specific configuration
 */
export function getEnvironmentConfig() {
  const env = serverEnv.NODE_ENV;
  
  return {
    isDevelopment: env === 'development',
    isProduction: env === 'production',
    isStaging: env === 'staging',
    isTest: env === 'test',
  };
}

/**
 * Validate that all required environment variables are set
 * Call this during application startup
 */
export function validateEnvironmentVariables(): void {
  const errors: string[] = [];
  
  // Check required server variables
  const requiredServerVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'GOOGLE_CLIENT_SECRET',
    'TWITTER_CLIENT_SECRET',
    'UPLOADTHING_SECRET',
    'UPLOADTHING_TOKEN',
  ] as const;
  
  requiredServerVars.forEach(varName => {
    if (!process.env[varName]) {
      errors.push(`Missing required server environment variable: ${varName}`);
    }
  });
  
  // Check that at least one AI provider is configured
  const aiProviders = [
    'OPENAI_API_KEY',
    'GEMINI_API_KEY',
    'MISTRAL_API_KEY',
    'HUGGINGFACE_API_KEY',
    'GROQ_API_KEY',
    'OPENROUTER_API_KEY',
  ];
  
  const hasAIProvider = aiProviders.some(provider => process.env[provider]);
  if (!hasAIProvider) {
    errors.push('At least one AI provider API key must be configured');
  }
  
  // Check required client variables (if on client side)
  if (isClient()) {
    const requiredClientVars = [
      'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
      'NEXT_PUBLIC_NEXTAUTH_URL',
      'NEXT_PUBLIC_FRONTEND_URL',
      'NEXT_PUBLIC_UPLOADTHING_APP_ID',
    ] as const;
    
    requiredClientVars.forEach(varName => {
      if (!process.env[varName]) {
        errors.push(`Missing required client environment variable: ${varName}`);
      }
    });
  }
  
  if (errors.length > 0) {
    throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
  }
}

/**
 * Get a summary of configured features
 */
export function getFeatureSummary() {
  return {
    database: !!serverEnv.DATABASE_URL,
    redis: !!serverEnv.REDIS_URL,
    googleOAuth: !!serverEnv.GOOGLE_CLIENT_SECRET,
    twitterOAuth: !!serverEnv.TWITTER_CLIENT_SECRET,
    uploadThing: !!(serverEnv.UPLOADTHING_SECRET && serverEnv.UPLOADTHING_TOKEN),
    aiProviders: {
      openai: !!serverEnv.OPENAI_API_KEY,
      gemini: !!serverEnv.GEMINI_API_KEY,
      mistral: !!serverEnv.MISTRAL_API_KEY,
      huggingface: !!serverEnv.HUGGINGFACE_API_KEY,
      groq: !!serverEnv.GROQ_API_KEY,
      openrouter: !!serverEnv.OPENROUTER_API_KEY,
    },
    scheduler: serverEnv.SCHEDULER_ENABLED,
  };
}
