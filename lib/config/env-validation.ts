import { z } from 'zod'

/**
 * Environment variable validation schema
 * Ensures all required environment variables are present and correctly formatted
 */
export const envSchema = z.object({
  // Application Configuration
  NODE_ENV: z.enum(['development', 'staging', 'production', 'test']).default('development'),
  PORT: z.coerce.number().min(1).max(65535).default(3030),
  FRONTEND_URL: z.string().url().default('http://localhost:3000'),

  // Database Configuration (Required)
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  DIRECT_URL: z.string().optional(),

  // Redis Configuration (Optional)
  REDIS_URL: z.string().optional(),

  // Authentication & Session Management (Required)
  NEXTAUTH_SECRET: z.string().min(32, 'NEXTAUTH_SECRET must be at least 32 characters'),
  NEXTAUTH_URL: z.string().url('NEXTAUTH_URL must be a valid URL'),

  // OAuth Providers (Required)
  GOOGLE_CLIENT_ID: z.string().min(1, 'GOOGLE_CLIENT_ID is required'),
  GOOGLE_CLIENT_SECRET: z.string().min(1, 'GOOGLE_CLIENT_SECRET is required'),
  
  TWITTER_CLIENT_ID: z.string().min(1, 'TWITTER_CLIENT_ID is required'),
  TWITTER_CLIENT_SECRET: z.string().min(1, 'TWITTER_CLIENT_SECRET is required'),
  TWITTER_CALLBACK_URL: z.string().url().default('http://localhost:3000/api/auth/twitter/callback'),

  // Media Upload Service (Required)
  UPLOADTHING_SECRET: z.string().min(1, 'UPLOADTHING_SECRET is required'),
  UPLOADTHING_APP_ID: z.string().min(1, 'UPLOADTHING_APP_ID is required'),
  UPLOADTHING_TOKEN: z.string().min(1, 'UPLOADTHING_TOKEN is required'),

  // AI Providers (At least one required)
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_BASE_URL: z.string().url().optional(),
  OPENAI_DEFAULT_MODEL: z.string().default('gpt-4'),
  OPENAI_MAX_TOKENS: z.coerce.number().min(1).default(4000),
  OPENAI_TEMPERATURE: z.coerce.number().min(0).max(2).default(0.7),

  GEMINI_API_KEY: z.string().optional(),
  MISTRAL_API_KEY: z.string().optional(),
  HUGGINGFACE_API_KEY: z.string().optional(),
  GROQ_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),

  // Scheduling Configuration
  SCHEDULER_ENABLED: z.coerce.boolean().default(true),
  SCHEDULER_INTERVAL: z.string().default('*/1 * * * *'),

  // Development Settings
  DEBUG: z.coerce.boolean().default(false),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
})

/**
 * Refined schema with custom validation for AI providers
 */
export const envSchemaRefined = envSchema.refine(
  (data) => {
    // At least one AI provider must be configured
    const aiProviders = [
      data.OPENAI_API_KEY,
      data.GEMINI_API_KEY,
      data.MISTRAL_API_KEY,
      data.HUGGINGFACE_API_KEY,
      data.GROQ_API_KEY,
      data.OPENROUTER_API_KEY,
    ]
    return aiProviders.some(key => key && key.length > 0)
  },
  {
    message: 'At least one AI provider API key must be configured (OPENAI_API_KEY, GEMINI_API_KEY, MISTRAL_API_KEY, HUGGINGFACE_API_KEY, GROQ_API_KEY, or OPENROUTER_API_KEY)',
    path: ['AI_PROVIDERS'],
  }
)

export type EnvConfig = z.infer<typeof envSchemaRefined>

/**
 * Validate environment variables and return parsed configuration
 */
export function validateEnv(): EnvConfig {
  try {
    const parsed = envSchemaRefined.parse(process.env)
    return parsed
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => {
        const path = err.path.join('.')
        return `  ❌ ${path}: ${err.message}`
      })

      console.error('🚨 Environment validation failed:')
      console.error(errorMessages.join('\n'))
      console.error('\n📝 Please check your .env file and ensure all required variables are set.')
      console.error('💡 Copy .env.example to .env and fill in your actual values.')
      
      process.exit(1)
    }
    throw error
  }
}

/**
 * Get feature availability based on configured environment variables
 */
export function getFeatureAvailability(env: EnvConfig) {
  return {
    database: !!env.DATABASE_URL,
    redis: !!env.REDIS_URL,
    googleOAuth: !!(env.GOOGLE_CLIENT_ID && env.GOOGLE_CLIENT_SECRET),
    twitterOAuth: !!(env.TWITTER_CLIENT_ID && env.TWITTER_CLIENT_SECRET),
    uploadThing: !!(env.UPLOADTHING_SECRET && env.UPLOADTHING_APP_ID && env.UPLOADTHING_TOKEN),
    openai: !!env.OPENAI_API_KEY,
    gemini: !!env.GEMINI_API_KEY,
    mistral: !!env.MISTRAL_API_KEY,
    huggingface: !!env.HUGGINGFACE_API_KEY,
    groq: !!env.GROQ_API_KEY,
    openrouter: !!env.OPENROUTER_API_KEY,
    scheduler: env.SCHEDULER_ENABLED,
  }
}

/**
 * Get count of configured AI providers
 */
export function getAIProviderCount(env: EnvConfig): number {
  const providers = [
    env.OPENAI_API_KEY,
    env.GEMINI_API_KEY,
    env.MISTRAL_API_KEY,
    env.HUGGINGFACE_API_KEY,
    env.GROQ_API_KEY,
    env.OPENROUTER_API_KEY,
  ]
  return providers.filter(key => key && key.length > 0).length
}

/**
 * Validate environment variables at startup
 * Call this in your application entry point
 */
export function initializeEnvironment(): EnvConfig {
  console.log('🔍 Validating environment configuration...')
  const env = validateEnv()
  
  const features = getFeatureAvailability(env)
  const aiProviderCount = getAIProviderCount(env)
  
  console.log('✅ Environment validation successful!')
  console.log(`🤖 AI Providers configured: ${aiProviderCount}`)
  console.log(`🔧 Features available:`, Object.entries(features)
    .filter(([, enabled]) => enabled)
    .map(([feature]) => feature)
    .join(', '))
  
  return env
}
