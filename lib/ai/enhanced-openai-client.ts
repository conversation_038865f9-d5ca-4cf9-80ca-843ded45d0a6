import OpenAI from 'openai'
import { memoryService } from './memory-service'
import { geminiClient } from './gemini-client'

// Enhanced OpenAI client with memory integration
export class EnhancedOpenAIClient {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    })
  }

  /**
   * Generate tweet content with memory context
   */
  async generateTweetWithMemory(
    agentPersona: any,
    agentId: string,
    topic?: string,
    context?: string[],
    model?: string
  ): Promise<{
    content: string
    usage: any
    model: string
    memoriesUsed: number
  }> {
    try {
      // Get relevant memories if we have context
      let relevantMemories: string[] = []
      if (topic) {
        const memoryResults = await memoryService.searchMemories(
          topic,
          agentId,
          { limit: 3, threshold: 0.7 }
        )
        relevantMemories = memoryResults.map(result => result.memory.content)
      }

      // Decide whether to use Gemini or OpenAI based on availability and task complexity
      const useGemini = geminiClient.isAvailable() && Math.random() > 0.5 // 50% chance to use Gemini for cost optimization

      if (useGemini) {
        const prompt = this.buildTweetPrompt(agentPersona, topic, relevantMemories)
        const response = await geminiClient.generateContentWithRetry(prompt, {
          model: geminiClient.getOptimalModel('simple', true),
          temperature: 0.8,
          maxOutputTokens: 150,
        })

        return {
          content: response.content,
          usage: response.usage || { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          model: response.model,
          memoriesUsed: relevantMemories.length,
        }
      } else {
        // Use OpenAI
        const systemPrompt = this.buildSystemPrompt(agentPersona, relevantMemories)
        const userPrompt = topic 
          ? `Create a tweet about: ${topic}`
          : `Create an engaging tweet about one of your topics of interest.`

        const completion = await this.openai.chat.completions.create({
          model: model || process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
            ...(context?.map((msg, i) => ({
              role: i % 2 === 0 ? 'user' : 'assistant',
              content: msg,
            })) || []),
          ],
          max_tokens: 150,
          temperature: 0.8,
        })

        const choice = completion.choices[0]
        if (!choice || !choice.message.content) {
          throw new Error('No content generated from OpenAI API')
        }

        return {
          content: choice.message.content,
          usage: {
            promptTokens: completion.usage?.prompt_tokens || 0,
            completionTokens: completion.usage?.completion_tokens || 0,
            totalTokens: completion.usage?.total_tokens || 0,
          },
          model: completion.model,
          memoriesUsed: relevantMemories.length,
        }
      }
    } catch (error) {
      console.error('Enhanced tweet generation error:', error)
      throw new Error(`Failed to generate tweet with memory: ${error}`)
    }
  }

  /**
   * Generate thread content with memory context
   */
  async generateThreadWithMemory(
    agentPersona: any,
    agentId: string,
    topic: string,
    tweetCount: number = 3,
    model?: string
  ): Promise<{
    content: string
    usage: any
    model: string
    memoriesUsed: number
  }> {
    try {
      // Get relevant memories for the topic
      const memoryResults = await memoryService.searchMemories(
        topic,
        agentId,
        { limit: 5, threshold: 0.6 }
      )
      const relevantMemories = memoryResults.map(result => result.memory.content)

      // Use Gemini for thread generation if available (better for longer content)
      if (geminiClient.isAvailable()) {
        const prompt = `
You are an AI social media agent with this persona:
- Personality: ${agentPersona.personality}
- Tone: ${agentPersona.tone}
- Writing Style: ${agentPersona.writingStyle}
- Topics: ${agentPersona.topics.join(', ')}

${relevantMemories.length > 0 ? `
Based on these relevant memories:
${relevantMemories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}
` : ''}

Create a ${tweetCount}-tweet thread about: ${topic}

Requirements:
- Each tweet should be under 280 characters
- Number each tweet (1/n, 2/n, etc.)
- The thread should flow naturally and provide value
- Match the personality and tone described
- Use memories to inform your content when relevant

Thread:
`

        const response = await geminiClient.generateContentWithRetry(prompt, {
          model: geminiClient.getOptimalModel('medium', true),
          temperature: 0.8,
          maxOutputTokens: 500 * tweetCount,
        })

        return {
          content: response.content,
          usage: response.usage || { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          model: response.model,
          memoriesUsed: relevantMemories.length,
        }
      } else {
        // Fallback to OpenAI
        const systemPrompt = this.buildSystemPrompt(agentPersona, relevantMemories)
        const userPrompt = `Create a ${tweetCount}-tweet thread about: ${topic}`

        const completion = await this.openai.chat.completions.create({
          model: model || process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
          ],
          max_tokens: 500 * tweetCount,
          temperature: 0.8,
        })

        const choice = completion.choices[0]
        if (!choice || !choice.message.content) {
          throw new Error('No content generated from OpenAI API')
        }

        return {
          content: choice.message.content,
          usage: {
            promptTokens: completion.usage?.prompt_tokens || 0,
            completionTokens: completion.usage?.completion_tokens || 0,
            totalTokens: completion.usage?.total_tokens || 0,
          },
          model: completion.model,
          memoriesUsed: relevantMemories.length,
        }
      }
    } catch (error) {
      console.error('Enhanced thread generation error:', error)
      throw new Error(`Failed to generate thread with memory: ${error}`)
    }
  }

  /**
   * Store interaction as memory
   */
  async storeInteractionMemory(
    agentId: string,
    interaction: {
      userInput: string
      agentResponse: string
      context?: string
    }
  ): Promise<void> {
    try {
      const memoryContent = `User: ${interaction.userInput}\nAgent: ${interaction.agentResponse}`
      
      await memoryService.createMemory({
        content: memoryContent,
        context: interaction.context || 'conversation',
        agentId,
      })
    } catch (error) {
      console.error('Error storing interaction memory:', error)
      // Don't throw here to avoid breaking the main flow
    }
  }

  /**
   * Build system prompt with memory context
   */
  private buildSystemPrompt(agentPersona: any, memories: string[] = []): string {
    let prompt = `You are an AI social media agent with the following persona:

Personality: ${agentPersona.personality}
Tone: ${agentPersona.tone}
Writing Style: ${agentPersona.writingStyle}
Topics of Interest: ${agentPersona.topics.join(', ')}
Communication Style: ${agentPersona.communicationStyle || 'Engaging and authentic'}
Target Audience: ${agentPersona.targetAudience || 'General audience'}

Content Restrictions:
${agentPersona.restrictions.map((r: string) => `- ${r}`).join('\n')}

${agentPersona.avoidTopics?.length ? `Topics to Avoid: ${agentPersona.avoidTopics.join(', ')}` : ''}`

    if (memories.length > 0) {
      prompt += `\n\nRelevant memories from past interactions:
${memories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}

Use these memories to inform your responses and maintain consistency with past interactions.`
    }

    prompt += `\n\nGenerate engaging, authentic content that matches this persona. Keep tweets under 280 characters. Be creative, informative, and true to the personality described.`

    return prompt
  }

  /**
   * Build tweet prompt for Gemini
   */
  private buildTweetPrompt(agentPersona: any, topic?: string, memories: string[] = []): string {
    let prompt = `You are an AI social media agent with this persona:
- Personality: ${agentPersona.personality}
- Tone: ${agentPersona.tone}
- Writing Style: ${agentPersona.writingStyle}
- Topics: ${agentPersona.topics.join(', ')}

${memories.length > 0 ? `
Based on these relevant memories:
${memories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}
` : ''}

${topic ? `Create a tweet about: ${topic}` : 'Create an engaging tweet about one of your topics of interest.'}

Requirements:
- Keep under 280 characters
- Match the personality and tone described
- Be engaging and authentic
- Include relevant hashtags if appropriate
- Use memories to inform your content when relevant

Tweet:`

    return prompt
  }
}

// Export singleton instance
export const enhancedOpenAIClient = new EnhancedOpenAIClient()