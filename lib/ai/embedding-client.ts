import { GoogleGenerativeAI } from '@google/generative-ai'
import OpenAI from 'openai'

// Configuration for embedding models
export const EMBEDDING_MODELS = {
  openai: {
    'text-embedding-3-small': { dimensions: 1536, maxTokens: 8191 },
    'text-embedding-3-large': { dimensions: 3072, maxTokens: 8191 },
    'text-embedding-ada-002': { dimensions: 1536, maxTokens: 8191 },
  },
  google: {
    'text-embedding-004': { dimensions: 768, maxTokens: 2048 },
    'embedding-001': { dimensions: 768, maxTokens: 2048 },
  },
} as const

export type EmbeddingProvider = keyof typeof EMBEDDING_MODELS
export type OpenAIEmbeddingModel = keyof typeof EMBEDDING_MODELS.openai
export type GoogleEmbeddingModel = keyof typeof EMBEDDING_MODELS.google

export interface EmbeddingResult {
  embedding: number[]
  dimensions: number
  model: string
  usage?: {
    promptTokens: number
    totalTokens: number
  }
}

export interface EmbeddingConfig {
  provider: EmbeddingProvider
  model: string
  dimensions?: number
}

class EmbeddingClient {
  private openaiClient: OpenAI | null = null
  private googleClient: GoogleGenerativeAI | null = null

  constructor() {
    this.initializeClients()
  }

  private initializeClients() {
    // Initialize OpenAI client
    if (process.env.OPENAI_API_KEY) {
      this.openaiClient = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      })
    }

    // Initialize Google client
    if (process.env.GOOGLE_AI_API_KEY) {
      this.googleClient = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY)
    }
  }

  /**
   * Generate embeddings using the specified model
   */
  async generateEmbedding(
    text: string,
    config: EmbeddingConfig
  ): Promise<EmbeddingResult> {
    if (!text.trim()) {
      throw new Error('Text cannot be empty')
    }

    switch (config.provider) {
      case 'openai':
        return this.generateOpenAIEmbedding(text, config.model as OpenAIEmbeddingModel)
      case 'google':
        return this.generateGoogleEmbedding(text, config.model as GoogleEmbeddingModel)
      default:
        throw new Error(`Unsupported embedding provider: ${config.provider}`)
    }
  }

  /**
   * Generate embeddings using OpenAI models
   */
  private async generateOpenAIEmbedding(
    text: string,
    model: OpenAIEmbeddingModel
  ): Promise<EmbeddingResult> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized. Check OPENAI_API_KEY.')
    }

    try {
      const response = await this.openaiClient.embeddings.create({
        model,
        input: text,
        encoding_format: 'float',
      })

      const embedding = response.data[0].embedding
      const modelConfig = EMBEDDING_MODELS.openai[model]

      return {
        embedding,
        dimensions: modelConfig.dimensions,
        model,
        usage: {
          promptTokens: response.usage.prompt_tokens,
          totalTokens: response.usage.total_tokens,
        },
      }
    } catch (error) {
      console.error('OpenAI embedding error:', error)
      throw new Error(`Failed to generate OpenAI embedding: ${error}`)
    }
  }

  /**
   * Generate embeddings using Google models
   */
  private async generateGoogleEmbedding(
    text: string,
    model: GoogleEmbeddingModel
  ): Promise<EmbeddingResult> {
    if (!this.googleClient) {
      throw new Error('Google client not initialized. Check GOOGLE_AI_API_KEY.')
    }

    try {
      const embeddingModel = this.googleClient.getGenerativeModel({ model })
      const result = await embeddingModel.embedContent(text)
      
      const embedding = result.embedding.values
      const modelConfig = EMBEDDING_MODELS.google[model]

      if (!embedding) {
        throw new Error('No embedding returned from Google API')
      }

      return {
        embedding,
        dimensions: modelConfig.dimensions,
        model,
      }
    } catch (error) {
      console.error('Google embedding error:', error)
      throw new Error(`Failed to generate Google embedding: ${error}`)
    }
  }

  /**
   * Generate embeddings in batches for better performance
   */
  async generateBatchEmbeddings(
    texts: string[],
    config: EmbeddingConfig
  ): Promise<EmbeddingResult[]> {
    const results: EmbeddingResult[] = []
    const batchSize = config.provider === 'openai' ? 100 : 10 // Different batch sizes

    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize)
      const batchPromises = batch.map(text => this.generateEmbedding(text, config))
      
      try {
        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)
        
        // Add delay between batches to respect rate limits
        if (i + batchSize < texts.length) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      } catch (error) {
        console.error(`Batch embedding error for batch starting at ${i}:`, error)
        throw error
      }
    }

    return results
  }

  /**
   * Get the default embedding configuration
   */
  getDefaultConfig(): EmbeddingConfig {
    // Prefer Google for cost-effectiveness, fallback to OpenAI
    if (process.env.GOOGLE_AI_API_KEY) {
      return {
        provider: 'google',
        model: 'text-embedding-004',
        dimensions: 768,
      }
    } else if (process.env.OPENAI_API_KEY) {
      return {
        provider: 'openai',
        model: 'text-embedding-3-small',
        dimensions: 1536,
      }
    } else {
      throw new Error('No embedding API keys configured')
    }
  }

  /**
   * Validate embedding configuration
   */
  validateConfig(config: EmbeddingConfig): boolean {
    const { provider, model } = config
    
    if (provider === 'openai') {
      return model in EMBEDDING_MODELS.openai && !!this.openaiClient
    } else if (provider === 'google') {
      return model in EMBEDDING_MODELS.google && !!this.googleClient
    }
    
    return false
  }
}

// Export singleton instance
export const embeddingClient = new EmbeddingClient()

// Utility functions
export function normalizeVector(vector: number[]): number[] {
  const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))
  return magnitude > 0 ? vector.map(val => val / magnitude) : vector
}

export function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length')
  }
  
  const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0)
  const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0))
  const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0))
  
  return dotProduct / (magnitudeA * magnitudeB)
}