import { prisma } from '@/lib/database'
import { embeddingClient, type EmbeddingConfig } from './embedding-client'
import { geminiClient } from './gemini-client'

export interface AgentMemory {
  id: string
  content: string
  context?: string
  embedding: number[]
  agentId: string
  createdAt: Date
  updatedAt: Date
}

export interface MemorySearchResult {
  memory: AgentMemory
  similarity: number
}

export interface CreateMemoryData {
  content: string
  context?: string
  agentId: string
}

export interface MemorySearchOptions {
  limit?: number
  threshold?: number
  includeContext?: boolean
}

class MemoryService {
  private embeddingConfig: EmbeddingConfig

  constructor() {
    this.embeddingConfig = embeddingClient.getDefaultConfig()
  }

  /**
   * Create a new memory with embedding
   */
  async createMemory(data: CreateMemoryData): Promise<AgentMemory> {
    try {
      // Generate embedding for the content
      const embeddingResult = await embeddingClient.generateEmbedding(
        data.content,
        this.embeddingConfig
      )

      // Convert embedding to buffer for storage
      const embeddingBuffer = Buffer.from(
        new Float32Array(embeddingResult.embedding).buffer
      )

      // Store in database
      const memory = await prisma.agentMemory.create({
        data: {
          content: data.content,
          context: data.context,
          embedding: embeddingBuffer,
          agentId: data.agentId,
        },
      })

      return {
        id: memory.id,
        content: memory.content,
        context: memory.context || undefined,
        embedding: embeddingResult.embedding,
        agentId: memory.agentId,
        createdAt: memory.createdAt,
        updatedAt: memory.updatedAt,
      }
    } catch (error) {
      console.error('Error creating memory:', error)
      throw new Error(`Failed to create memory: ${error}`)
    }
  }

  /**
   * Create multiple memories in batch
   */
  async createMemories(memories: CreateMemoryData[]): Promise<AgentMemory[]> {
    try {
      // Generate embeddings for all content
      const texts = memories.map(m => m.content)
      const embeddingResults = await embeddingClient.generateBatchEmbeddings(
        texts,
        this.embeddingConfig
      )

      // Prepare data for batch insert
      const memoryData = memories.map((memory, index) => ({
        content: memory.content,
        context: memory.context,
        embedding: Buffer.from(
          new Float32Array(embeddingResults[index].embedding).buffer
        ),
        agentId: memory.agentId,
      }))

      // Batch insert
      const createdMemories = await prisma.agentMemory.createMany({
        data: memoryData,
      })

      // Return created memories with embeddings
      return memories.map((memory, index) => ({
        id: `batch-${index}`, // This would be replaced with actual IDs in a real implementation
        content: memory.content,
        context: memory.context,
        embedding: embeddingResults[index].embedding,
        agentId: memory.agentId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }))
    } catch (error) {
      console.error('Error creating batch memories:', error)
      throw new Error(`Failed to create batch memories: ${error}`)
    }
  }

  /**
   * Search for similar memories using vector similarity
   */
  async searchMemories(
    query: string,
    agentId: string,
    options: MemorySearchOptions = {}
  ): Promise<MemorySearchResult[]> {
    const { limit = 5, threshold = 0.7, includeContext = true } = options

    try {
      // Generate embedding for the query
      const queryEmbedding = await embeddingClient.generateEmbedding(
        query,
        this.embeddingConfig
      )

      // Convert embedding to the format expected by pgvector
      const queryVector = `[${queryEmbedding.embedding.join(',')}]`

      // Perform similarity search using pgvector
      const results = await prisma.$queryRaw<Array<{
        id: string
        content: string
        context: string | null
        embedding: Buffer
        agent_id: string
        created_at: Date
        updated_at: Date
        similarity: number
      }>>`
        SELECT 
          id,
          content,
          context,
          embedding,
          agent_id,
          created_at,
          updated_at,
          1 - (embedding <=> ${queryVector}::vector) as similarity
        FROM agent_memories 
        WHERE agent_id = ${agentId}
          AND 1 - (embedding <=> ${queryVector}::vector) > ${threshold}
        ORDER BY embedding <=> ${queryVector}::vector
        LIMIT ${limit}
      `

      // Convert results to proper format
      return results.map(row => ({
        memory: {
          id: row.id,
          content: row.content,
          context: includeContext ? row.context || undefined : undefined,
          embedding: this.bufferToEmbedding(row.embedding),
          agentId: row.agent_id,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
        },
        similarity: row.similarity,
      }))
    } catch (error) {
      console.error('Error searching memories:', error)
      throw new Error(`Failed to search memories: ${error}`)
    }
  }

  /**
   * Get memories for an agent with context-aware retrieval
   */
  async getContextualMemories(
    agentId: string,
    currentContext: string,
    limit: number = 10
  ): Promise<AgentMemory[]> {
    try {
      // Search for relevant memories based on current context
      const searchResults = await this.searchMemories(
        currentContext,
        agentId,
        { limit, threshold: 0.6 }
      )

      return searchResults.map(result => result.memory)
    } catch (error) {
      console.error('Error getting contextual memories:', error)
      throw new Error(`Failed to get contextual memories: ${error}`)
    }
  }

  /**
   * Generate agent response using memories and Gemini
   */
  async generateContextualResponse(
    agentId: string,
    query: string,
    agentPersona: any,
    maxMemories: number = 5
  ): Promise<string> {
    try {
      // Retrieve relevant memories
      const memoryResults = await this.searchMemories(
        query,
        agentId,
        { limit: maxMemories, threshold: 0.6 }
      )

      const relevantMemories = memoryResults.map(result => result.memory.content)

      // Generate response using Gemini with memory context
      const prompt = `
You are an AI agent with this persona:
- Personality: ${agentPersona.personality}
- Tone: ${agentPersona.tone}
- Writing Style: ${agentPersona.writingStyle}
- Topics: ${agentPersona.topics.join(', ')}

Based on these relevant memories from your past interactions:
${relevantMemories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}

Respond to this query: ${query}

Use your memories to provide context and inform your response. Be specific and reference relevant details when appropriate. Maintain your personality and tone throughout.
`

      const response = await geminiClient.generateContentWithRetry(prompt, {
        model: geminiClient.getOptimalModel('medium', true),
        temperature: 0.7,
        maxOutputTokens: 1000,
      })

      return response.content
    } catch (error) {
      console.error('Error generating contextual response:', error)
      throw new Error(`Failed to generate contextual response: ${error}`)
    }
  }

  /**
   * Update memory content and regenerate embedding
   */
  async updateMemory(
    memoryId: string,
    content: string,
    context?: string
  ): Promise<AgentMemory> {
    try {
      // Generate new embedding
      const embeddingResult = await embeddingClient.generateEmbedding(
        content,
        this.embeddingConfig
      )

      const embeddingBuffer = Buffer.from(
        new Float32Array(embeddingResult.embedding).buffer
      )

      // Update in database
      const memory = await prisma.agentMemory.update({
        where: { id: memoryId },
        data: {
          content,
          context,
          embedding: embeddingBuffer,
          updatedAt: new Date(),
        },
      })

      return {
        id: memory.id,
        content: memory.content,
        context: memory.context || undefined,
        embedding: embeddingResult.embedding,
        agentId: memory.agentId,
        createdAt: memory.createdAt,
        updatedAt: memory.updatedAt,
      }
    } catch (error) {
      console.error('Error updating memory:', error)
      throw new Error(`Failed to update memory: ${error}`)
    }
  }

  /**
   * Delete a memory
   */
  async deleteMemory(memoryId: string): Promise<void> {
    try {
      await prisma.agentMemory.delete({
        where: { id: memoryId },
      })
    } catch (error) {
      console.error('Error deleting memory:', error)
      throw new Error(`Failed to delete memory: ${error}`)
    }
  }

  /**
   * Get all memories for an agent
   */
  async getAgentMemories(
    agentId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<AgentMemory[]> {
    try {
      const memories = await prisma.agentMemory.findMany({
        where: { agentId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      })

      return memories.map(memory => ({
        id: memory.id,
        content: memory.content,
        context: memory.context || undefined,
        embedding: this.bufferToEmbedding(memory.embedding),
        agentId: memory.agentId,
        createdAt: memory.createdAt,
        updatedAt: memory.updatedAt,
      }))
    } catch (error) {
      console.error('Error getting agent memories:', error)
      throw new Error(`Failed to get agent memories: ${error}`)
    }
  }

  /**
   * Convert buffer to embedding array
   */
  private bufferToEmbedding(buffer: Buffer | null): number[] {
    if (!buffer) return []
    
    try {
      const float32Array = new Float32Array(buffer.buffer, buffer.byteOffset, buffer.byteLength / 4)
      return Array.from(float32Array)
    } catch (error) {
      console.error('Error converting buffer to embedding:', error)
      return []
    }
  }

  /**
   * Get memory statistics for an agent
   */
  async getMemoryStats(agentId: string): Promise<{
    totalMemories: number
    averageContentLength: number
    oldestMemory?: Date
    newestMemory?: Date
  }> {
    try {
      const stats = await prisma.agentMemory.aggregate({
        where: { agentId },
        _count: { id: true },
        _min: { createdAt: true },
        _max: { createdAt: true },
      })

      const memories = await prisma.agentMemory.findMany({
        where: { agentId },
        select: { content: true },
      })

      const averageContentLength = memories.length > 0
        ? memories.reduce((sum, m) => sum + m.content.length, 0) / memories.length
        : 0

      return {
        totalMemories: stats._count.id,
        averageContentLength: Math.round(averageContentLength),
        oldestMemory: stats._min.createdAt || undefined,
        newestMemory: stats._max.createdAt || undefined,
      }
    } catch (error) {
      console.error('Error getting memory stats:', error)
      throw new Error(`Failed to get memory stats: ${error}`)
    }
  }
}

// Export singleton instance
export const memoryService = new MemoryService()