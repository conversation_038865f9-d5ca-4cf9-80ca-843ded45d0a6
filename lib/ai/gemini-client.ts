import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold, GenerativeModel } from '@google/generative-ai'

export interface GeminiConfig {
  model: 'gemini-1.5-pro' | 'gemini-1.5-flash'
  temperature?: number
  topP?: number
  topK?: number
  maxOutputTokens?: number
  safetySettings?: Array<{
    category: HarmCategory
    threshold: HarmBlockThreshold
  }>
}

export interface GeminiResponse {
  content: string
  usage?: {
    promptTokens: number
    candidatesTokens: number
    totalTokens: number
  }
  model: string
  finishReason?: string
  safetyRatings?: Array<{
    category: HarmCategory
    probability: string
  }>
}

export interface FunctionDeclaration {
  name: string
  description: string
  parameters: {
    type: string
    properties: Record<string, any>
    required?: string[]
  }
}

export interface FunctionCall {
  name: string
  args: Record<string, any>
}

class GeminiClient {
  private client: GoogleGenerativeAI | null = null
  private defaultConfig: GeminiConfig

  constructor() {
    this.initializeClient()
    this.defaultConfig = {
      model: 'gemini-1.5-flash', // Default to faster, cheaper model
      temperature: 0.7,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 2048,
      safetySettings: [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ],
    }
  }

  private initializeClient() {
    const apiKey = process.env.GOOGLE_AI_API_KEY
    if (!apiKey) {
      console.warn('Google AI API key not found. Gemini features will be disabled.')
      return
    }

    this.client = new GoogleGenerativeAI(apiKey)
  }

  /**
   * Generate content using Gemini models
   */
  async generateContent(
    prompt: string,
    config: Partial<GeminiConfig> = {}
  ): Promise<GeminiResponse> {
    if (!this.client) {
      throw new Error('Gemini client not initialized. Check GOOGLE_AI_API_KEY.')
    }

    const finalConfig = { ...this.defaultConfig, ...config }
    
    try {
      const model = this.client.getGenerativeModel({
        model: finalConfig.model,
        generationConfig: {
          temperature: finalConfig.temperature,
          topP: finalConfig.topP,
          topK: finalConfig.topK,
          maxOutputTokens: finalConfig.maxOutputTokens,
        },
        safetySettings: finalConfig.safetySettings,
      })

      const result = await model.generateContent(prompt)
      const response = await result.response

      return {
        content: response.text(),
        model: finalConfig.model,
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          candidatesTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0,
        } : undefined,
        finishReason: response.candidates?.[0]?.finishReason,
        safetyRatings: response.candidates?.[0]?.safetyRatings,
      }
    } catch (error) {
      console.error('Gemini generation error:', error)
      throw new Error(`Failed to generate content with Gemini: ${error}`)
    }
  }

  /**
   * Generate content with function calling
   */
  async generateContentWithFunctions(
    prompt: string,
    functions: FunctionDeclaration[],
    config: Partial<GeminiConfig> = {}
  ): Promise<GeminiResponse & { functionCalls?: FunctionCall[] }> {
    if (!this.client) {
      throw new Error('Gemini client not initialized. Check GOOGLE_AI_API_KEY.')
    }

    const finalConfig = { ...this.defaultConfig, ...config }
    
    try {
      const model = this.client.getGenerativeModel({
        model: finalConfig.model,
        generationConfig: {
          temperature: finalConfig.temperature,
          topP: finalConfig.topP,
          topK: finalConfig.topK,
          maxOutputTokens: finalConfig.maxOutputTokens,
        },
        safetySettings: finalConfig.safetySettings,
        tools: [{ functionDeclarations: functions }],
      })

      const result = await model.generateContent(prompt)
      const response = await result.response

      // Extract function calls if any
      const functionCalls: FunctionCall[] = []
      const candidates = response.candidates || []
      
      for (const candidate of candidates) {
        const parts = candidate.content?.parts || []
        for (const part of parts) {
          if (part.functionCall) {
            functionCalls.push({
              name: part.functionCall.name,
              args: part.functionCall.args || {},
            })
          }
        }
      }

      return {
        content: response.text(),
        model: finalConfig.model,
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          candidatesTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0,
        } : undefined,
        finishReason: response.candidates?.[0]?.finishReason,
        safetyRatings: response.candidates?.[0]?.safetyRatings,
        functionCalls: functionCalls.length > 0 ? functionCalls : undefined,
      }
    } catch (error) {
      console.error('Gemini function calling error:', error)
      throw new Error(`Failed to generate content with functions: ${error}`)
    }
  }

  /**
   * Generate content with multimodal input (text + images)
   */
  async generateMultimodalContent(
    textPrompt: string,
    imageData: string | Buffer,
    mimeType: string = 'image/jpeg',
    config: Partial<GeminiConfig> = {}
  ): Promise<GeminiResponse> {
    if (!this.client) {
      throw new Error('Gemini client not initialized. Check GOOGLE_AI_API_KEY.')
    }

    const finalConfig = { ...this.defaultConfig, ...config }
    
    try {
      const model = this.client.getGenerativeModel({
        model: finalConfig.model,
        generationConfig: {
          temperature: finalConfig.temperature,
          topP: finalConfig.topP,
          topK: finalConfig.topK,
          maxOutputTokens: finalConfig.maxOutputTokens,
        },
        safetySettings: finalConfig.safetySettings,
      })

      // Convert image data to base64 if it's a Buffer
      const imageBase64 = Buffer.isBuffer(imageData) 
        ? imageData.toString('base64')
        : imageData

      const result = await model.generateContent([
        textPrompt,
        {
          inlineData: {
            data: imageBase64,
            mimeType,
          },
        },
      ])

      const response = await result.response

      return {
        content: response.text(),
        model: finalConfig.model,
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          candidatesTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0,
        } : undefined,
        finishReason: response.candidates?.[0]?.finishReason,
        safetyRatings: response.candidates?.[0]?.safetyRatings,
      }
    } catch (error) {
      console.error('Gemini multimodal error:', error)
      throw new Error(`Failed to generate multimodal content: ${error}`)
    }
  }

  /**
   * Count tokens in text
   */
  async countTokens(text: string, model: string = 'gemini-1.5-flash'): Promise<number> {
    if (!this.client) {
      throw new Error('Gemini client not initialized. Check GOOGLE_AI_API_KEY.')
    }

    try {
      const generativeModel = this.client.getGenerativeModel({ model })
      const result = await generativeModel.countTokens(text)
      return result.totalTokens
    } catch (error) {
      console.error('Token counting error:', error)
      // Fallback estimation: roughly 4 characters per token
      return Math.ceil(text.length / 4)
    }
  }

  /**
   * Get optimal model for task based on complexity and cost
   */
  getOptimalModel(
    taskComplexity: 'simple' | 'medium' | 'complex',
    prioritizeCost: boolean = true
  ): 'gemini-1.5-pro' | 'gemini-1.5-flash' {
    if (taskComplexity === 'complex') {
      return 'gemini-1.5-pro'
    }
    
    if (taskComplexity === 'simple' || prioritizeCost) {
      return 'gemini-1.5-flash'
    }
    
    return 'gemini-1.5-pro'
  }

  /**
   * Retry mechanism with exponential backoff
   */
  async generateContentWithRetry(
    prompt: string,
    config: Partial<GeminiConfig> = {},
    maxRetries: number = 3
  ): Promise<GeminiResponse> {
    let lastError: Error | null = null
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await this.generateContent(prompt, config)
      } catch (error) {
        lastError = error as Error
        
        // Don't retry on certain errors
        if (error instanceof Error && (
          error.message.includes('SAFETY') ||
          error.message.includes('INVALID_ARGUMENT')
        )) {
          throw error
        }
        
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000 + Math.random() * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError || new Error('Max retries exceeded')
  }

  /**
   * Check if client is available
   */
  isAvailable(): boolean {
    return this.client !== null
  }
}

// Export singleton instance
export const geminiClient = new GeminiClient()

// Utility functions for common agent tasks
export const agentPrompts = {
  tweetGeneration: (persona: any, topic?: string) => `
You are an AI social media agent with this persona:
- Personality: ${persona.personality}
- Tone: ${persona.tone}
- Writing Style: ${persona.writingStyle}
- Topics: ${persona.topics.join(', ')}

${topic ? `Create a tweet about: ${topic}` : 'Create an engaging tweet about one of your topics of interest.'}

Requirements:
- Keep under 280 characters
- Match the personality and tone described
- Be engaging and authentic
- Include relevant hashtags if appropriate

Tweet:`,

  contentImprovement: (content: string, persona: any, improvementType: string) => `
You are an AI social media agent. Improve this content to be more ${improvementType}:

Original content: "${content}"

Your persona:
- Personality: ${persona.personality}
- Tone: ${persona.tone}
- Writing Style: ${persona.writingStyle}

Improved content:`,

  memoryRetrieval: (query: string, memories: string[]) => `
Based on these relevant memories:
${memories.map((memory, i) => `${i + 1}. ${memory}`).join('\n')}

Answer this query: ${query}

Use the memories to provide context and inform your response. Be specific and reference relevant details from the memories.`,
}