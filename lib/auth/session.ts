import { cookies } from 'next/headers'
import { prisma } from '@/lib/database'
import { encryptToken, decryptToken } from './crypto'

export interface SessionData {
  userId: string
  email: string
  name?: string
  avatar?: string
}

export async function createSession(userData: SessionData): Promise<string> {
  // Create encrypted session token
  const sessionToken = await encryptToken({
    userId: userData.userId,
    email: userData.email,
    name: userData.name,
    avatar: userData.avatar,
    iat: Date.now(),
  })

  // Set secure cookie
  const cookieStore = await cookies()
  cookieStore.set('session', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24, // 24 hours
    path: '/',
  })

  return sessionToken
}

export async function getSession(): Promise<SessionData | null> {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value

    if (!sessionToken) {
      return null
    }

    const sessionData = await decryptToken(sessionToken)
    
    // Verify user still exists in database
    const user = await prisma.user.findUnique({
      where: { id: sessionData.userId },
      select: { id: true, email: true, name: true, avatar: true }
    })

    if (!user) {
      await destroySession()
      return null
    }

    return {
      userId: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
    }
  } catch (error) {
    console.error('Session validation error:', error)
    await destroySession()
    return null
  }
}

export async function destroySession(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete('session')
}

export async function requireAuth(): Promise<SessionData> {
  const session = await getSession()
  if (!session) {
    throw new Error('Authentication required')
  }
  return session
}