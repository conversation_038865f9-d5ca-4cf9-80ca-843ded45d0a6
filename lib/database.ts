import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ['query'],
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Database health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

// Database utilities
export async function getDatabaseStats() {
  try {
    const [userCount, agentCount, tweetCount] = await Promise.all([
      prisma.user.count(),
      prisma.agent.count(),
      prisma.scheduledTweet.count(),
    ])

    return {
      users: userCount,
      agents: agentCount,
      tweets: tweetCount,
    }
  } catch (error) {
    console.error('Failed to get database stats:', error)
    throw error
  }
}

// Graceful shutdown
export async function disconnectDatabase() {
  await prisma.$disconnect()
}