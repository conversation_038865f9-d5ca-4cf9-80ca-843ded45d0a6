# XTask Frontend Knowledge Base
## Complete Technical Reference for AI Agent

### 🎯 Project Overview

**XTask** is an AI-powered social media management platform built with Next.js 15, featuring:
- **Dark theme** with purple accents (#8B5CF6, #A855F7)
- **AI agent management** for automated content creation
- **Tweet composition and scheduling** with advanced features
- **Analytics dashboard** for performance tracking
- **Multi-provider OAuth** (Google, Twitter) authentication

### 🏗️ Architecture & Technology Stack

#### Core Technologies
```json
{
  "framework": "Next.js 15 (App Router)",
  "language": "TypeScript",
  "styling": "Tailwind CSS + shadcn/ui",
  "state": "Zustand + React Query",
  "forms": "React Hook Form + Zod",
  "animations": "Framer Motion",
  "icons": "Heroicons + Lucide React",
  "package_manager": "npm"
}
```

#### Project Structure
```
src/
├── app/                     # Next.js App Router
│   ├── (auth)/             # Authentication pages
│   ├── dashboard/          # Main application
│   └── api/                # API routes
├── components/             # React components
│   ├── ui/                 # Base components
│   ├── auth/               # Auth components
│   ├── agents/             # Agent components
│   ├── compose/            # Tweet composer
│   ├── schedule/           # Scheduling
│   ├── analytics/          # Analytics
│   └── layout/             # Layout components
├── hooks/                  # Custom React hooks
├── lib/                    # Utilities and configs
├── stores/                 # Zustand stores
└── types/                  # TypeScript definitions
```

### 🎨 Design System

#### Color Palette
```css
/* Primary Colors */
--primary-50: #faf5ff
--primary-500: #8b5cf6    /* Main purple */
--primary-600: #7c3aed
--primary-700: #6d28d9
--primary-900: #4c1d95

/* Dark Theme */
--dark-bg: #0a0a0a        /* Main background */
--dark-surface: #1a1a1a   /* Card background */
--dark-border: #2a2a2a    /* Border color */
--dark-text: #ffffff      /* Primary text */
--dark-text-muted: #a1a1aa /* Secondary text */

/* Status Colors */
--success: #10b981
--warning: #f59e0b
--error: #ef4444
--info: #3b82f6
```

#### Typography Scale
```css
--text-xs: 0.75rem     /* 12px */
--text-sm: 0.875rem    /* 14px */
--text-base: 1rem      /* 16px */
--text-lg: 1.125rem    /* 18px */
--text-xl: 1.25rem     /* 20px */
--text-2xl: 1.5rem     /* 24px */
--text-3xl: 1.875rem   /* 30px */
--text-4xl: 2.25rem    /* 36px */
```

#### Component Variants
```typescript
// Button variants
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
type ButtonSize = 'sm' | 'md' | 'lg' | 'xl'

// Badge variants
type BadgeVariant = 'default' | 'success' | 'warning' | 'error' | 'info'

// Card variants
type CardVariant = 'default' | 'elevated' | 'outlined' | 'filled'
```

### 🧩 Component Architecture

#### Base UI Components (src/components/ui/)
1. **Button** - All variants with loading states
2. **Input** - Text, email, password, textarea
3. **Card** - Container with elevation options
4. **Modal** - Overlay dialogs and drawers
5. **Badge** - Status indicators
6. **Avatar** - User profile images
7. **Dropdown** - Menu and select components
8. **Tabs** - Navigation tabs
9. **Toast** - Notification system
10. **Loading** - Spinners and skeletons
11. **Progress** - Progress indicators
12. **Theme Toggle** - Dark/light switcher

#### Feature Components
- **Authentication**: AuthProvider, ProtectedRoute, OAuth buttons
- **Agents**: AgentCard, AgentForm, AgentList, PersonaUploader
- **Compose**: TweetComposer, RichTextEditor, MediaUploader
- **Schedule**: ScheduleCalendar, TimePicker, BulkScheduler
- **Analytics**: Charts, MetricCards, DateRangePicker
- **Layout**: DashboardLayout, Sidebar, Navbar

### 🔐 Authentication System

#### Auth Flow
1. User clicks OAuth provider button
2. Redirect to provider (Google/Twitter)
3. Provider returns authorization code
4. Backend exchanges code for tokens
5. Backend creates secure session
6. Frontend receives session cookie
7. AuthProvider manages auth state

#### Key Components
```typescript
// AuthProvider context
interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (provider: 'google' | 'twitter') => void;
  logout: () => Promise<void>;
  refetch: () => Promise<void>;
}

// ProtectedRoute wrapper
<ProtectedRoute>
  <DashboardContent />
</ProtectedRoute>
```

### 🤖 Agent Management

#### Agent Data Structure
```typescript
interface Agent {
  id: string;
  name: string;
  description: string;
  personaData: PersonaSchema;
  aiProvider: 'openai' | 'google';
  aiModel: string;
  isActive: boolean;
  tweetsGenerated: number;
  engagementRate: number;
  maxDailyTweets: number;
}
```

#### Persona Schema
```typescript
interface PersonaSchema {
  name: string;
  role: string;
  personality_traits: string[];
  expertise_areas: string[];
  voice_and_tone: {
    formality_level: string;
    emoji_usage: string;
    hashtag_style: string;
  };
  content_strategy: {
    posting_frequency: object;
    content_types: object;
  };
}
```

### ✍️ Tweet Composition

#### Composer Features
- Rich text editing with formatting
- Character counting (280 limit)
- Media upload (images, videos, GIFs)
- Hashtag and mention autocomplete
- Thread composition
- AI content suggestions
- Draft auto-save

#### Media Upload
- UploadThing integration
- Drag & drop interface
- Image optimization
- Preview functionality
- Multiple file support

### 📅 Scheduling System

#### Scheduling Features
- Visual calendar interface
- Timezone-aware scheduling
- Optimal time suggestions
- Bulk scheduling operations
- Queue management
- Conflict detection

#### Time Management
```typescript
interface ScheduledTweet {
  id: string;
  content: string;
  scheduledAt: Date;
  status: 'pending' | 'published' | 'failed';
  agentId?: string;
  mediaUrls: string[];
}
```

### 📊 Analytics Dashboard

#### Metrics Tracked
- Tweet performance (likes, retweets, replies)
- Engagement rates
- Follower growth
- Reach and impressions
- Agent performance comparison
- Content type analysis

#### Chart Types
- Line charts for trends
- Bar charts for comparisons
- Pie charts for distributions
- Metric cards for key numbers
- Tables for detailed data

### 🔧 State Management

#### Zustand Stores
```typescript
// Auth Store
interface AuthStore {
  user: User | null;
  setUser: (user: User | null) => void;
  logout: () => void;
}

// Agent Store
interface AgentStore {
  agents: Agent[];
  selectedAgent: Agent | null;
  setAgents: (agents: Agent[]) => void;
  selectAgent: (agent: Agent) => void;
}
```

#### React Query Setup
```typescript
// Query keys
export const queryKeys = {
  agents: ['agents'],
  agent: (id: string) => ['agent', id],
  tweets: ['tweets'],
  analytics: (range: string) => ['analytics', range],
};
```

### 📱 Responsive Design

#### Breakpoints
```css
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
2xl: 1536px /* 2X large devices */
```

#### Mobile Patterns
- Collapsible sidebar navigation
- Touch-friendly button sizes
- Swipe gestures for cards
- Bottom sheet modals
- Responsive grid layouts

### 🎭 Animation Guidelines

#### Micro-interactions
```css
/* Button hover */
.button:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* Card hover */
.card:hover {
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.1);
  border-color: #8b5cf6;
}
```

#### Page Transitions
- Fade in/out for route changes
- Slide animations for modals
- Stagger animations for lists
- Loading skeleton states

### 🔍 SEO & Accessibility

#### SEO Requirements
- Proper meta tags
- Open Graph tags
- Structured data
- Semantic HTML
- Clean URLs

#### Accessibility Standards
- WCAG 2.1 AA compliance
- Keyboard navigation
- Screen reader support
- Color contrast ratios
- Focus management

### 🚀 Performance Optimization

#### Best Practices
- React.memo for expensive components
- Lazy loading for routes
- Image optimization with Next.js Image
- Virtual scrolling for large lists
- Code splitting by routes
- npmdle size monitoring

#### Loading States
- Skeleton screens
- Progressive loading
- Optimistic updates
- Error boundaries
- Retry mechanisms

This knowledge base provides comprehensive technical reference for building the XTask frontend with modern React patterns and best practices.
