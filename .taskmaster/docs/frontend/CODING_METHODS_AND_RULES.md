# XTask Frontend Coding Methods & Rules
## Development Standards and Best Practices

### 🎯 Core Development Principles

#### 1. TypeScript First
- **Always use TypeScript** for all components and utilities
- **Define interfaces** for all props and data structures
- **Use strict mode** with proper type checking
- **Avoid `any` type** - use proper typing or `unknown`
- **Export types** from dedicated type files

```typescript
// ✅ DO: Proper interface definition
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline';
  size: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

// ❌ DON'T: Using any or missing types
const Button = (props: any) => { ... }
```

#### 2. Component Structure Standards
- **Use functional components** with hooks
- **Implement forwardRef** for reusable UI components
- **Use proper naming conventions** (PascalCase for components)
- **Export components as named exports**
- **Include displayName** for debugging

```typescript
// ✅ DO: Proper component structure
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant, size, loading, children, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(buttonVariants({ variant, size }))}
        disabled={loading}
        {...props}
      >
        {loading && <LoadingSpinner />}
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";
export { Button };
```

### 🎨 Styling Guidelines

#### 1. Tailwind CSS Standards
- **Use Tailwind classes** for all styling
- **Implement design system** with consistent spacing
- **Use CSS variables** for theme colors
- **Create utility classes** for repeated patterns
- **Use `cn()` utility** for conditional classes

```typescript
// ✅ DO: Proper Tailwind usage
import { cn } from '@/lib/utils';

const className = cn(
  "flex items-center justify-center rounded-lg",
  "bg-dark-surface border border-dark-border",
  "hover:border-primary-500 transition-colors",
  isActive && "border-primary-500 bg-primary-500/10",
  disabled && "opacity-50 pointer-events-none"
);
```

#### 2. Component Variants
- **Use class-variance-authority (cva)** for component variants
- **Define all variants** in a single place
- **Use consistent naming** for variant options
- **Provide default variants**

```typescript
// ✅ DO: Proper variant definition
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-lg font-medium transition-colors",
  {
    variants: {
      variant: {
        primary: "bg-gradient-primary text-white hover:opacity-90",
        secondary: "bg-dark-surface text-white border border-dark-border",
        outline: "border border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white",
      },
      size: {
        sm: "h-8 px-3 text-xs",
        md: "h-10 px-4 text-sm",
        lg: "h-12 px-6 text-base",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  }
);
```

### 🔧 State Management Rules

#### 1. Zustand Store Patterns
- **Create focused stores** for specific domains
- **Use immer for complex state updates**
- **Implement proper TypeScript interfaces**
- **Avoid global state for local component state**

```typescript
// ✅ DO: Proper Zustand store
interface AgentStore {
  agents: Agent[];
  selectedAgent: Agent | null;
  loading: boolean;
  setAgents: (agents: Agent[]) => void;
  selectAgent: (agent: Agent) => void;
  updateAgent: (id: string, updates: Partial<Agent>) => void;
}

export const useAgentStore = create<AgentStore>((set) => ({
  agents: [],
  selectedAgent: null,
  loading: false,
  setAgents: (agents) => set({ agents }),
  selectAgent: (agent) => set({ selectedAgent: agent }),
  updateAgent: (id, updates) =>
    set((state) => ({
      agents: state.agents.map((agent) =>
        agent.id === id ? { ...agent, ...updates } : agent
      ),
    })),
}));
```

#### 2. React Query Patterns
- **Use consistent query keys**
- **Implement proper error handling**
- **Use optimistic updates** for better UX
- **Cache data appropriately**

```typescript
// ✅ DO: Proper React Query usage
export const useAgents = () => {
  return useQuery({
    queryKey: ['agents'],
    queryFn: async () => {
      const response = await fetch('/api/agents');
      if (!response.ok) throw new Error('Failed to fetch agents');
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateAgent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (agentData: CreateAgentData) => {
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(agentData),
      });
      if (!response.ok) throw new Error('Failed to create agent');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    },
  });
};
```

### 📝 Form Handling Standards

#### 1. React Hook Form + Zod
- **Use React Hook Form** for all forms
- **Implement Zod schemas** for validation
- **Use proper error handling**
- **Implement loading states**

```typescript
// ✅ DO: Proper form implementation
const agentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  aiProvider: z.enum(['openai', 'google']),
  maxDailyTweets: z.number().min(1).max(50),
});

type AgentFormData = z.infer<typeof agentSchema>;

export function AgentForm({ onSubmit }: { onSubmit: (data: AgentFormData) => void }) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<AgentFormData>({
    resolver: zodResolver(agentSchema),
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Input
          {...register('name')}
          placeholder="Agent name"
          error={errors.name?.message}
        />
      </div>
      <Button type="submit" loading={isSubmitting}>
        Create Agent
      </Button>
    </form>
  );
}
```

### 🔐 Authentication Patterns

#### 1. Auth Context Usage
- **Use AuthProvider** at app root
- **Implement ProtectedRoute** for auth-required pages
- **Handle loading states** properly
- **Provide clear error messages**

```typescript
// ✅ DO: Proper auth implementation
export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return null; // Will redirect
  }

  return <>{children}</>;
}
```

### 🎭 Animation Guidelines

#### 1. Framer Motion Standards
- **Use consistent animation durations**
- **Implement proper exit animations**
- **Use layout animations** for smooth transitions
- **Optimize performance** with transform properties

```typescript
// ✅ DO: Proper animation implementation
const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
};

export function AgentCard({ agent }: { agent: Agent }) {
  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      transition={{ duration: 0.3, ease: "easeOut" }}
      layout
      className="bg-dark-surface rounded-lg p-6"
    >
      {/* Card content */}
    </motion.div>
  );
}
```

### 🚀 Performance Rules

#### 1. Component Optimization
- **Use React.memo** for expensive components
- **Implement useMemo** for expensive calculations
- **Use useCallback** for stable function references
- **Avoid inline objects** in JSX props

```typescript
// ✅ DO: Proper optimization
const AgentCard = memo(({ agent, onEdit, onDelete }: AgentCardProps) => {
  const handleEdit = useCallback(() => {
    onEdit(agent.id);
  }, [agent.id, onEdit]);

  const metrics = useMemo(() => {
    return calculateAgentMetrics(agent);
  }, [agent]);

  return (
    <Card>
      <Button onClick={handleEdit}>Edit</Button>
    </Card>
  );
});
```

#### 2. Loading Strategies
- **Implement skeleton screens** for loading states
- **Use lazy loading** for route components
- **Optimize images** with Next.js Image
- **Implement virtual scrolling** for large lists

### 🔍 Error Handling Standards

#### 1. Error Boundaries
- **Implement error boundaries** for component trees
- **Provide fallback UI** for errors
- **Log errors** for debugging
- **Show user-friendly messages**

```typescript
// ✅ DO: Proper error boundary
export class ErrorBoundary extends Component<
  { children: ReactNode; fallback?: ReactNode },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <ErrorFallback />;
    }

    return this.props.children;
  }
}
```

### 📱 Responsive Design Rules

#### 1. Mobile-First Approach
- **Design for mobile first**
- **Use responsive breakpoints** consistently
- **Test on multiple devices**
- **Implement touch-friendly interactions**

```typescript
// ✅ DO: Proper responsive design
<div className="
  grid grid-cols-1 gap-4
  sm:grid-cols-2 sm:gap-6
  lg:grid-cols-3 lg:gap-8
  xl:grid-cols-4
">
  {agents.map((agent) => (
    <AgentCard key={agent.id} agent={agent} />
  ))}
</div>
```

### 🧪 Testing Guidelines

#### 1. Component Testing
- **Test component behavior** not implementation
- **Use React Testing Library**
- **Test user interactions**
- **Mock external dependencies**

```typescript
// ✅ DO: Proper component testing
test('should create agent when form is submitted', async () => {
  const mockOnSubmit = jest.fn();
  render(<AgentForm onSubmit={mockOnSubmit} />);
  
  await user.type(screen.getByPlaceholderText('Agent name'), 'Test Agent');
  await user.click(screen.getByRole('button', { name: /create agent/i }));
  
  expect(mockOnSubmit).toHaveBeenCalledWith({
    name: 'Test Agent',
    // ... other form data
  });
});
```

These coding standards ensure consistent, maintainable, and performant frontend code for the XTask platform.
