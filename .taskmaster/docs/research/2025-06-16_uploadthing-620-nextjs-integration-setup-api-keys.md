---
title: Research Session
query: "UploadThing 6.2.0 Next.js integration setup API keys configuration file upload components React hooks latest documentation"
date: 6/15/2025
time: 9:16:53 PM
timestamp: 2025-06-16T01:16:53.116Z
exchanges: 1
---

# Research Session

## Initial Query

**Question:** UploadThing 6.2.0 Next.js integration setup API keys configuration file upload components React hooks latest documentation

**Response:**

Integrating UploadThing 6.2.0 into your Next.js 15.3.3 project for media uploads, as outlined in `Task 13: UploadThing Integration for Media Uploads`, is a straightforward process that leverages Next.js API routes and React components. This integration will be crucial for `Task 14: Rich Text Tweet Composer UI` to enable media embedding.

### 1. Understanding UploadThing and Its Role in the Project

UploadThing is a file upload solution designed for modern web applications, offering a simple API for handling file uploads directly from your frontend to a secure cloud storage, with server-side validation and post-upload processing. For this project, it will serve as the backbone for image and video uploads within the tweet composer and potentially other areas requiring media.

**Relevance to Project Tasks:**

*   **Task 13 (UploadThing Integration for Media Uploads):** This is the primary task addressed. We will cover installation, API key configuration, backend endpoint setup, frontend component integration, and Prisma schema definition for `MediaFile`.
*   **Task 14 (Rich Text Tweet Composer UI):** The `UploadDropzone` or `UploadButton` components will be integrated directly into the composer UI, allowing users to attach media. The successful upload callback will provide the URL and key needed to display previews and store references in the database.
*   **Project Structure:** The integration will primarily involve creating files within `xtask-frontend/src/app/api/uploadthing` for the backend and `xtask-frontend/src/components/compose` or similar for frontend components.

### 2. Installation

First, install the necessary UploadThing packages using npm:

```bash
npm add uploadthing @uploadthing/react
```

This command installs `uploadthing` for your backend API routes and `@uploadthing/react` for the frontend React components and hooks.

### 3. API Key Configuration

UploadThing requires two API keys: `UPLOADTHING_SECRET` and `UPLOADTHING_APP_ID`. These should be stored securely as environment variables.

1.  **Obtain API Keys:** Sign up on the UploadThing website (uploadthing.com) and navigate to your dashboard to obtain your `UPLOADTHING_SECRET` and `UPLOADTHING_APP_ID`.
2.  **Update `.env.example`:** Add these variables to your `xtask-frontend/.env.example` file. If you have a `.env` file, update it accordingly.

    ```
    # xtask-frontend/.env.example

    # ... other existing variables ...

    UPLOADTHING_SECRET="sk_live_YOUR_SECRET_KEY"
    UPLOADTHING_APP_ID="YOUR_APP_ID"
    ```

    Remember to replace `YOUR_SECRET_KEY` and `YOUR_APP_ID` with your actual keys. For local development, you'll typically put these in `xtask-frontend/.env`.

### 4. Backend Endpoint Setup (`app/api/uploadthing/core.ts`)

UploadThing uses a file router concept, typically defined in `app/api/uploadthing/core.ts` for Next.js App Router projects. This file defines the types of files that can be uploaded, any middleware for authentication/authorization, and the logic to execute upon successful upload.

1.  **Create the directory and file:**
    ```bash
    mkdir -p xtask-frontend/src/app/api/uploadthing
    touch xtask-frontend/src/app/api/uploadthing/core.ts
    ```

2.  **Define the File Router (`xtask-frontend/src/app/api/uploadthing/core.ts`):**

    This file will define your `fileRouter` and export the `ourFileRouter` type. The `middleware` function is crucial for authenticating the user and attaching user-specific data to the upload context. Given your project's existing authentication (`auth.ts`, `src/lib/auth/passport.ts`), you should integrate with it here. For simplicity, let's assume you can retrieve a `userId` from the request.

    ```typescript
    import { createUploadthing, type FileRouter } from "uploadthing/next";
    import { UploadThingError } from "uploadthing/server";
    import { db } from "@/lib/db"; // Assuming you have a Prisma client instance at this path
    import { getUserIdFromRequest } from "@/lib/auth-utils"; // Placeholder for your auth logic

    const f = createUploadthing();

    // Helper function to get user ID from request (replace with your actual auth logic)
    // This is a placeholder. You'll need to adapt it to your specific auth implementation
    // e.g., using NextAuth.js session, Passport.js, or custom JWT verification.
    async function getUserAuthId(req: Request) {
      // Example: If using a custom JWT/session system, extract user ID from headers/cookies
      // For a real application, this would involve verifying a token or session.
      // For now, let's simulate a user ID.
      // In a real scenario, you might parse cookies or check a session.
      // const userId = await getUserIdFromRequest(req); // Your actual auth utility
      // if (!userId) throw new UploadThingError("Unauthorized");
      // return userId;

      // Placeholder: For demonstration, return a static user ID or null
      // You MUST replace this with actual authentication logic.
      console.log("Simulating user authentication for UploadThing middleware...");
      return "clx0f0b4o000008l100000000"; // Example user ID for testing
    }

    // FileRouter for your app, can contain multiple FileRoutes
    export const ourFileRouter = {
      // Define a route for image uploads
      imageUploader: f({ image: { maxFileSize: "4MB", maxFileCount: 5 } })
        // Set permissions and context (like who is uploading)
        .middleware(async ({ req }) => {
          // This code runs on your server before upload
          const userId = await getUserAuthId(req); // Replace with actual auth logic

          // If you throw, the user will not be able to upload
          if (!userId) throw new UploadThingError("Unauthorized");

          // Whatever is returned here is accessible in onUploadComplete as `metadata`
          return { userId: userId };
        })
        // Do whatever you want with the uploaded file
        .onUploadComplete(async ({ metadata, file }) => {
          // This code runs on your server after upload
          console.log("Upload complete for userId:", metadata.userId);
          console.log("file url", file.url);

          // Store file information in your database (MediaFile schema)
          try {
            await db.mediaFile.create({
              data: {
                userId: metadata.userId,
                url: file.url,
                key: file.key,
                name: file.name,
                type: file.type || "unknown", // UploadThing provides file.type
                size: file.size,
                // Add other fields from your MediaFile schema if necessary
              },
            });
            console.log("File saved to database:", file.url);
          } catch (error) {
            console.error("Failed to save file to database:", error);
            // You might want to delete the file from UploadThing if DB save fails
            // This requires additional logic or a separate webhook.
          }

          // !!! Whatever is returned here is sent to the client side.
          return { uploadedBy: metadata.userId, fileUrl: file.url, fileKey: file.key };
        }),

      // Define a route for video uploads (e.g., for larger files)
      videoUploader: f({ video: { maxFileSize: "256MB", maxFileCount: 1 } })
        .middleware(async ({ req }) => {
          const userId = await getUserAuthId(req);
          if (!userId) throw new UploadThingError("Unauthorized");
          return { userId: userId };
        })
        .onUploadComplete(async ({ metadata, file }) => {
          console.log("Video upload complete for userId:", metadata.userId);
          console.log("file url", file.url);

          try {
            await db.mediaFile.create({
              data: {
                userId: metadata.userId,
                url: file.url,
                key: file.key,
                name: file.name,
                type: file.type || "video",
                size: file.size,
              },
            });
            console.log("Video file saved to database:", file.url);
          } catch (error) {
            console.error("Failed to save video file to database:", error);
          }

          return { uploadedBy: metadata.userId, fileUrl: file.url, fileKey: file.key };
        }),

      // You can combine multiple types or create a generic one
      mediaUploader: f({
        image: { maxFileSize: "4MB", maxFileCount: 5 },
        video: { maxFileSize: "256MB", maxFileCount: 1 },
      })
        .middleware(async ({ req }) => {
          const userId = await getUserAuthId(req);
          if (!userId) throw new UploadThingError("Unauthorized");
          return { userId: userId };
        })
        .onUploadComplete(async ({ metadata, file }) => {
          console.log("Media upload complete for userId:", metadata.userId);
          console.log("file url", file.url);

          try {
            await db.mediaFile.create({
              data: {
                userId: metadata.userId,
                url: file.url,
                key: file.key,
                name: file.name,
                type: file.type || "unknown",
                size: file.size,
              },
            });
            console.log("Media file saved to database:", file.url);
          } catch (error) {
            console.error("Failed to save media file to database:", error);
          }

          return { uploadedBy: metadata.userId, fileUrl: file.url, fileKey: file.key };
        }),
    } satisfies FileRouter;

    export type OurFileRouter = typeof ourFileRouter;
    ```

    **Important Notes on `middleware`:**
    *   **Authentication:** The `getUserAuthId` function is a placeholder. You *must* replace this with your actual authentication logic. For example, if you're using NextAuth.js, you'd use `getServerSession` or similar. If you have a custom JWT setup, you'd decode and verify the token from the request headers. The project context mentions `auth.ts` and `src/lib/auth/passport.ts`, so you'll need to adapt this to retrieve the authenticated user's ID.
    *   **Error Handling:** If `middleware` throws an `UploadThingError`, the upload will be rejected.
    *   **`onUploadComplete`:** This function runs *after* the file has been successfully uploaded to UploadThing's storage. This is where you save the file metadata (URL, key, size, type) to your `MediaFile` database table.

### 5. Frontend Integration (React Components & Hooks)

UploadThing provides React components (`UploadDropzone`, `UploadButton`) and a hook (`useUploadThing`) to interact with your backend file router.

1.  **Create a Utility File for Components (`xtask-frontend/src/utils/uploadthing.ts`):**
    This file will generate the typed components and hooks from your `ourFileRouter`.

    ```bash
    mkdir -p xtask-frontend/src/utils
    touch xtask-frontend/src/utils/uploadthing.ts
    ```

    ```typescript
    // xtask-frontend/src/utils/uploadthing.ts
    import { generateComponents } from "@uploadthing/react";
    import { generateReactHelpers } from "@uploadthing/react/hooks";

    import type { OurFileRouter } from "@/app/api/uploadthing/core";

    export const { UploadButton, UploadDropzone, Uploader } =
      generateComponents<OurFileRouter>();

    export const { useUploadThing } = generateReactHelpers<OurFileRouter>();
    ```

2.  **Integrate into Frontend Components (e.g., `xtask-frontend/src/components/compose/TweetComposer.tsx`):**

    You can use `UploadDropzone` for a drag-and-drop area or `UploadButton` for a simple button. For `Task 14` (Rich Text Tweet Composer), you'll likely want to integrate these within the composer's UI.

    ```typescript
    // xtask-frontend/src/components/compose/TweetComposer.tsx (Example)
    "use client"; // This component needs to be a Client Component

    import React, { useState } from "react";
    import { UploadDropzone } from "@/utils/uploadthing"; // Import your generated components
    import { useToast } from "@/components/ui/sonner"; // Assuming you use shadcn/ui toast
    import { Input } from "@/components/ui/input"; // Example shadcn/ui component
    import { Button } from "@/components/ui/button"; // Example shadcn/ui component

    // Assuming you have a rich text editor like react-quill or Lexical
    // import ReactQuill from 'react-quill';
    // import 'react-quill/dist/quill.snow.css';

    interface UploadedMedia {
      url: string;
      key: string;
      name: string;
      type: string;
      size: number;
    }

    export function TweetComposer() {
      const [tweetContent, setTweetContent] = useState("");
      const [uploadedFiles, setUploadedFiles] = useState<UploadedMedia[]>([]);
      const { toast } = useToast(); // From shadcn/ui sonner.tsx

      const handleUploadComplete = (res: { fileUrl: string; fileKey: string; uploadedBy: string }[]) => {
        // Do something with the response, e.g., add to state for preview
        const newFiles: UploadedMedia[] = res.map(file => ({
          url: file.fileUrl,
          key: file.fileKey,
          name: file.fileUrl.split('/').pop() || 'unknown', // Basic name extraction
          type: file.fileUrl.includes('.mp4') ? 'video' : 'image', // Basic type detection
          size: 0, // Size is not returned by default, you might need to fetch it or pass it from backend
        }));
        setUploadedFiles(prev => [...prev, ...newFiles]);
        toast({
          title: "Upload Successful!",
          description: `${newFiles.length} file(s) uploaded.`,
        });
        // Here you would also insert the media into your rich text editor
        // e.g., editor.insertEmbed(range.index, 'image', file.fileUrl);
      };

      const handleUploadError = (error: Error) => {
        // Do something with the error.
        toast({
          title: "Upload Failed!",
          description: error.message,
          variant: "destructive",
        });
        console.error("Upload Error:", error);
      };

      const handleSubmitTweet = () => {
        console.log("Tweet Content:", tweetContent);
        console.log("Attached Media:", uploadedFiles);
        // Logic to submit tweet to your backend
        // This would involve sending tweetContent and uploadedFiles data
      };

      return (
        <div className="p-4 border rounded-lg shadow-sm bg-card text-card-foreground">
          <h2 className="text-xl font-semibold mb-4">Compose New Tweet</h2>
          {/* Rich Text Editor Placeholder */}
          {/* <ReactQuill
            value={tweetContent}
            onChange={setTweetContent}
            placeholder="What's happening?"
            className="mb-4"
          /> */}
          <Input
            placeholder="What's happening?"
            value={tweetContent}
            onChange={(e) => setTweetContent(e.target.value)}
            className="mb-4"
          />

          <div className="mb-4">
            <UploadDropzone
              endpoint="mediaUploader" // Use the endpoint defined in core.ts
              onClientUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              className="ut-label:text-primary-500 ut-button:bg-primary-500 ut-button:text-white ut-button:hover:bg-primary-600 ut-allowed-content:text-muted-foreground"
            />
          </div>

          {uploadedFiles.length > 0 && (
            <div className="mb-4">
              <h3 className="text-md font-medium mb-2">Attached Media:</h3>
              <div className="grid grid-cols-2 gap-2">
                {uploadedFiles.map((file, index) => (
                  <div key={index} className="relative group">
                    {file.type === 'image' ? (
                      <img src={file.url} alt={file.name} className="w-full h-auto rounded-md object-cover" />
                    ) : (
                      <video src={file.url} controls className="w-full h-auto rounded-md object-cover" />
                    )}
                    <p className="text-sm text-muted-foreground mt-1 truncate">{file.name}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <Button onClick={handleSubmitTweet} className="w-full bg-primary-500 hover:bg-primary-600 text-white">
            Tweet
          </Button>
        </div>
      );
    }
    ```

    **Using `useUploadThing` Hook for Custom UI:**
    If you need more control over the UI (e.g., integrating with a custom button or drag area), you can use the `useUploadThing` hook:

    ```typescript
    // Example using useUploadThing hook
    import { useUploadThing } from "@/utils/uploadthing";
    import { Button } from "@/components/ui/button";
    import { Input } from "@/components/ui/input";
    import { useState } from "react";

    export function CustomMediaUploader() {
      const [files, setFiles] = useState<File[]>([]);
      const { startUpload, isUploading } = useUploadThing("mediaUploader", {
        onClientUploadComplete: (res) => {
          console.log("Files uploaded!", res);
          alert("Upload Complete!");
          // Handle successful upload, e.g., update state, show toast
        },
        onUploadError: (error: Error) => {
          console.error("Error uploading files:", error);
          alert(`ERROR! ${error.message}`);
          // Handle upload error
        },
        onUploadBegin: (fileName) => {
          console.log("Upload has begun for:", fileName);
        },
      });

      return (
        <div>
          <Input
            type="file"
            multiple
            onChange={(e) => {
              if (e.target.files) {
                setFiles(Array.from(e.target.files));
              }
            }}
          />
          <Button
            onClick={() => startUpload(files)}
            disabled={isUploading || files.length === 0}
            className="mt-2"
          >
            {isUploading ? "Uploading..." : "Upload Files"}
          </Button>
        </div>
      );
    }
    ```

### 6. Prisma `MediaFile` Schema Definition

As per `Task 13`, you need to define the `MediaFile` model in your `prisma/schema.prisma` file.

1.  **Update `xtask-frontend/prisma/schema.prisma`:**

    ```prisma
    // xtask-frontend/prisma/schema.prisma

    // ... existing models like User, etc. ...

    model MediaFile {
      id        String   @id @default(cuid())
      userId    String   // Link to the User who uploaded the file
      url       String   @unique // The URL of the uploaded file
      key       String   @unique // The unique key/ID from UploadThing
      name      String   // Original file name
      type      String   // Mime type or 'image', 'video', etc.
      size      Int      // File size in bytes
      createdAt DateTime @default(now())
      updatedAt DateTime @updatedAt

      // Define relation to User model if you have one
      user      User     @relation(fields: [userId], references: [id])

      @@index([userId]) // Index for faster lookups by user
    }
    ```

    **Note:** Ensure you have a `User` model defined in your `schema.prisma` for the `userId` relation to work correctly. If your `User` model uses a different ID type (e.g., `Int` or `uuid`), adjust `userId` and `references` accordingly.

2.  **Run Prisma Migrations:** After updating your `schema.prisma`, you need to generate and apply a new migration to update your database schema.

    ```bash
    npm prisma migrate dev --name add_media_file_model
    ```

    This command will:
    *   Create a new migration file in `prisma/migrations`.
    *   Apply the changes to your database.
    *   Generate/update your Prisma client.

### 7. Database Interaction on Upload Complete

The `onUploadComplete` callback in `xtask-frontend/src/app/api/uploadthing/core.ts` is where the database interaction happens. The example code provided in section 4 already includes the `db.mediaFile.create` call. This ensures that upon successful upload to UploadThing, the file's metadata (URL, key, name, type, size, and the `userId` from the middleware) is persisted in your PostgreSQL database via Prisma.

### 8. Security Considerations

*   **API Keys:** Never expose `UPLOADTHING_SECRET` or `UPLOADTHING_APP_ID` in your frontend code. They are server-side environment variables.
*   **Authentication Middleware:** The `middleware` function in `core.ts` is critical for security. It ensures that only authenticated and authorized users can upload files. The placeholder `getUserAuthId` *must* be replaced with robust authentication logic that verifies the user's identity based on your project's authentication system (e.g., session, JWT). Without proper authentication, anyone could upload files.
*   **File Size and Count Limits:** Use `maxFileSize` and `maxFileCount` in your `f()` definitions to prevent abuse and manage storage costs.
*   **File Types:** While UploadThing handles basic file type validation, consider additional server-side validation if specific content types are critical.

### 9. Latest Documentation Reference

For the most up-to-date and detailed information, always refer to the official UploadThing documentation:

*   **Main Documentation:** [https://docs.uploadthing.com/](https://docs.uploadthing.com/)
*   **Next.js App Router Integration:** [https://docs.uploadthing.com/nextjs/appdir](https://docs.uploadthing.com/nextjs/appdir)
*   **React Components & Hooks:** [https://docs.uploadthing.com/api-reference/react](https://docs.uploadthing.com/api-reference/react)

### 10. Actionable Steps for the Project

To complete `Task 13` and enable `Task 14`:

1.  **Install Dependencies:** Run `npm add uploadthing @uploadthing/react` in `xtask-frontend/`.
2.  **Configure API Keys:** Add `UPLOADTHING_SECRET` and `UPLOADTHING_APP_ID` to `xtask-frontend/.env.example` and your local `.env` file.
3.  **Define File Router:** Create `xtask-frontend/src/app/api/uploadthing/core.ts` and populate it with the `ourFileRouter` definition, ensuring you implement the `middleware` with your actual user authentication logic.
4.  **Create Utility File:** Create `xtask-frontend/src/utils/uploadthing.ts` to generate the typed components and hooks.
5.  **Update Prisma Schema:** Add the `MediaFile` model to `xtask-frontend/prisma/schema.prisma`.
6.  **Run Migrations:** Execute `npm prisma migrate dev --name add_media_file_model` to update your database.
7.  **Integrate Frontend Components:** In `xtask-frontend/src/components/compose/TweetComposer.tsx` (or a similar component), integrate `UploadDropzone` or `UploadButton`. Handle the `onClientUploadComplete` callback to update the UI (e.g., display previews) and pass the file URLs/keys to the rich text editor for embedding.
8.  **Test:** Thoroughly test the upload process, including successful uploads, error handling, and ensuring the `MediaFile` records are correctly stored in the database.

This comprehensive setup will provide the robust media upload capabilities required for your project.


---

*Generated by Task Master Research Command*  
*Timestamp: 2025-06-16T01:16:53.116Z*
