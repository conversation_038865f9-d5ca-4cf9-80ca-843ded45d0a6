# XTask API Specification
## Complete REST API Documentation

### 🔐 Authentication

#### Base URL
```
Development: http://localhost:3000/api
Production: https://xtask.com/api
```

#### Authentication Methods
- **Session-based**: Express sessions with Redis store
- **JWT Tokens**: For API access (optional)
- **OAuth**: Google and Twitter/X integration

#### Headers
```http
Content-Type: application/json
Authorization: Bearer <jwt-token> (if using JWT)
Cookie: session-id=<session-id> (for session auth)
```

### 🔑 Authentication Endpoints

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "displayName": "<PERSON> Doe"
}
```

**Response (201):**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "displayName": "<PERSON>",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

#### POST /api/auth/login
Authenticate user with email/password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response (200):**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "displayName": "John Doe"
  },
  "token": "jwt_token_here" // if using JWT
}
```

#### GET /api/auth/google
Initiate Google OAuth flow.

**Response:** Redirects to Google OAuth consent screen.

**Implementation Details:**
```typescript
// Redirect URL construction
const googleAuthUrl = `https://accounts.google.com/oauth/authorize?` +
  `client_id=${GOOGLE_CLIENT_ID}&` +
  `redirect_uri=${GOOGLE_CALLBACK_URL}&` +
  `response_type=code&` +
  `scope=openid email profile&` +
  `access_type=offline&` +
  `prompt=consent`;
```

#### GET /api/auth/google/callback
Handle Google OAuth callback.

**Query Parameters:**
- `code`: Authorization code from Google
- `state`: CSRF protection state (optional)

**Process Flow:**
1. Exchange authorization code for access token
2. Verify ID token with Google
3. Extract user profile information
4. Upsert user in database
5. Create secure session token
6. Set httpOnly cookie
7. Redirect to dashboard

**Success Response:** Redirects to `/dashboard`
**Error Response:** Redirects to `/?error=auth_failed`

#### GET /api/auth/twitter
Initiate Twitter OAuth 2.0 flow.

**Response:** Redirects to Twitter OAuth consent screen.

**Implementation Details:**
```typescript
// Twitter OAuth URL with PKCE
const twitterAuthUrl = "https://twitter.com/i/oauth2/authorize?" +
  `client_id=${TWITTER_CLIENT_ID}&` +
  `redirect_uri=${TWITTER_CALLBACK_URL}&` +
  `response_type=code&` +
  `scope=users.read tweet.read tweet.write&` +
  `code_challenge=${CODE_CHALLENGE}&` +
  `code_challenge_method=S256&` +
  `state=${CSRF_STATE}`;
```

#### GET /api/auth/twitter/callback
Handle Twitter OAuth callback.

**Query Parameters:**
- `code`: Authorization code from Twitter
- `state`: CSRF protection state

**Process Flow:**
1. Exchange authorization code for access token using PKCE
2. Fetch user profile from Twitter API v2
3. Upsert user and Twitter account in database
4. Encrypt and store Twitter access token
5. Create secure session token
6. Set httpOnly cookie
7. Redirect to dashboard

**Success Response:** Redirects to `/dashboard`
**Error Response:** Redirects to `/?error=auth_failed`

#### POST /api/auth/logout
Logout current user.

**Response (200):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### GET /api/auth/me
Get current user information.

**Response (200):**
```json
{
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "profilePicture": "https://example.com/avatar.jpg",
    "connectedAccounts": [
      {
        "provider": "twitter",
        "username": "@johndoe",
        "isActive": true
      }
    ],
    "preferences": {
      "timezone": "UTC",
      "theme": "dark",
      "defaultProvider": "openai"
    }
  }
}
```

### 🤖 Agent Management Endpoints

#### GET /api/agents
List user's AI agents.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status (active, inactive)

**Response (200):**
```json
{
  "agents": [
    {
      "id": "agent_123",
      "name": "TechGuru AI",
      "description": "Tech thought leader persona",
      "status": "active",
      "aiProvider": "openai",
      "aiModel": "gpt-4",
      "tweetsGenerated": 45,
      "engagementRate": 3.2,
      "lastActivity": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-10T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "totalPages": 1
  }
}
```

#### POST /api/agents
Create a new AI agent.

**Request Body:**
```json
{
  "name": "TechGuru AI",
  "description": "Tech thought leader persona",
  "aiProvider": "openai",
  "aiModel": "gpt-4",
  "temperature": 0.7,
  "maxTokens": 280,
  "autoSchedule": true,
  "maxDailyTweets": 5,
  "minHoursBetweenTweets": 2
}
```

**Response (201):**
```json
{
  "success": true,
  "agent": {
    "id": "agent_123",
    "name": "TechGuru AI",
    "description": "Tech thought leader persona",
    "status": "inactive",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

#### GET /api/agents/:id
Get specific agent details.

**Response (200):**
```json
{
  "agent": {
    "id": "agent_123",
    "name": "TechGuru AI",
    "description": "Tech thought leader persona",
    "personaData": {
      "role": "Tech thought leader",
      "personality_traits": ["analytical", "helpful"],
      "expertise_areas": ["JavaScript", "AI/ML"]
    },
    "aiProvider": "openai",
    "aiModel": "gpt-4",
    "settings": {
      "temperature": 0.7,
      "maxTokens": 280,
      "autoSchedule": true,
      "maxDailyTweets": 5
    },
    "analytics": {
      "tweetsGenerated": 45,
      "engagementRate": 3.2,
      "avgLikes": 12.5,
      "avgRetweets": 3.2
    }
  }
}
```

#### PUT /api/agents/:id
Update agent configuration.

**Request Body:** Same as POST /api/agents

#### DELETE /api/agents/:id
Delete an agent.

**Response (200):**
```json
{
  "success": true,
  "message": "Agent deleted successfully"
}
```

#### POST /api/agents/:id/persona
Upload persona file for agent.

**Request:** Multipart form data with file upload.

**Response (200):**
```json
{
  "success": true,
  "message": "Persona uploaded successfully",
  "personaData": {
    "name": "TechGuru_AI",
    "version": "1.0",
    "role": "Tech thought leader"
  }
}
```

#### POST /api/agents/:id/generate
Generate content with specific agent.

**Request Body:**
```json
{
  "prompt": "Write a tweet about the latest JavaScript features",
  "context": "Recent ES2024 updates",
  "includeMedia": false
}
```

**Response (200):**
```json
{
  "success": true,
  "generatedContent": {
    "content": "🚀 ES2024 brings some incredible features! The new Array.groupBy() method is a game-changer for data manipulation. No more verbose reduce() calls! #JavaScript #WebDev",
    "metadata": {
      "characterCount": 156,
      "hashtags": ["#JavaScript", "#WebDev"],
      "mentions": [],
      "estimatedEngagement": "medium"
    }
  }
}
```

### ✍️ Tweet Management Endpoints

#### POST /api/tweets/compose
Create a new tweet (human-composed).

**Request Body:**
```json
{
  "content": "Just shipped a new feature! 🚀",
  "mediaUrls": ["https://example.com/image.jpg"],
  "twitterAccountId": "account_123",
  "threadTweets": ["Follow-up tweet content"]
}
```

#### POST /api/tweets/schedule
Schedule a tweet for future publishing.

**Request Body:**
```json
{
  "content": "Scheduled tweet content",
  "scheduledAt": "2024-01-16T14:30:00Z",
  "twitterAccountId": "account_123",
  "agentId": "agent_123", // optional
  "mediaUrls": []
}
```

**Response (201):**
```json
{
  "success": true,
  "scheduledTweet": {
    "id": "tweet_123",
    "content": "Scheduled tweet content",
    "scheduledAt": "2024-01-16T14:30:00Z",
    "status": "pending",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

#### GET /api/tweets/scheduled
List scheduled tweets.

**Query Parameters:**
- `status`: Filter by status (pending, published, failed)
- `agentId`: Filter by agent
- `page`, `limit`: Pagination

**Response (200):**
```json
{
  "scheduledTweets": [
    {
      "id": "tweet_123",
      "content": "Scheduled tweet content",
      "scheduledAt": "2024-01-16T14:30:00Z",
      "status": "pending",
      "agent": {
        "id": "agent_123",
        "name": "TechGuru AI"
      },
      "twitterAccount": {
        "id": "account_123",
        "username": "@johndoe"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "total": 15
  }
}
```

#### PUT /api/tweets/scheduled/:id
Update scheduled tweet.

#### DELETE /api/tweets/scheduled/:id
Cancel scheduled tweet.

#### POST /api/tweets/publish
Publish tweet immediately.

**Request Body:**
```json
{
  "content": "Tweet to publish now",
  "twitterAccountId": "account_123",
  "mediaUrls": []
}
```

### 📊 Analytics Endpoints

#### GET /api/analytics/overview
Get dashboard analytics overview.

**Query Parameters:**
- `dateRange`: "7d", "30d", "90d"
- `agentId`: Filter by specific agent

**Response (200):**
```json
{
  "overview": {
    "totalTweets": 156,
    "totalEngagement": 2340,
    "followerGrowth": 45,
    "engagementRate": 3.2,
    "topPerformingTweet": {
      "id": "tweet_456",
      "content": "Top performing tweet...",
      "likes": 89,
      "retweets": 23
    }
  },
  "trends": {
    "tweetsOverTime": [
      {"date": "2024-01-10", "count": 5},
      {"date": "2024-01-11", "count": 8}
    ],
    "engagementOverTime": [
      {"date": "2024-01-10", "engagement": 45},
      {"date": "2024-01-11", "engagement": 67}
    ]
  }
}
```

### 📁 Media Management Endpoints

#### POST /api/media/upload
Upload media files for tweets.

**Request:** Multipart form data

**Response (200):**
```json
{
  "success": true,
  "files": [
    {
      "id": "media_123",
      "url": "https://uploadthing.com/file123.jpg",
      "fileName": "screenshot.jpg",
      "fileType": "image/jpeg",
      "fileSize": 245760
    }
  ]
}
```

#### GET /api/media
List user's uploaded media.

#### DELETE /api/media/:id
Delete media file.

### ⚙️ Settings Endpoints

#### GET /api/settings/profile
Get user profile settings.

#### PUT /api/settings/profile
Update user profile.

#### GET /api/settings/ai-config
Get AI provider configurations.

#### PUT /api/settings/ai-config
Update AI provider settings.

**Request Body:**
```json
{
  "openai": {
    "apiKey": "encrypted_key",
    "baseUrl": "https://api.openai.com/v1",
    "defaultModel": "gpt-4"
  },
  "google": {
    "apiKey": "encrypted_key",
    "defaultModel": "gemini-pro"
  },
  "defaultProvider": "openai"
}
```

### 🔗 Connected Accounts Endpoints

#### GET /api/accounts/connected
List connected social media accounts.

#### DELETE /api/accounts/:id
Disconnect social media account.

#### PUT /api/accounts/:id/primary
Set account as primary.

### 📝 Error Responses

#### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

#### Common Error Codes
- `VALIDATION_ERROR` (400): Invalid input data
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `RATE_LIMITED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error

### 🔄 Rate Limiting

#### Limits
- Authentication: 10 requests/minute
- Agent operations: 100 requests/hour
- Tweet publishing: 50 requests/hour
- Analytics: 200 requests/hour

#### Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

This API specification provides comprehensive documentation for all XTask platform endpoints.
