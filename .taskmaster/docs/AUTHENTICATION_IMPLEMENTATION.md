# XTask Authentication Implementation Guide
## Complete OAuth 2.0 Integration with Twitter & Google

### 🔐 Authentication Architecture Overview

Based on the comprehensive analysis of modern OAuth 2.0 implementations, our XTask platform will implement a hybrid authentication system that combines the best practices from both Twitter OAuth 2.0 and Google OAuth 2.0 flows.

#### Key Authentication Flow
1. **Frontend Initiation**: User clicks OAuth provider button
2. **Provider Authorization**: Redirect to OAuth provider (Google/Twitter)
3. **Authorization Code**: Provider returns authorization code
4. **Token Exchange**: Backend exchanges code for access token
5. **User Verification**: Backend verifies user identity with provider
6. **Session Creation**: Backend creates secure session with JWT
7. **Client Authentication**: <PERSON><PERSON> receives session token for API calls

### 🐦 Twitter OAuth 2.0 Implementation

#### Twitter Developer Setup
```bash
# Twitter App Configuration
App Type: Web Application
Callback URI: http://www.localhost:3001/api/auth/twitter/callback
Website URL: http://www.localhost:3000
Permissions: Read (minimum for user profile)
```

#### Environment Variables
```env
# Twitter OAuth Configuration
TWITTER_CLIENT_ID="your-twitter-client-id"
TWITTER_CLIENT_SECRET="your-twitter-client-secret"
TWITTER_CALLBACK_URL="http://www.localhost:3001/api/auth/twitter/callback"

# PKCE Configuration (for security)
TWITTER_CODE_CHALLENGE="y_SfRG4BmOES02uqWeIkIgLQAlTBggyf_G7uKT51ku8"
TWITTER_CODE_VERIFIER="8KxxO-RPl0bLSxX5AWwgdiFbMnry_VOKzFeIlVA7NoA"
```

#### Frontend Twitter OAuth Component
```typescript
// src/components/auth/TwitterOAuthButton.tsx
import { TwitterIcon } from '@heroicons/react/24/outline';

const TWITTER_CLIENT_ID = process.env.NEXT_PUBLIC_TWITTER_CLIENT_ID;

function getTwitterOAuthUrl() {
  const rootUrl = "https://twitter.com/i/oauth2/authorize";
  const options = {
    redirect_uri: process.env.NEXT_PUBLIC_TWITTER_CALLBACK_URL,
    client_id: TWITTER_CLIENT_ID,
    state: crypto.randomUUID(), // CSRF protection
    response_type: "code",
    code_challenge: process.env.NEXT_PUBLIC_TWITTER_CODE_CHALLENGE,
    code_challenge_method: "S256",
    scope: [
      "users.read", 
      "tweet.read", 
      "tweet.write", 
      "follows.read", 
      "follows.write"
    ].join(" "),
  };
  const qs = new URLSearchParams(options).toString();
  return `${rootUrl}?${qs}`;
}

export function TwitterOAuthButton() {
  return (
    <a 
      href={getTwitterOAuthUrl()}
      className="flex items-center justify-center gap-3 bg-black hover:bg-gray-800 text-white px-6 py-3 rounded-lg transition-colors"
    >
      <TwitterIcon className="w-5 h-5" />
      <span>Continue with Twitter</span>
    </a>
  );
}
```

#### Backend Twitter OAuth Handler
```typescript
// src/server/routes/auth.ts
import { TwitterApi } from 'twitter-api-v2';
import axios from 'axios';

const TWITTER_OAUTH_TOKEN_URL = "https://api.twitter.com/2/oauth2/token";
const TWITTER_CLIENT_ID = process.env.TWITTER_CLIENT_ID!;
const TWITTER_CLIENT_SECRET = process.env.TWITTER_CLIENT_SECRET!;

// Base64 encode client credentials
const BasicAuthToken = Buffer.from(
  `${TWITTER_CLIENT_ID}:${TWITTER_CLIENT_SECRET}`, 
  "utf8"
).toString("base64");

interface TwitterTokenResponse {
  token_type: "bearer";
  expires_in: number;
  access_token: string;
  scope: string;
  refresh_token?: string;
}

interface TwitterUser {
  id: string;
  name: string;
  username: string;
  profile_image_url?: string;
  email?: string;
}

// Step 1: Exchange authorization code for access token
export async function getTwitterOAuthToken(code: string): Promise<TwitterTokenResponse | null> {
  try {
    const params = {
      client_id: TWITTER_CLIENT_ID,
      code_verifier: process.env.TWITTER_CODE_VERIFIER!,
      redirect_uri: process.env.TWITTER_CALLBACK_URL!,
      grant_type: "authorization_code",
      code,
    };

    const response = await axios.post<TwitterTokenResponse>(
      TWITTER_OAUTH_TOKEN_URL,
      new URLSearchParams(params).toString(),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": `Basic ${BasicAuthToken}`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Twitter OAuth token error:', error);
    return null;
  }
}

// Step 2: Get Twitter user profile
export async function getTwitterUser(accessToken: string): Promise<TwitterUser | null> {
  try {
    const response = await axios.get<{ data: TwitterUser }>(
      "https://api.twitter.com/2/users/me?user.fields=profile_image_url",
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`,
        },
      }
    );

    return response.data.data;
  } catch (error) {
    console.error('Twitter user fetch error:', error);
    return null;
  }
}

// Main Twitter OAuth callback handler
export async function handleTwitterCallback(req: Request, res: Response) {
  const { code, state } = req.query;

  if (!code) {
    return res.redirect(`${process.env.CLIENT_URL}?error=no_code`);
  }

  try {
    // Step 1: Get access token
    const tokenData = await getTwitterOAuthToken(code as string);
    if (!tokenData) {
      return res.redirect(`${process.env.CLIENT_URL}?error=token_failed`);
    }

    // Step 2: Get user profile
    const twitterUser = await getTwitterUser(tokenData.access_token);
    if (!twitterUser) {
      return res.redirect(`${process.env.CLIENT_URL}?error=user_failed`);
    }

    // Step 3: Upsert user in database
    const user = await prisma.user.upsert({
      where: { twitterId: twitterUser.id },
      update: {
        name: twitterUser.name,
        username: twitterUser.username,
        profilePicture: twitterUser.profile_image_url,
      },
      create: {
        twitterId: twitterUser.id,
        name: twitterUser.name,
        username: twitterUser.username,
        profilePicture: twitterUser.profile_image_url,
        email: twitterUser.email,
      },
    });

    // Step 4: Create Twitter account record
    await prisma.twitterAccount.upsert({
      where: { twitterUserId: twitterUser.id },
      update: {
        username: twitterUser.username,
        displayName: twitterUser.name,
        profilePicture: twitterUser.profile_image_url,
        accessToken: encrypt(tokenData.access_token),
        refreshToken: tokenData.refresh_token ? encrypt(tokenData.refresh_token) : null,
      },
      create: {
        twitterUserId: twitterUser.id,
        username: twitterUser.username,
        displayName: twitterUser.name,
        profilePicture: twitterUser.profile_image_url,
        accessToken: encrypt(tokenData.access_token),
        refreshToken: tokenData.refresh_token ? encrypt(tokenData.refresh_token) : null,
        userId: user.id,
      },
    });

    // Step 5: Create session
    const sessionToken = jwt.sign(
      { 
        userId: user.id, 
        provider: 'twitter',
        twitterId: twitterUser.id 
      },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    // Set secure cookie
    res.cookie('session-token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Redirect to dashboard
    return res.redirect(`${process.env.CLIENT_URL}/dashboard`);

  } catch (error) {
    console.error('Twitter OAuth error:', error);
    return res.redirect(`${process.env.CLIENT_URL}?error=auth_failed`);
  }
}
```

### 🔍 Google OAuth 2.0 Implementation

#### Google Cloud Console Setup
```bash
# Google OAuth Configuration
Application Type: Web Application
Authorized JavaScript Origins: http://localhost:3000
Authorized Redirect URIs: http://localhost:3000/api/auth/google/callback
```

#### Environment Variables
```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_CALLBACK_URL="http://localhost:3000/api/auth/google/callback"
```

#### Frontend Google OAuth Component
```typescript
// src/components/auth/GoogleOAuthButton.tsx
import { GoogleIcon } from '@/components/icons/GoogleIcon';

export function GoogleOAuthButton() {
  const handleGoogleLogin = () => {
    const googleAuthUrl = `https://accounts.google.com/oauth/authorize?` +
      `client_id=${process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL}&` +
      `response_type=code&` +
      `scope=openid email profile&` +
      `access_type=offline&` +
      `prompt=consent`;
    
    window.location.href = googleAuthUrl;
  };

  return (
    <button
      onClick={handleGoogleLogin}
      className="flex items-center justify-center gap-3 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 px-6 py-3 rounded-lg transition-colors"
    >
      <GoogleIcon className="w-5 h-5" />
      <span>Continue with Google</span>
    </button>
  );
}
```

#### Backend Google OAuth Handler
```typescript
// src/server/routes/auth.ts
import { OAuth2Client } from 'google-auth-library';

const googleClient = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_CALLBACK_URL
);

interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  email_verified: boolean;
}

export async function handleGoogleCallback(req: Request, res: Response) {
  const { code } = req.query;

  if (!code) {
    return res.redirect(`${process.env.CLIENT_URL}?error=no_code`);
  }

  try {
    // Step 1: Exchange code for tokens
    const { tokens } = await googleClient.getToken(code as string);
    googleClient.setCredentials(tokens);

    // Step 2: Verify ID token and get user info
    const ticket = await googleClient.verifyIdToken({
      idToken: tokens.id_token!,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload) {
      return res.redirect(`${process.env.CLIENT_URL}?error=invalid_token`);
    }

    const googleUser: GoogleUser = {
      id: payload.sub,
      email: payload.email!,
      name: payload.name!,
      picture: payload.picture,
      email_verified: payload.email_verified || false,
    };

    // Step 3: Upsert user in database
    const user = await prisma.user.upsert({
      where: { googleId: googleUser.id },
      update: {
        name: googleUser.name,
        email: googleUser.email,
        profilePicture: googleUser.picture,
      },
      create: {
        googleId: googleUser.id,
        name: googleUser.name,
        email: googleUser.email,
        profilePicture: googleUser.picture,
      },
    });

    // Step 4: Create session
    const sessionToken = jwt.sign(
      { 
        userId: user.id, 
        provider: 'google',
        googleId: googleUser.id 
      },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    // Set secure cookie
    res.cookie('session-token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Redirect to dashboard
    return res.redirect(`${process.env.CLIENT_URL}/dashboard`);

  } catch (error) {
    console.error('Google OAuth error:', error);
    return res.redirect(`${process.env.CLIENT_URL}?error=auth_failed`);
  }
}
```

### 🔒 Session Management & Security

#### JWT Token Structure
```typescript
interface SessionPayload {
  userId: string;
  provider: 'google' | 'twitter';
  googleId?: string;
  twitterId?: string;
  iat: number;
  exp: number;
}
```

#### Authentication Middleware
```typescript
// src/server/middleware/auth.ts
export async function authenticateUser(req: Request, res: Response, next: NextFunction) {
  try {
    const token = req.cookies['session-token'];
    
    if (!token) {
      return res.status(401).json({ error: 'No authentication token' });
    }

    const payload = jwt.verify(token, process.env.JWT_SECRET!) as SessionPayload;
    
    // Verify user still exists
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        twitterAccounts: true,
      },
    });

    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid authentication token' });
  }
}
```

#### Frontend Session Hook
```typescript
// src/hooks/useAuth.ts
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCurrentUser();
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      setUser(null);
      window.location.href = '/';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return { user, loading, logout, refetch: fetchCurrentUser };
}
```

### 🔐 Enhanced Security Features

#### Token Encryption & Storage
```typescript
// src/lib/encryption.ts
import crypto from 'crypto';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!; // 32 bytes key
const ALGORITHM = 'aes-256-gcm';

export function encrypt(text: string): string {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  cipher.setAAD(Buffer.from('XTask-Auth', 'utf8'));

  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const authTag = cipher.getAuthTag();

  return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
}

export function decrypt(encryptedData: string): string {
  const [ivHex, authTagHex, encrypted] = encryptedData.split(':');

  const iv = Buffer.from(ivHex, 'hex');
  const authTag = Buffer.from(authTagHex, 'hex');

  const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
  decipher.setAAD(Buffer.from('XTask-Auth', 'utf8'));
  decipher.setAuthTag(authTag);

  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}
```

#### Rate Limiting for Auth Endpoints
```typescript
// src/server/middleware/rateLimiting.ts
import rateLimit from 'express-rate-limit';

export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests, please try again later.',
  },
});
```

### 🔄 Complete Express Route Setup

#### Authentication Routes
```typescript
// src/server/routes/auth.ts
import express from 'express';
import { authRateLimit } from '../middleware/rateLimiting';
import {
  handleTwitterCallback,
  handleGoogleCallback,
  handleLogout,
  getCurrentUser
} from '../controllers/authController';

const router = express.Router();

// Apply rate limiting to auth routes
router.use(authRateLimit);

// OAuth callback routes
router.get('/twitter/callback', handleTwitterCallback);
router.get('/google/callback', handleGoogleCallback);

// Session management
router.get('/me', getCurrentUser);
router.post('/logout', handleLogout);

export default router;
```

#### Complete Auth Controller
```typescript
// src/server/controllers/authController.ts
import { Request, Response } from 'express';
import { prisma } from '../config/database';
import { encrypt, decrypt } from '../lib/encryption';
import jwt from 'jsonwebtoken';

export async function getCurrentUser(req: Request, res: Response) {
  try {
    const token = req.cookies['session-token'];

    if (!token) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const payload = jwt.verify(token, process.env.JWT_SECRET!) as SessionPayload;

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        twitterAccounts: {
          select: {
            id: true,
            username: true,
            displayName: true,
            profilePicture: true,
            isActive: true,
          },
        },
      },
    });

    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Remove sensitive data
    const { openaiApiKey, googleAiApiKey, ...safeUser } = user;

    res.json({
      user: safeUser,
      provider: payload.provider,
    });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(401).json({ error: 'Invalid session' });
  }
}

export async function handleLogout(req: Request, res: Response) {
  try {
    // Clear the session cookie
    res.clearCookie('session-token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
}
```

### 📱 Frontend Integration Components

#### Auth Provider Component
```typescript
// src/components/auth/AuthProvider.tsx
'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@prisma/client';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (provider: 'google' | 'twitter') => void;
  logout: () => Promise<void>;
  refetch: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchUser = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Auth fetch error:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = (provider: 'google' | 'twitter') => {
    const authUrls = {
      google: '/api/auth/google',
      twitter: '/api/auth/twitter',
    };

    window.location.href = authUrls[provider];
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      setUser(null);
      window.location.href = '/';
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, refetch: fetchUser }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

#### Login Page Component
```typescript
// src/app/(auth)/login/page.tsx
'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import { TwitterOAuthButton } from '@/components/auth/TwitterOAuthButton';
import { GoogleOAuthButton } from '@/components/auth/GoogleOAuthButton';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (user && !loading) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (user) {
    return null; // Will redirect to dashboard
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-bg">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-white">
            Welcome to XTask
          </h2>
          <p className="mt-2 text-sm text-gray-400">
            Sign in to manage your AI-powered social media
          </p>
        </div>

        <div className="space-y-4">
          <GoogleOAuthButton />
          <TwitterOAuthButton />
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            By signing in, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </div>
  );
}
```

### 🛡️ Protected Route Component
```typescript
// src/components/auth/ProtectedRoute.tsx
'use client';

import { useAuth } from './AuthProvider';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
        </div>
      )
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return <>{children}</>;
}
```

This implementation provides a robust, secure authentication system that integrates seamlessly with our XTask platform architecture.
