# Frontend Development Master Guide
## XTask Social Media Management Platform

### 🎯 Overview
This comprehensive guide provides the complete frontend development roadmap for XTask, a social media management platform built with Next.js 15, React 19, TypeScript, and shadcn/ui.

### 📋 Quick Reference
- **Framework**: Next.js 15.3.3 with App Router
- **React Version**: 19.0.0 with Server Components & Server Actions
- **TypeScript**: 5.5.x with strict mode
- **Styling**: Tailwind CSS 3.4.x + shadcn/ui
- **State Management**: Zustand + React Query
- **Forms**: React Hook Form + Zod validation
- **Package Manager**: npm

---

## 🏗️ Project Structure & File Organization

### App Directory Structure
```
xtask-frontend/
├── app/
│   ├── (auth)/                    # Route group for authentication
│   │   ├── layout.tsx            # Auth layout (centered forms)
│   │   ├── login/
│   │   │   └── page.tsx          # Login page
│   │   ├── signup/
│   │   │   └── page.tsx          # Signup page
│   │   └── forgot-password/
│   │       └── page.tsx          # Password reset
│   ├── (dashboard)/              # Route group for authenticated users
│   │   ├── layout.tsx            # Main dashboard layout (sidebar + header)
│   │   ├── dashboard/
│   │   │   ├── page.tsx          # Dashboard overview
│   │   │   └── loading.tsx       # Dashboard loading state
│   │   ├── agents/
│   │   │   ├── page.tsx          # Agent management list
│   │   │   ├── [id]/
│   │   │   │   ├── page.tsx      # Agent detail/edit
│   │   │   │   └── loading.tsx   # Agent detail loading
│   │   │   └── new/
│   │   │       └── page.tsx      # Create new agent
│   │   ├── compose/
│   │   │   └── page.tsx          # Tweet composer
│   │   ├── schedule/
│   │   │   ├── page.tsx          # Scheduling calendar
│   │   │   └── [id]/
│   │   │       └── page.tsx      # Edit scheduled post
│   │   ├── analytics/
│   │   │   ├── page.tsx          # Analytics dashboard
│   │   │   └── loading.tsx       # Analytics loading
│   │   ├── settings/
│   │   │   ├── page.tsx          # Settings overview
│   │   │   ├── profile/
│   │   │   │   └── page.tsx      # Profile settings
│   │   │   ├── accounts/
│   │   │   │   └── page.tsx      # Connected accounts
│   │   │   └── notifications/
│   │   │       └── page.tsx      # Notification preferences
│   │   └── error.tsx             # Dashboard error boundary
│   ├── api/                      # API routes
│   │   ├── auth/                 # Authentication endpoints
│   │   ├── agents/               # Agent management APIs
│   │   ├── tweets/               # Tweet operations
│   │   ├── analytics/            # Analytics data
│   │   └── uploadthing/          # Media upload
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   ├── page.tsx                  # Landing page
│   ├── loading.tsx               # Global loading
│   ├── error.tsx                 # Global error boundary
│   └── not-found.tsx             # 404 page
├── components/
│   ├── ui/                       # shadcn/ui components
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   ├── form.tsx
│   │   ├── input.tsx
│   │   ├── table.tsx
│   │   └── ...
│   ├── custom/                   # Application-specific components
│   │   ├── auth/
│   │   │   ├── login-form.tsx
│   │   │   ├── signup-form.tsx
│   │   │   └── oauth-buttons.tsx
│   │   ├── dashboard/
│   │   │   ├── metric-card.tsx
│   │   │   ├── activity-feed.tsx
│   │   │   └── quick-actions.tsx
│   │   ├── agents/
│   │   │   ├── agent-card.tsx
│   │   │   ├── agent-form.tsx
│   │   │   └── agent-list.tsx
│   │   ├── compose/
│   │   │   ├── tweet-composer.tsx
│   │   │   ├── media-uploader.tsx
│   │   │   └── preview-card.tsx
│   │   ├── schedule/
│   │   │   ├── calendar-view.tsx
│   │   │   ├── schedule-form.tsx
│   │   │   └── scheduled-list.tsx
│   │   ├── analytics/
│   │   │   ├── chart-component.tsx
│   │   │   ├── metrics-overview.tsx
│   │   │   └── performance-table.tsx
│   │   └── layout/
│   │       ├── sidebar.tsx
│   │       ├── header.tsx
│   │       ├── breadcrumbs.tsx
│   │       └── theme-toggle.tsx
│   └── providers/
│       ├── auth-provider.tsx
│       ├── query-provider.tsx
│       └── theme-provider.tsx
├── lib/
│   ├── auth.ts                   # Authentication utilities
│   ├── api.ts                    # API client
│   ├── utils.ts                  # General utilities
│   ├── validations.ts            # Zod schemas
│   └── stores/
│       ├── auth-store.ts         # Zustand auth store
│       ├── ui-store.ts           # UI state store
│       └── settings-store.ts     # Settings store
├── hooks/
│   ├── use-auth.ts               # Authentication hooks
│   ├── use-api.ts                # API hooks
│   └── use-local-storage.ts      # Local storage hooks
├── types/
│   ├── auth.ts                   # Authentication types
│   ├── agent.ts                  # Agent types
│   ├── tweet.ts                  # Tweet types
│   └── analytics.ts              # Analytics types
└── middleware.ts                 # Route protection middleware
```

### Component Organization Principles
- **Atomic Design**: Components organized by complexity (atoms → molecules → organisms)
- **Feature-Based**: Components grouped by application feature
- **Reusability**: Shared components in `ui/` and `custom/`
- **Type Safety**: Comprehensive TypeScript interfaces for all components

---

## 🎨 Design System & UI Components

### Color Palette
```css
:root {
  --primary-500: #8b5cf6;     /* Purple primary */
  --dark-bg: #0a0a0a;         /* Dark background */
  --dark-surface: #1a1a1a;    /* Dark surface */
  --dark-border: #2a2a2a;     /* Dark borders */
}
```

### shadcn/ui Component Usage Map

| Component | Usage | Pages |
|-----------|-------|-------|
| `Button` | Actions, forms, navigation | All pages |
| `Card` | Content containers | Dashboard, Analytics |
| `Dialog` | Modals, forms | Agent creation, Settings |
| `Form` | All form implementations | Auth, Settings, Compose |
| `Input` | Text inputs | Forms, Search |
| `Table` | Data display | Agents, Analytics, Schedule |
| `Calendar` | Date selection | Scheduling |
| `Avatar` | User/agent representation | Header, Agents |
| `Badge` | Status indicators | Agents, Schedule |
| `Tabs` | Section navigation | Analytics, Settings |
| `Toast` | Notifications | Global feedback |
| `Skeleton` | Loading states | All data-heavy pages |
| `Progress` | Upload/process status | Media uploads |
| `Sheet` | Side panels | Mobile navigation |
| `Tooltip` | Help text | Interactive elements |

---

## 📱 Page Specifications & Components

### 1. Dashboard Page (`/dashboard`)
**Purpose**: Overview of user metrics and quick actions
**Type**: Server Component (data fetching) + Client Components (interactivity)

**Components Required**:
- `MetricCard` - Display key statistics
- `ActivityFeed` - Recent activities list
- `QuickActionButtons` - Fast access to common tasks
- `ChartComponent` - Summary visualizations
- `RecentAgents` - Recently used agents

**Data Sources**:
- User statistics API
- Recent activity feed
- Agent performance summary

### 2. Agent Management Page (`/agents`)
**Purpose**: CRUD operations for AI agents
**Type**: Server Component (list) + Client Components (forms, actions)

**Components Required**:
- `AgentList` - Table of all agents
- `AgentCard` - Individual agent display
- `AgentForm` - Create/edit agent form
- `AgentStatusBadge` - Status indicators
- `AgentActions` - Action buttons

**Features**:
- Create new agents
- Edit existing agents
- Delete agents
- View agent performance
- Agent status management

### 3. Tweet Composer Page (`/compose`)
**Purpose**: Create and schedule tweets
**Type**: Client Component (form interactivity)

**Components Required**:
- `TweetComposer` - Main composition interface
- `MediaUploader` - Image/video upload
- `PreviewCard` - Tweet preview
- `ScheduleForm` - Date/time selection
- `CharacterCounter` - Tweet length indicator

**Features**:
- Rich text composition
- Media attachment
- Scheduling options
- Preview functionality
- Draft saving

### 4. Scheduling Page (`/schedule`)
**Purpose**: Manage scheduled posts
**Type**: Server Component (data) + Client Components (calendar)

**Components Required**:
- `CalendarView` - Monthly/weekly calendar
- `ScheduledPostCard` - Individual post display
- `ScheduleFilters` - Filter options
- `BulkActions` - Multi-select operations

**Features**:
- Calendar view of scheduled posts
- Edit scheduled posts
- Bulk operations
- Filter by agent/status

### 5. Analytics Page (`/analytics`)
**Purpose**: Performance metrics and insights
**Type**: Server Component (data) + Client Components (charts)

**Components Required**:
- `MetricsOverview` - Key performance indicators
- `ChartComponent` - Various chart types
- `PerformanceTable` - Detailed data table
- `DateRangePicker` - Time period selection
- `ExportButton` - Data export functionality

**Features**:
- Performance charts
- Engagement metrics
- Agent comparison
- Export capabilities
- Custom date ranges

### 6. Settings Page (`/settings`)
**Purpose**: User preferences and account management
**Type**: Server Component (data) + Client Components (forms)

**Components Required**:
- `ProfileForm` - User profile editing
- `NotificationSettings` - Notification preferences
- `ConnectedAccounts` - OAuth account management
- `SecuritySettings` - Password/security options
- `ThemeToggle` - Dark/light mode

**Features**:
- Profile management
- Notification preferences
- Connected account management
- Security settings
- Theme customization

---

## 🔧 Technical Implementation Details

### Server vs Client Components Strategy

**Server Components** (Default):
- Page layouts and static content
- Data fetching from APIs/database
- Initial page rendering
- SEO-critical content

**Client Components** (`'use client'`):
- Interactive forms and inputs
- State management (useState, useEffect)
- Event handlers (onClick, onChange)
- Third-party libraries requiring browser APIs
- Real-time features

### Authentication Middleware
```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Protect dashboard routes
  if (pathname.startsWith('/dashboard') || 
      pathname.startsWith('/agents') || 
      pathname.startsWith('/compose') ||
      pathname.startsWith('/schedule') ||
      pathname.startsWith('/analytics') ||
      pathname.startsWith('/settings')) {
    
    const sessionToken = request.cookies.get('session_token')?.value;
    
    if (!sessionToken || !(await validateJwtSession(sessionToken))) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }
  
  return NextResponse.next();
}
```

### State Management Architecture
- **Zustand**: Global application state (auth, UI preferences)
- **React Query**: Server state management and caching
- **React Hook Form**: Form state and validation
- **Local State**: Component-specific state with useState

### Form Validation Pattern
```typescript
// Example: Agent creation form
const agentSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  persona: z.string().min(1, "Persona is required"),
  settings: z.object({
    tone: z.enum(["professional", "casual", "humorous"]),
    frequency: z.number().min(1).max(10)
  })
});

type AgentFormData = z.infer<typeof agentSchema>;

const AgentForm = () => {
  const { register, handleSubmit, formState: { errors } } = useForm<AgentFormData>({
    resolver: zodResolver(agentSchema)
  });
  
  const onSubmit = async (data: AgentFormData) => {
    // Handle form submission with Server Action or API call
  };
  
  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      {/* Form fields */}
    </Form>
  );
};
```

---

## 📋 Development Task Checklist

### Phase 1: Foundation (Tasks 1-4)
- [ ] Project initialization with Next.js 15 + React 19
- [ ] Database setup with Prisma
- [ ] Tailwind CSS + shadcn/ui integration
- [ ] Core UI component library

### Phase 2: Authentication & Core Features (Tasks 5-14)
- [ ] User management system
- [ ] Multi-provider OAuth implementation
- [ ] Session management with JWT
- [ ] API security (rate limiting, CSRF)
- [ ] User profile management
- [ ] AI agent system setup
- [ ] Agent management interface
- [ ] Agent memory system
- [ ] Media upload integration
- [ ] Tweet composer interface

### Phase 3: Advanced Features (Tasks 15-25)
- [ ] Scheduling system
- [ ] Publishing automation
- [ ] OpenAI API integration
- [ ] Twitter API integration
- [ ] Google Gemini integration
- [ ] Mistral AI integration
- [ ] Hugging Face integration
- [ ] Agent orchestration
- [ ] Content generation pipeline
- [ ] Analytics dashboard
- [ ] Performance monitoring

### Phase 4: Frontend Implementation (Task 26)
- [ ] Dashboard page implementation
- [ ] Agent management page
- [ ] Tweet composer page
- [ ] Scheduling page
- [ ] Analytics page
- [ ] Settings page
- [ ] Authentication middleware
- [ ] Server/Client component optimization
- [ ] React 19 Server Actions integration

### Phase 5: Configuration & Testing (Tasks 27-28)
- [ ] Environment configuration
- [ ] Testing framework setup
- [ ] End-to-end testing
- [ ] Performance optimization
- [ ] Security audit
- [ ] Deployment preparation

---

## 🚀 Getting Started

1. **Initialize Project**: Start with Task 1 (Project Setup)
2. **Set Up Database**: Complete Task 2 (Database & Prisma)
3. **Configure Styling**: Implement Task 3 (Tailwind + shadcn/ui)
4. **Build Components**: Create Task 4 (Core UI Components)
5. **Implement Authentication**: Tasks 5-9 (Auth system)
6. **Develop Features**: Tasks 10-25 (Core functionality)
7. **Complete Frontend**: Task 26 (Frontend implementation)
8. **Finalize Setup**: Tasks 27-28 (Config & testing)

### Next Steps
Use Task Master CLI to begin development:
```bash
task-master next          # Show next task
task-master show 1        # View Task 1 details
task-master set-status --id=1 --status=in-progress
```

This guide serves as the definitive reference for frontend development. Each task contains detailed implementation specifications, component requirements, and testing strategies.

---

## 🔍 Detailed Component Specifications

### Authentication Components

#### LoginForm Component
```typescript
interface LoginFormProps {
  onSuccess?: (user: User) => void;
  redirectTo?: string;
}

// Features:
// - Email/password validation
// - OAuth provider buttons (Google, Twitter)
// - Remember me functionality
// - Forgot password link
// - Loading states and error handling
```

#### SignupForm Component
```typescript
interface SignupFormProps {
  onSuccess?: (user: User) => void;
  redirectTo?: string;
}

// Features:
// - Email, password, confirm password fields
// - Terms of service acceptance
// - OAuth provider options
// - Email verification flow
// - Validation with Zod schema
```

### Dashboard Components

#### MetricCard Component
```typescript
interface MetricCardProps {
  title: string;
  value: number | string;
  unit?: string;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
  };
  icon?: React.ReactNode;
  isLoading?: boolean;
}

// Usage: Display KPIs like total tweets, engagement rate, followers
```

#### ActivityFeed Component
```typescript
interface ActivityFeedProps {
  activities: Activity[];
  maxItems?: number;
  showTimestamp?: boolean;
  onItemClick?: (activity: Activity) => void;
}

interface Activity {
  id: string;
  type: 'tweet_posted' | 'agent_created' | 'schedule_updated';
  title: string;
  description?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Features:
// - Real-time activity updates
// - Clickable items for navigation
// - Activity type icons
// - Relative timestamps
```

### Agent Management Components

#### AgentCard Component
```typescript
interface AgentCardProps {
  agent: Agent;
  onEdit?: (agent: Agent) => void;
  onDelete?: (agentId: string) => void;
  onToggleStatus?: (agentId: string, status: AgentStatus) => void;
  showActions?: boolean;
}

interface Agent {
  id: string;
  name: string;
  description: string;
  persona: string;
  status: 'active' | 'inactive' | 'paused';
  avatar?: string;
  stats: {
    totalTweets: number;
    engagement: number;
    lastActive: Date;
  };
  settings: AgentSettings;
}

// Features:
// - Agent avatar display
// - Status badge with color coding
// - Quick stats overview
// - Action buttons (edit, delete, toggle)
// - Hover effects and animations
```

#### AgentForm Component
```typescript
interface AgentFormProps {
  agent?: Agent; // For editing existing agent
  onSubmit: (data: AgentFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

interface AgentFormData {
  name: string;
  description: string;
  persona: string;
  settings: {
    tone: 'professional' | 'casual' | 'humorous' | 'creative';
    frequency: number; // Posts per day
    topics: string[];
    restrictions: string[];
    timezone: string;
  };
  avatar?: File;
}

// Features:
// - Multi-step form with validation
// - Rich text editor for persona
// - File upload for avatar
// - Advanced settings accordion
// - Preview mode
// - Auto-save drafts
```

### Tweet Composer Components

#### TweetComposer Component
```typescript
interface TweetComposerProps {
  initialContent?: string;
  agentId?: string;
  onSubmit: (data: TweetData) => Promise<void>;
  onSaveDraft?: (data: TweetData) => void;
  maxLength?: number;
}

interface TweetData {
  content: string;
  media?: MediaFile[];
  scheduledFor?: Date;
  agentId?: string;
  threadParts?: string[]; // For tweet threads
}

// Features:
// - Character counter with visual feedback
// - Media attachment with preview
// - Thread composition
// - Emoji picker
// - Hashtag and mention suggestions
// - Schedule date/time picker
// - Draft auto-save
// - Preview mode
```

#### MediaUploader Component
```typescript
interface MediaUploaderProps {
  onUpload: (files: MediaFile[]) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  maxFileSize?: number;
  showPreview?: boolean;
}

interface MediaFile {
  id: string;
  url: string;
  type: 'image' | 'video' | 'gif';
  size: number;
  filename: string;
  alt?: string;
}

// Features:
// - Drag and drop upload
// - Multiple file selection
// - File type validation
// - Size limit enforcement
// - Upload progress indicator
// - Image preview with crop tool
// - Alt text input for accessibility
```

### Analytics Components

#### ChartComponent Component
```typescript
interface ChartComponentProps {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  data: ChartData;
  title?: string;
  height?: number;
  showLegend?: boolean;
  showTooltip?: boolean;
  colors?: string[];
  onDataPointClick?: (point: DataPoint) => void;
}

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    color?: string;
  }[];
}

// Features:
// - Multiple chart types
// - Interactive tooltips
// - Responsive design
// - Export functionality
// - Real-time data updates
// - Custom color schemes
// - Zoom and pan capabilities
```

#### PerformanceTable Component
```typescript
interface PerformanceTableProps {
  data: PerformanceData[];
  columns: TableColumn[];
  sortable?: boolean;
  filterable?: boolean;
  pagination?: boolean;
  onRowClick?: (row: PerformanceData) => void;
  onExport?: () => void;
}

interface PerformanceData {
  id: string;
  tweetId: string;
  content: string;
  publishedAt: Date;
  likes: number;
  retweets: number;
  replies: number;
  impressions: number;
  engagementRate: number;
  agentId: string;
  agentName: string;
}

// Features:
// - Sortable columns
// - Search and filter
// - Pagination
// - Row selection
// - Export to CSV/Excel
// - Column visibility toggle
// - Responsive table design
```

---

## 🎯 Task-Specific Implementation Details

### Task 26 Subtasks Breakdown

#### Subtask 26.13: Dashboard Page Implementation
**Components to Build**:
- `DashboardOverview` (Server Component)
- `MetricCard` (Client Component)
- `ActivityFeed` (Client Component)
- `QuickActions` (Client Component)
- `RecentAgents` (Server Component)

**API Integrations**:
- `/api/dashboard/metrics` - User statistics
- `/api/dashboard/activity` - Recent activities
- `/api/agents/recent` - Recently used agents

**Key Features**:
- Real-time metric updates
- Activity feed with infinite scroll
- Quick action buttons for common tasks
- Responsive grid layout
- Loading states and error boundaries

#### Subtask 26.14: Agent Management Page
**Components to Build**:
- `AgentList` (Server Component)
- `AgentCard` (Client Component)
- `AgentForm` (Client Component)
- `AgentFilters` (Client Component)
- `BulkActions` (Client Component)

**API Integrations**:
- `/api/agents` - CRUD operations
- `/api/agents/[id]/stats` - Agent performance
- `/api/agents/bulk` - Bulk operations

**Key Features**:
- Sortable and filterable agent list
- Bulk operations (activate, deactivate, delete)
- Agent creation wizard
- Performance metrics per agent
- Status management

#### Subtask 26.15: Tweet Composer Page
**Components to Build**:
- `TweetComposer` (Client Component)
- `MediaUploader` (Client Component)
- `ScheduleForm` (Client Component)
- `PreviewCard` (Client Component)
- `DraftManager` (Client Component)

**API Integrations**:
- `/api/tweets/compose` - Tweet creation
- `/api/media/upload` - Media upload
- `/api/tweets/schedule` - Scheduling
- `/api/tweets/drafts` - Draft management

**Key Features**:
- Rich text composition with formatting
- Media attachment and preview
- Scheduling with timezone support
- Draft auto-save and management
- Thread composition
- Character counting and validation

#### Subtask 26.16: Scheduling Page
**Components to Build**:
- `CalendarView` (Client Component)
- `ScheduledPostCard` (Client Component)
- `ScheduleFilters` (Client Component)
- `BulkScheduleActions` (Client Component)

**API Integrations**:
- `/api/schedule` - Scheduled posts CRUD
- `/api/schedule/bulk` - Bulk operations
- `/api/schedule/calendar` - Calendar data

**Key Features**:
- Monthly/weekly calendar view
- Drag and drop rescheduling
- Bulk operations on scheduled posts
- Filter by agent, status, date range
- Quick edit functionality

#### Subtask 26.17: Analytics Page
**Components to Build**:
- `AnalyticsOverview` (Server Component)
- `ChartComponent` (Client Component)
- `MetricsGrid` (Client Component)
- `PerformanceTable` (Client Component)
- `DateRangePicker` (Client Component)
- `ExportTools` (Client Component)

**API Integrations**:
- `/api/analytics/overview` - Summary metrics
- `/api/analytics/charts` - Chart data
- `/api/analytics/performance` - Detailed performance
- `/api/analytics/export` - Data export

**Key Features**:
- Interactive charts and graphs
- Customizable date ranges
- Performance comparison tools
- Export functionality
- Real-time data updates
- Agent performance comparison

#### Subtask 26.18: Settings Page
**Components to Build**:
- `ProfileSettings` (Client Component)
- `NotificationSettings` (Client Component)
- `ConnectedAccounts` (Client Component)
- `SecuritySettings` (Client Component)
- `PreferencesForm` (Client Component)

**API Integrations**:
- `/api/user/profile` - Profile management
- `/api/user/notifications` - Notification preferences
- `/api/auth/accounts` - Connected accounts
- `/api/user/security` - Security settings

**Key Features**:
- Profile information editing
- Notification preferences
- Connected account management
- Password and security settings
- Theme and UI preferences
- Data export and deletion

---

## 🧪 Testing Strategy

### Component Testing
- Unit tests for all custom components
- Integration tests for form submissions
- Accessibility testing with axe-core
- Visual regression testing

### Page Testing
- End-to-end testing with Playwright
- Authentication flow testing
- Data fetching and error handling
- Responsive design testing

### Performance Testing
- Core Web Vitals monitoring
- npmdle size analysis
- Server Component vs Client Component performance
- Loading state optimization

---

## 📚 Additional Resources

### Documentation References
- [Next.js 15 Documentation](https://nextjs.org/docs)
- [React 19 Features](https://react.dev/blog/2024/04/25/react-19)
- [shadcn/ui Components](https://ui.shadcn.com/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)

### Development Tools
- **Task Master CLI**: `task-master next`, `task-master show <id>`
- **npm Commands**: `npm dev`, `npm build`, `npm test`
- **shadcn/ui CLI**: `npm dlx shadcn-ui@latest add <component>`

This comprehensive guide provides everything needed to implement the frontend successfully. Each component specification includes TypeScript interfaces, feature lists, and implementation details to ensure consistent, high-quality development.
