# XTask UI Components Specification
## Comprehensive Component Design & Implementation Guide

### 🎨 Design System Overview

#### Color Palette
```css
/* Primary Colors */
--primary-50: #faf5ff
--primary-500: #8b5cf6  /* Main purple */
--primary-600: #7c3aed
--primary-700: #6d28d9
--primary-900: #4c1d95

/* Dark Theme */
--dark-bg: #0a0a0a      /* Main background */
--dark-surface: #1a1a1a  /* Card/surface background */
--dark-border: #2a2a2a   /* Border color */
--dark-text: #ffffff     /* Primary text */
--dark-text-muted: #a1a1aa /* Secondary text */

/* Status Colors */
--success: #10b981
--warning: #f59e0b
--error: #ef4444
--info: #3b82f6
```

#### Typography Scale
```css
/* Font Sizes */
--text-xs: 0.75rem    /* 12px */
--text-sm: 0.875rem   /* 14px */
--text-base: 1rem     /* 16px */
--text-lg: 1.125rem   /* 18px */
--text-xl: 1.25rem    /* 20px */
--text-2xl: 1.5rem    /* 24px */
--text-3xl: 1.875rem  /* 30px */
--text-4xl: 2.25rem   /* 36px */

/* Font Weights */
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
```

### 🧩 Core UI Components

#### 1. Button Component
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size: 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  disabled?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  children: React.ReactNode
  onClick?: () => void
}
```

**Visual States:**
- Primary: Purple gradient background, white text
- Secondary: Dark surface background, purple border
- Outline: Transparent background, purple border
- Ghost: Transparent background, hover effects
- Danger: Red background for destructive actions

#### 2. Input Component
```typescript
interface InputProps {
  type: 'text' | 'email' | 'password' | 'number' | 'textarea'
  label?: string
  placeholder?: string
  error?: string
  helperText?: string
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  disabled?: boolean
  required?: boolean
  value?: string
  onChange?: (value: string) => void
}
```

**Features:**
- Floating labels with smooth animations
- Error states with red border and message
- Icon support with proper spacing
- Dark theme optimized colors

#### 3. Card Component
```typescript
interface CardProps {
  variant: 'default' | 'elevated' | 'outlined' | 'filled'
  padding: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  clickable?: boolean
  children: React.ReactNode
  className?: string
}
```

**Visual Design:**
- Dark surface background (#1a1a1a)
- Subtle border (#2a2a2a)
- Hover effects with purple accent
- Smooth shadow transitions

### 🤖 Agent-Specific Components

#### 1. Agent Card
```typescript
interface AgentCardProps {
  agent: {
    id: string
    name: string
    description: string
    avatar?: string
    status: 'active' | 'inactive' | 'training'
    lastActivity: Date
    tweetsGenerated: number
    engagementRate: number
  }
  onEdit?: () => void
  onDelete?: () => void
  onChat?: () => void
  onToggleStatus?: () => void
}
```

**Visual Elements:**
- Agent avatar with status indicator
- Performance metrics with progress bars
- Action buttons (edit, delete, chat)
- Status badge with color coding
- Hover effects with purple glow

#### 2. Agent Creation Form
```typescript
interface AgentFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<Agent>
  onSubmit: (data: AgentFormData) => void
  onCancel: () => void
}

interface AgentFormData {
  name: string
  description: string
  personaFile?: File
  aiProvider: 'openai' | 'google'
  model: string
  temperature: number
  maxTokens: number
  autoSchedule: boolean
  maxDailyTweets: number
}
```

**Form Sections:**
1. Basic Information (name, description)
2. Persona Configuration (file upload)
3. AI Settings (provider, model, parameters)
4. Behavior Settings (scheduling, limits)

#### 3. Persona File Uploader
```typescript
interface PersonaUploaderProps {
  onFileSelect: (file: File) => void
  onFileRemove: () => void
  acceptedFormats: string[]
  maxSize: number
  currentFile?: File
  validationSchema?: object
}
```

**Features:**
- Drag & drop interface
- File format validation (.json, .txt, .md)
- Real-time schema validation
- Preview of persona content
- Progress indicator during upload

### ✍️ Compose Components

#### 1. Tweet Composer
```typescript
interface TweetComposerProps {
  mode: 'compose' | 'schedule' | 'draft'
  initialContent?: string
  agentId?: string
  onPublish?: (content: TweetData) => void
  onSchedule?: (content: TweetData, scheduledAt: Date) => void
  onSaveDraft?: (content: TweetData) => void
  onCancel?: () => void
}

interface TweetData {
  content: string
  mediaUrls: string[]
  threadTweets?: string[]
  agentId?: string
  scheduledAt?: Date
}
```

**Features:**
- Rich text editor with formatting
- Character counter with visual feedback
- Media upload with preview
- Thread composition support
- AI suggestions panel
- Emoji picker integration

#### 2. Rich Text Editor
```typescript
interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  maxLength?: number
  features: {
    bold?: boolean
    italic?: boolean
    mentions?: boolean
    hashtags?: boolean
    links?: boolean
  }
  onMentionSearch?: (query: string) => Promise<User[]>
}
```

**Formatting Features:**
- Bold/italic text support
- @mention autocomplete
- #hashtag highlighting
- Link detection and formatting
- Character count with warnings

#### 3. Media Uploader
```typescript
interface MediaUploaderProps {
  maxFiles: number
  acceptedTypes: string[]
  maxFileSize: number
  onUpload: (files: File[]) => Promise<string[]>
  onRemove: (url: string) => void
  currentMedia: string[]
}
```

**Capabilities:**
- Multiple file upload
- Image/video/GIF support
- Drag & drop interface
- Upload progress tracking
- Media preview with remove option

### 📅 Scheduling Components

#### 1. Schedule Calendar
```typescript
interface ScheduleCalendarProps {
  scheduledTweets: ScheduledTweet[]
  onDateSelect: (date: Date) => void
  onTweetClick: (tweet: ScheduledTweet) => void
  view: 'month' | 'week' | 'day'
  timezone: string
}
```

**Visual Features:**
- Monthly/weekly/daily views
- Tweet indicators on dates
- Color coding by agent
- Time slot visualization
- Drag & drop rescheduling

#### 2. Time Picker
```typescript
interface TimePickerProps {
  value: Date
  onChange: (date: Date) => void
  timezone: string
  minDate?: Date
  maxDate?: Date
  suggestedTimes?: Date[]
  optimalTimes?: Date[]
}
```

**Smart Features:**
- Timezone-aware selection
- Optimal posting time suggestions
- Visual indicators for peak times
- Quick time slot selection
- Conflict detection

### 📊 Analytics Components

#### 1. Analytics Dashboard
```typescript
interface AnalyticsDashboardProps {
  dateRange: DateRange
  agentId?: string
  metrics: {
    totalTweets: number
    totalEngagement: number
    followerGrowth: number
    topPerformingTweet: Tweet
    engagementRate: number
    reachMetrics: ReachData
  }
}
```

**Metric Cards:**
- Total tweets with trend indicator
- Engagement rate with sparkline
- Follower growth chart
- Top performing content
- Reach and impressions

#### 2. Engagement Chart
```typescript
interface EngagementChartProps {
  data: EngagementData[]
  timeframe: 'day' | 'week' | 'month'
  metrics: ('likes' | 'retweets' | 'replies' | 'clicks')[]
  showComparison?: boolean
  agentFilter?: string[]
}
```

**Chart Features:**
- Interactive line/bar charts
- Multiple metric overlay
- Agent comparison mode
- Hover tooltips with details
- Export functionality

### 🔧 Settings Components

#### 1. AI Provider Configuration
```typescript
interface AIProviderConfigProps {
  providers: {
    openai: {
      apiKey: string
      baseUrl: string
      models: string[]
      defaultModel: string
    }
    google: {
      apiKey: string
      models: string[]
      defaultModel: string
    }
  }
  onSave: (config: AIProviderConfig) => void
  onTest: (provider: string) => Promise<boolean>
}
```

**Configuration Sections:**
- API key management with encryption
- Model selection dropdowns
- Custom endpoint configuration
- Connection testing
- Usage monitoring

#### 2. Account Connections
```typescript
interface AccountConnectionsProps {
  connectedAccounts: ConnectedAccount[]
  availableProviders: OAuthProvider[]
  onConnect: (provider: string) => void
  onDisconnect: (accountId: string) => void
  onSetPrimary: (accountId: string) => void
}
```

**Account Management:**
- Connected account list
- Connection status indicators
- Primary account selection
- Disconnect with confirmation
- OAuth flow integration

### 🎯 Component Implementation Priorities

#### Phase 1: Core Components (Week 1)
1. Button, Input, Card, Modal
2. Layout components (Sidebar, Navbar)
3. Theme toggle and basic styling

#### Phase 2: Agent Components (Week 2)
1. Agent Card and Agent Form
2. Persona File Uploader
3. Agent List and Management

#### Phase 3: Compose Components (Week 3)
1. Tweet Composer with Rich Text Editor
2. Media Uploader
3. Character Counter and Validation

#### Phase 4: Advanced Components (Week 4)
1. Schedule Calendar and Time Picker
2. Analytics Dashboard and Charts
3. Settings and Configuration panels

### 🎨 Animation & Interaction Guidelines

#### Micro-interactions
- Button hover: Scale 1.02, purple glow
- Card hover: Lift shadow, border glow
- Input focus: Border color transition
- Loading states: Skeleton animations

#### Page Transitions
- Route changes: Fade in/out
- Modal open/close: Scale and fade
- Sidebar toggle: Slide animation
- Tab switching: Horizontal slide

#### Performance Considerations
- Lazy load heavy components
- Virtualize long lists
- Optimize re-renders with React.memo
- Use CSS transforms for animations

### 🚀 Implementation Notes for Agent

#### Component Library Integration
- Use shadcn/ui as the base component library
- Customize components with XTask brand colors
- Ensure all components support dark theme
- Implement proper TypeScript interfaces

#### Accessibility Requirements
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Focus management for modals
- Color contrast ratios > 4.5:1

#### Performance Optimization
- Code splitting for route-based components
- Lazy loading for heavy components
- Image optimization with Next.js Image
- npmdle size monitoring
- Core Web Vitals optimization

#### Testing Strategy
- Unit tests for all components
- Integration tests for forms
- E2E tests for critical user flows
- Visual regression testing
- Accessibility testing

This specification provides a comprehensive guide for implementing a modern, accessible, and visually appealing UI for the XTask platform.
