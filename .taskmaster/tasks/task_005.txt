# Task ID: 5
# Title: Database Schema for Authentication & User Profiles
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Implement the database schema for User and TwitterAccount models, including fields necessary for authentication, user preferences, and connected social accounts.
# Details:
Update `prisma/schema.prisma` to define `User` model with fields like `id`, `email`, `passwordHash`, `name`, `preferences` (JSONB), `createdAt`, `updatedAt`. Define `TwitterAccount` model with fields like `id`, `userId`, `twitterId`, `accessToken`, `refreshToken`, `username`, `profileImageUrl`. Establish a one-to-many relationship between `User` and `TwitterAccount`. Run `npm prisma migrate dev --name auth_schema`.

# Test Strategy:
Verify new tables and columns are created in the database. Use Prisma Studio (`npm prisma studio`) to manually add data and confirm schema integrity. Write a simple Prisma script to create a user and link a Twitter account.

# Subtasks:
## 1. Define User Model Schema [pending]
### Dependencies: None
### Description: Create the Prisma schema definition for the `User` model, including essential fields such as `id`, `email`, `passwordHash`, `createdAt`, and `updatedAt`.
### Details:
This step involves writing the `model User { ... }` block in `schema.prisma`.

## 2. Define TwitterAccount Model and Relationship [pending]
### Dependencies: 5.1
### Description: Create the Prisma schema definition for the `TwitterAccount` model, including fields like `id`, `userId`, `oauthToken`, `oauthSecret`, `twitterId`, `username`, `createdAt`, `updatedAt`, and establish the one-to-many relationship with the `User` model.
### Details:
This step involves writing the `model TwitterAccount { ... }` block and adding the `twitterAccounts` relation field to the `User` model.

## 3. Generate and Apply Prisma Migration [pending]
### Dependencies: 5.1, 5.2
### Description: Execute Prisma CLI commands (`npx prisma migrate dev --name init_models`) to generate a new migration file based on the updated schema and apply it to the database.
### Details:
This step creates the SQL migration file and runs it against the configured database, creating the new tables and relationships.

## 4. Verify Schema and Data Integrity [pending]
### Dependencies: 5.3
### Description: Verify the successful application of the migration by inspecting the database schema (e.g., using a database client, `npx prisma studio`, or `npx prisma db pull`) and confirming the presence of `User` and `TwitterAccount` tables with correct fields and relationships.
### Details:
This step ensures that the database reflects the changes defined in the Prisma schema and that tables and foreign keys are correctly set up.

