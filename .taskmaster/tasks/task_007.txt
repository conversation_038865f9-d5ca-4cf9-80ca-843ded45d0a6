# Task ID: 7
# Title: Develop Database-Based Session Management with JWT
# Status: pending
# Dependencies: 6
# Priority: high
# Description: Implement database-based session management using JWT tokens for secure and scalable user sessions, including login, logout, and session validation.
# Details:
Upon successful login (OAuth or traditional), generate a JWT using `jsonwebtoken` (v9.0.2). Store the JWT in an HTTP-only cookie. Create a `Session` model in Prisma to store session data (e.g., `userId`, `jwtId`, `expiresAt`, `ipAddress`, `userAgent`) for revocation and tracking. Implement middleware to validate JWTs on incoming requests. Use `bcryptjs` (v2.4.3) for password hashing if traditional login is added later, or for any internal password management. Ensure JWT secret is securely stored in environment variables.

# Test Strategy:
Log in and verify an HTTP-only cookie containing the JWT is set. Make authenticated API requests and ensure they succeed. Test logout functionality to ensure session invalidation. Verify session data is correctly stored and updated in the database.

# Subtasks:
## 1. Implement Secure Secret Management [pending]
### Dependencies: None
### Description: Establish a secure method for storing and accessing application secrets, including the JWT signing key. This could involve environment variables, a secrets manager, or a configuration file with restricted access.
### Details:
Define how the JWT signing secret will be loaded securely at application startup. Consider using a library for environment variable management or a dedicated secrets vault.

## 2. Develop JWT Generation Logic [pending]
### Dependencies: 7.1
### Description: Create a function or service responsible for generating JSON Web Tokens (JWTs). This includes defining the payload (claims), setting an expiration time, and signing the token using the securely managed secret.
### Details:
Implement the logic to create access and refresh tokens. Define standard claims (e.g., 'sub', 'exp', 'iat') and custom claims as needed. Use a robust JWT library.

## 3. Implement JWT Storage and Transmission (HTTP-only Cookies) [pending]
### Dependencies: 7.2
### Description: Design and implement the mechanism for securely storing the generated JWTs on the client-side, primarily using HTTP-only, secure cookies, and transmitting them with subsequent requests.
### Details:
Configure the server to set JWTs as HTTP-only and secure cookies upon successful authentication. Ensure proper SameSite attribute settings (e.g., 'Lax' or 'Strict') to mitigate CSRF.

## 4. Design and Implement Database Session Model [pending]
### Dependencies: None
### Description: Create a database model to track active user sessions. This model will store information necessary for session management, such as user ID, token ID (if applicable), creation/expiration times, and a flag for revocation.
### Details:
Define the schema for the 'sessions' table, including fields like 'user_id', 'token_jti' (JWT ID), 'issued_at', 'expires_at', and 'is_revoked'. This model will be used for session tracking and revocation, separate from the JWT itself.

## 5. Develop Authentication Middleware [pending]
### Dependencies: 7.1, 7.3, 7.4
### Description: Create a middleware function that intercepts incoming requests, extracts the JWT from the HTTP-only cookie, validates its signature and claims, and attaches the authenticated user's information to the request object.
### Details:
The middleware should verify the JWT's signature using the secret, check expiration, and optionally check against the database session model for revocation status. Handle token absence or invalidity gracefully.

## 6. Implement JWT Revocation Mechanism [pending]
### Dependencies: 7.4, 7.5
### Description: Develop the logic to invalidate or revoke active JWTs, typically by updating the status in the database session model. This is crucial for logout, password changes, or security breaches.
### Details:
Implement an endpoint or function that, upon user logout or administrative action, marks the corresponding session in the database as revoked. The authentication middleware (Subtask 5) should then check this status.

