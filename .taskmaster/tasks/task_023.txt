# Task ID: 23
# Title: Bulk Scheduling and Timezone Support
# Status: pending
# Dependencies: 16, 22
# Priority: medium
# Description: Implement bulk scheduling capabilities and robust timezone handling for scheduled tweets.
# Details:
For bulk scheduling, create a UI that allows users to upload a list of tweets or generate multiple tweets and schedule them at once (e.g., spaced out over time). The `POST /api/tweets/schedule` endpoint should accept an array of tweets. For timezone support, store the user's preferred timezone in the `User` model. All `scheduledAt` times in the database should be stored in UTC. When displaying times to the user, convert them to the user's local timezone. When scheduling, convert the user's local time input to UTC before saving. Use a library like `date-fns-tz` or `luxon` for reliable timezone conversions.

# Test Strategy:
Schedule tweets from different timezones and verify they are published at the correct UTC time, and displayed correctly in the user's local timezone. Test bulk scheduling with multiple tweets and confirm all are scheduled as expected.

# Subtasks:
## 1. Design Bulk Scheduling User Interface [pending]
### Dependencies: None
### Description: Outline the user interface elements and workflow for initiating and managing bulk scheduling operations. This includes input fields for multiple items, date/time pickers, and status indicators.
### Details:
Focus on user experience for adding multiple schedule entries, selecting common times, and reviewing scheduled items before submission. Consider error handling and progress display.

## 2. Design Bulk Scheduling API Endpoints [pending]
### Dependencies: 23.1
### Description: Define the RESTful API endpoints, request/response schemas, and data models necessary to support the bulk scheduling UI and backend processing.
### Details:
Specify endpoints for submitting bulk schedules, retrieving status, and potentially cancelling. Define payload structure for multiple schedule items, including `scheduledAt` (likely in UTC).

## 3. Design & Implement User Timezone Storage [pending]
### Dependencies: None
### Description: Determine the best approach for storing user-specific timezone preferences (e.g., IANA timezone string) in the database and implement the necessary data model and retrieval mechanisms.
### Details:
Research best practices for timezone storage. Ensure the chosen method allows for accurate conversion and is easily accessible when processing user-specific times.

## 4. Implement `scheduledAt` Time Conversion Logic [pending]
### Dependencies: 23.2, 23.3
### Description: Develop and implement the core logic for converting `scheduledAt` times between UTC (for backend storage/processing) and the user's local timezone (for UI display and input).
### Details:
Utilize a robust date/time library (e.g., Joda-Time, Moment.js, `Intl.DateTimeFormat`) to handle timezone conversions, daylight saving time, and edge cases. Ensure consistency between frontend and backend.

## 5. Integrate Bulk Scheduling with Timezone Logic & Test [pending]
### Dependencies: 23.1, 23.2, 23.3, 23.4
### Description: Integrate the designed UI, API, timezone storage, and conversion logic into a cohesive system and perform comprehensive testing to ensure correct functionality and data integrity.
### Details:
Conduct end-to-end testing, including edge cases for timezones (e.g., DST changes, different offsets), large bulk submissions, and error handling. Verify `scheduledAt` times are correctly displayed and stored.

