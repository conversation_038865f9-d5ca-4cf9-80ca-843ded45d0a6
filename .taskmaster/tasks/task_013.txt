# Task ID: 13
# Title: UploadThing Integration for Media Uploads
# Status: pending
# Dependencies: 2, 4
# Priority: high
# Description: Integrate UploadThing 6.2.0 for secure, efficient, and authenticated media (images/videos) uploads. This involves comprehensive backend configuration (file router, middleware, `onUploadComplete` callback), frontend component integration using `generateComponents`, and defining the `MediaFile` schema. Implement robust security considerations, including authentication and authorization, and define appropriate file type and size limits.
# Details:
Obtain `UPLOADTHING_SECRET` and `UPLOADTHING_APP_ID` from the UploadThing dashboard and add them to your project's `.env` file. Install `uploadthing` (v6.2.0) and `@uploadthing/react`.

**Backend Configuration (`app/api/uploadthing/core.ts`):**
1.  Create the UploadThing file router in `app/api/uploadthing/core.ts`. This file will define the upload endpoints, allowed file types, and size limits.
2.  Implement authentication middleware within the file router (e.g., using `auth()` from `@clerk/nextjs` or similar context) to ensure only authorized users can upload files.
3.  Define specific upload endpoints (e.g., `imageUploader`, `videoUploader`) with `f.image().maxFileSize('4MB')` or `f.video().maxFileSize('16MB')` for type and size validation.
4.  Implement the `onUploadComplete` callback for each endpoint. This callback will receive the uploaded file's URL, key, and metadata. Inside this callback, store the relevant information (URL, key, file type, size, user ID, etc.) into the `MediaFile` database table.

**MediaFile Schema (`prisma/schema.prisma`):**
1.  Define the `MediaFile` model in `prisma/schema.prisma`. Essential fields include: `id` (String @id @default(cuid())), `userId` (String), `url` (String), `key` (String @unique), ``type` (String, e.g., 'image', 'video'), `size` (Int, in bytes), `createdAt` (DateTime @default(now())).
2.  Ensure `userId` is linked to your User model for proper ownership.

**Frontend Integration:**
1.  Use `generateComponents` from `@uploadthing/react` to create type-safe `UploadButton` and `UploadDropzone` components based on your backend file router.
2.  Integrate the generated `UploadButton` or `UploadDropzone` component into your frontend application (e.g., a user profile page or content creation form). Handle the `onClientUploadComplete` and `onUploadError` callbacks to provide user feedback.

**Security Considerations:**
*   Implement server-side validation for file types and sizes within the UploadThing router.
*   Ensure proper authentication and authorization checks are performed in the UploadThing middleware before allowing uploads.
*   Store only necessary metadata (URL, key) in your database, not the files themselves.
*   Consider rate limiting for upload endpoints to prevent abuse.

# Test Strategy:
Perform comprehensive testing for various image and video file types, including edge cases (e.g., very small/large files). Verify successful upload to UploadThing and that the file URL, key, type, size, and `userId` are stored correctly in the `MediaFile` table. Test error handling for failed uploads, including:
*   Attempting to upload unauthorized file types (e.g., `.exe`, `.zip`).
*   Attempting to upload files exceeding defined size limits.
*   Attempting uploads without proper authentication/authorization.
*   Verify that `onUploadComplete` correctly triggers database storage and `onClientUploadComplete` provides appropriate frontend feedback.
*   Check for unique key constraint violations if re-uploading the same file.

# Subtasks:
## 1. Setup UploadThing API Keys [pending]
### Dependencies: None
### Description: Obtain and configure the necessary API keys (`UPLOADTHING_SECRET`, `UPLOADTHING_APP_ID`) from the UploadThing dashboard and add them to the project's environment variables (.env file).
### Details:
This involves signing up for UploadThing, creating an application, and securely storing the generated API keys for backend access.

## 2. Define MediaFile Schema and Storage [pending]
### Dependencies: None
### Description: Create a new database schema (`MediaFile` model) to store metadata for uploaded files, including fields like `id`, `userId`, `url`, `key`, `type`, `size`, `createdAt`. Implement the necessary database migrations.
### Details:
This schema will be used to persist information about files uploaded via UploadThing, allowing for later retrieval and management. Ensure `userId` is properly linked.

## 3. Configure Backend UploadThing Endpoints [pending]
### Dependencies: 13.1, 13.2
### Description: Implement the backend API routes for UploadThing in `app/api/uploadthing/core.ts`, defining file types, sizes, implementing authentication middleware, and handling the `onUploadComplete` callback to save file metadata (URL, key, type, size, userId) to the `MediaFile` database schema.
### Details:
This involves setting up the `/api/uploadthing` route, configuring the `createUploadthing` instance with `auth()` middleware, defining specific uploaders (e.g., `imageUploader`, `videoUploader`) with `f.image().maxFileSize()`, and integrating with the database to store file details upon successful upload via `onUploadComplete`.

## 4. Integrate Frontend Upload Component [pending]
### Dependencies: 13.3
### Description: Integrate the UploadThing React component using `generateComponents` into the frontend application, connecting it to the configured backend endpoints and handling successful upload responses and errors.
### Details:
This step involves using `generateComponents` to create type-safe `UploadButton` or `UploadDropzone` components, adding the UI element that users will interact with to upload files, ensuring it correctly communicates with the backend, and provides feedback on upload status via `onClientUploadComplete` and `onUploadError`.

