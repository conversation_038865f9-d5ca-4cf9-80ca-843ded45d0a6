# Task ID: 14
# Title: Rich Text Tweet Composer UI
# Status: pending
# Dependencies: 13
# Priority: high
# Description: Develop a rich text tweet composer UI with support for text formatting, media embedding (via UploadThing), and character limits.
# Details:
Use a rich text editor library like `react-quill` or `Lexical` (more modern, but higher complexity) for the tweet composer. Integrate the UploadThing component to allow users to attach images/videos. Implement real-time character counting (Twitter/X limits) and visual feedback for exceeding limits. Ensure media previews are displayed within the composer. Use React Hook Form for managing the composer state.

# Test Strategy:
Test text input, formatting (bold, italics), and media embedding. Verify character count updates correctly and prevents submission if limits are exceeded. Ensure media previews render correctly. Test composing a tweet with multiple media files.

# Subtasks:
## 1. Setup Core Rich Text Editor [pending]
### Dependencies: None
### Description: Initialize and configure the chosen rich text editor library (e.g., TipTap, Quill, Slate) within the application. This includes basic text formatting capabilities.
### Details:
Research and select a suitable rich text editor library. Implement basic editor instance, ensure text input and display are functional. Configure initial toolbar options.

## 2. Implement Real-time Character Counter [pending]
### Dependencies: 14.1
### Description: Develop a real-time character counting mechanism that updates as the user types in the rich text editor.
### Details:
Hook into the editor's change events to get the current text content. Calculate character count (excluding formatting markup if desired). Display the count near the editor.

## 3. Integrate UploadThing Backend Endpoint [pending]
### Dependencies: None
### Description: Set up the necessary backend API endpoint using UploadThing to handle secure media file uploads.
### Details:
Configure UploadThing in the backend. Define an upload route that specifies allowed file types (images, videos) and maximum file size. Implement security measures.

## 4. Integrate UploadThing with Rich Text Editor [pending]
### Dependencies: 14.1, 14.3
### Description: Connect the rich text editor's media embedding functionality to the UploadThing backend for seamless file uploads and insertion.
### Details:
Add a custom 'upload media' button/plugin to the editor's toolbar. Implement the logic to trigger UploadThing's upload process. On successful upload, insert the media URL into the editor content.

## 5. Develop Media Preview Display [pending]
### Dependencies: 14.4
### Description: Ensure that embedded media (images, videos) are correctly rendered and displayed within the rich text editor's view.
### Details:
Configure the rich text editor to correctly interpret and display image and video URLs. Implement responsive styling for embedded media. Handle potential broken links or loading states.

## 6. Implement UI/UX Refinements & Error Handling [pending]
### Dependencies: 14.1, 14.2, 14.4, 14.5
### Description: Refine the user interface for the editor and media features, and implement robust error handling for uploads and editor interactions.
### Details:
Add loading indicators for uploads. Implement user-friendly error messages for failed uploads or invalid file types. Ensure editor responsiveness and accessibility. Conduct thorough testing.

