# Render Blueprint for Tasker Application
# This file defines the infrastructure for your Tasker app on Render

services:
  # Frontend - Next.js Application
  - type: web
    name: tasker-frontend
    env: node
    plan: starter
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3030
      - key: FRONTEND_URL
        fromService:
          type: web
          name: tasker-frontend
          property: host
      - key: BACKEND_URL
        fromService:
          type: web
          name: tasker-frontend
          property: host
      - key: NEXTAUTH_URL
        fromService:
          type: web
          name: tasker-frontend
          property: host
      # Add your secrets in Render dashboard
      - key: NEXTAUTH_SECRET
        sync: false
      - key: DATABASE_URL
        sync: false
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOOGLE_CLIENT_SECRET
        sync: false
      - key: TWITTER_API_KEY
        sync: false
      - key: TWITTER_API_SECRET
        sync: false
      - key: TWITTER_BEARER_TOKEN
        sync: false
      - key: UPLOADTHING_SECRET
        sync: false
      - key: UPLOADTHING_APP_ID
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: REDIS_URL
        sync: false

  # Database - PostgreSQL
  - type: pserv
    name: tasker-database
    env: docker
    plan: starter
    disk:
      name: tasker-db-disk
      mountPath: /var/lib/postgresql/data
      sizeGB: 1

  # Redis Cache (Optional)
  - type: redis
    name: tasker-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru

# Environment Groups (for shared environment variables)
envVarGroups:
  - name: tasker-shared
    envVars:
      - key: NODE_ENV
        value: production
      - key: TZ
        value: UTC
