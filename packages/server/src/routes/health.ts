import { Router } from 'express';
import { checkDatabaseHealth, getDatabaseStats } from '../../../../lib/database';

const router = Router();

// Helper function to get feature availability
function getFeatureAvailability() {
  return {
    database: !!process.env.DATABASE_URL,
    redis: !!process.env.REDIS_URL,
    googleOAuth: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
    twitterOAuth: !!(process.env.TWITTER_CLIENT_ID && process.env.TWITTER_CLIENT_SECRET),
    uploadThing: !!(process.env.UPLOADTHING_SECRET && process.env.UPLOADTHING_APP_ID && process.env.UPLOADTHING_TOKEN),
    openai: !!process.env.OPENAI_API_KEY,
    gemini: !!process.env.GEMINI_API_KEY,
    mistral: !!process.env.MISTRAL_API_KEY,
    huggingface: !!process.env.HUGGINGFACE_API_KEY,
    groq: !!process.env.GROQ_API_KEY,
    openrouter: !!process.env.OPENROUTER_API_KEY,
    scheduler: process.env.SCHEDULER_ENABLED !== 'false',
  }
}

// Helper function to get AI provider count
function getAIProviderCount() {
  const providers = [
    process.env.OPENAI_API_KEY,
    process.env.GEMINI_API_KEY,
    process.env.MISTRAL_API_KEY,
    process.env.HUGGINGFACE_API_KEY,
    process.env.GROQ_API_KEY,
    process.env.OPENROUTER_API_KEY,
  ]
  return providers.filter(key => key && key.length > 0).length
}

// Enhanced health check endpoint with environment validation
router.get('/health', async (req, res) => {
  try {
    const dbHealthy = await checkDatabaseHealth();
    const features = getFeatureAvailability();
    const aiProviderCount = getAIProviderCount();

    // Determine overall health status
    const hasRequiredFeatures = features.database && features.googleOAuth && features.twitterOAuth && features.uploadThing && aiProviderCount > 0;
    const overallStatus = dbHealthy && hasRequiredFeatures ? 'ok' : 'degraded';

    res.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: {
        connected: dbHealthy,
        provider: 'postgresql',
      },
      features,
      aiProviders: {
        count: aiProviderCount,
        available: Object.entries(features)
          .filter(([key, enabled]) => key.includes('ai') || ['openai', 'gemini', 'mistral', 'huggingface', 'groq', 'openrouter'].includes(key))
          .filter(([, enabled]) => enabled)
          .map(([key]) => key),
      },
      version: '1.0.0',
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Database stats endpoint
router.get('/stats', async (req, res) => {
  try {
    const stats = await getDatabaseStats();
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      data: stats,
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Failed to get database stats',
    });
  }
});

export default router;