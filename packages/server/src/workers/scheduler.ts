import cron from 'node-cron';
import { createTwitterServiceForAccount } from '../services/twitter-client';
import { prisma } from '../../../../lib/database';

// Simple structured logger
const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[${new Date().toISOString()}] INFO: ${message}`, meta ? JSON.stringify(meta) : '');
  },
  error: (message: string, meta?: any) => {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`, meta ? JSON.stringify(meta) : '');
  },
  warn: (message: string, meta?: any) => {
    console.warn(`[${new Date().toISOString()}] WARN: ${message}`, meta ? JSON.stringify(meta) : '');
  },
};

const processScheduledTweets = async () => {
  logger.info('Scheduler worker checking for due tweets...');

  const dueTweets = await prisma.scheduledTweet.findMany({
    where: {
      status: 'scheduled',
      scheduledFor: {
        lte: new Date(),
      },
    },
    include: {
      twitterAccount: true,
      user: true,
    },
  });

  if (dueTweets.length === 0) {
    logger.info('No due tweets found.');
    return;
  }

  logger.info(`Found ${dueTweets.length} due tweets to process.`);

  for (const tweet of dueTweets) {
    if (!tweet.twitterAccount) {
      const errorMessage = 'Twitter account not found';
      logger.error(`Tweet ${tweet.id} is missing a Twitter account. Marking as failed.`, {
        tweetId: tweet.id,
        userId: tweet.userId,
        error: errorMessage
      });

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'failed',
          errorMessage,
          updatedAt: new Date(),
        },
      });
      continue;
    }

    try {
      const { accessToken, refreshToken } = tweet.twitterAccount;
      if (!accessToken) {
        throw new Error('Twitter account is missing access token.');
      }

      logger.info(`Publishing tweet ${tweet.id}`, {
        tweetId: tweet.id,
        userId: tweet.userId,
        contentLength: tweet.content.length,
        hasMedia: !!(tweet.mediaUrls && tweet.mediaUrls.length > 0)
      });

      // Create Twitter service for this user's account using OAuth 2.0
      const twitterService = createTwitterServiceForAccount(
        accessToken,
        refreshToken || undefined
      );

      // Post the tweet using the service (handles media upload if provided)
      const twitterTweetId = await twitterService.postTweet(
        tweet.content,
        tweet.mediaUrls || []
      );

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'published',
          twitterTweetId,
          publishedAt: new Date(),
          updatedAt: new Date(),
        },
      });

      logger.info(`Successfully published tweet ${tweet.id}`, {
        tweetId: tweet.id,
        userId: tweet.userId,
        twitterTweetId,
        publishedAt: new Date().toISOString()
      });

    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred.';
      logger.error(`Failed to publish tweet ${tweet.id}`, {
        tweetId: tweet.id,
        userId: tweet.userId,
        error: errorMessage,
        stack: error.stack
      });

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'failed',
          errorMessage,
          updatedAt: new Date(),
        },
      });
    }
  }
};

export const startScheduler = () => {
  const cronInterval = process.env.SCHEDULER_INTERVAL || '*/1 * * * *';
  const isEnabled = process.env.SCHEDULER_ENABLED === 'true';

  if (!isEnabled) {
    logger.warn('Tweet scheduler is disabled', {
      reason: 'SCHEDULER_ENABLED is not set to "true"',
      currentValue: process.env.SCHEDULER_ENABLED,
      instruction: 'Set SCHEDULER_ENABLED=true in .env to enable scheduling'
    });
    return null;
  }

  const scheduledTask = cron.schedule(cronInterval, processScheduledTweets, {
    scheduled: true,
    timezone: 'UTC',
  });

  logger.info('Tweet scheduler started successfully', {
    interval: cronInterval,
    timezone: 'UTC',
    enabled: true,
    nextRun: 'Within 1 minute'
  });

  return scheduledTask;
};
