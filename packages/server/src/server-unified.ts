import express from 'express'
import cors from 'cors'
import path from 'path'
import { config } from 'dotenv'
import next from 'next'

// Load environment variables
config({ path: path.resolve(__dirname, '../../../.env') })

const app = express()
const PORT = parseInt(process.env.PORT || '3030', 10)
const isDev = process.env.NODE_ENV === 'development'

// Initialize Next.js
const nextApp = next({ 
  dev: isDev,
  dir: path.resolve(__dirname, '../../../apps/web')
})
const handle = nextApp.getRequestHandler()

async function startServer() {
  try {
    // Prepare Next.js
    await nextApp.prepare()
    console.log('✅ Next.js prepared')

    // Middleware
    app.use(express.json({ limit: '10mb' }))
    app.use(express.urlencoded({ extended: true }))

    // CORS configuration
    app.use(cors({
      origin: process.env.FRONTEND_URL || `http://localhost:${PORT}`,
      credentials: true
    }))

    // Health check endpoint
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        port: PORT
      })
    })

    // API Routes (your existing API endpoints)
    app.use('/api/auth', (req, res, next) => {
      // Your auth routes here
      res.json({ message: 'Auth endpoint' })
    })

    app.use('/api/agents', (req, res, next) => {
      // Your agents routes here
      res.json({ message: 'Agents endpoint' })
    })

    app.use('/api/schedule', (req, res, next) => {
      // Your schedule routes here
      res.json({ message: 'Schedule endpoint' })
    })

    app.use('/api/analytics', (req, res, next) => {
      // Your analytics routes here
      res.json({ message: 'Analytics endpoint' })
    })

    // Catch-all handler for Next.js pages
    app.all('*', (req, res) => {
      return handle(req, res)
    })

    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 Unified server running on port ${PORT}`)
      console.log(`🌐 Frontend: http://localhost:${PORT}`)
      console.log(`🔌 API: http://localhost:${PORT}/api`)
      console.log(`💚 Health: http://localhost:${PORT}/health`)
      console.log(`🔧 Environment: ${process.env.NODE_ENV}`)
    })

  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully')
  process.exit(0)
})

// Start the server
startServer()
