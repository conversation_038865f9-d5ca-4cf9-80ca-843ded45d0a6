import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import fs from 'fs';
import { config } from 'dotenv';
import path from 'path';
import healthRoutes from './routes/health';
import { tweetScheduler } from './services/scheduler';

// Load environment variables from the nearest .env file.
// 1. Prefer the monorepo root (two levels up when running via ts-node/tsx)
// 2. Handle the compiled JS path (`dist/...`) which is three levels up
// 3. Fallback to whatever the current working directory is
const envCandidates = [
	path.resolve(__dirname, '../../.env'),
	path.resolve(__dirname, '../../../.env'),
	path.resolve(process.cwd(), '.env'),
];

for (const p of envCandidates) {
	if (fs.existsSync(p)) {
		config({ path: p });
		break;
	}
}

// Validate environment variables at startup
try {
  console.log('🔍 Validating environment configuration...');

  // Basic validation for required variables
  const requiredVars = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars.join(', '));
    console.error('💡 Please check your .env file and ensure all required variables are set.');
    process.exit(1);
  }

  console.log('✅ Environment validation successful!');
} catch (error) {
  console.error('💥 Environment validation failed:', error);
  process.exit(1);
}

const app = express();
const PORT = parseInt(process.env.PORT || '3000', 10);

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3030',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api', healthRoutes);

// Hello endpoint
app.get('/api/hello', (req, res) => {
  res.json({ 
    message: 'Hello from XTask Express server!',
    version: '1.0.0'
  });
});

// API Routes placeholder
app.use('/api', (req, res, next) => {
  // Future API routes will be added here
  if (!res.headersSent) {
    res.status(404).json({ error: 'API endpoint not found' });
  }
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 XTask server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Start tweet scheduler
  tweetScheduler.start();
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  tweetScheduler.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Shutting down server...');
  tweetScheduler.stop();
  process.exit(0);
});

export default app;