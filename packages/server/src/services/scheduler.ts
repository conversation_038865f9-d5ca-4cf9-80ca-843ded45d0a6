import cron from 'node-cron'
import { PrismaClient } from '@prisma/client'
import { TwitterService, createTwitterServiceForAccount } from './twitter-client'
import { decryptToken } from '../../../../lib/auth/crypto'

const prisma = new PrismaClient()

export class TweetScheduler {
  private isRunning = false
  private cronJob: cron.ScheduledTask | null = null

  constructor() {
    // No default Twitter service needed since we use OAuth per user
  }

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('Scheduler is already running')
      return
    }

    const interval = process.env.SCHEDULER_INTERVAL || '*/1 * * * *' // Every minute by default
    const enabled = process.env.SCHEDULER_ENABLED !== 'false'

    if (!enabled) {
      console.log('Scheduler is disabled')
      return
    }

    console.log(`Starting tweet scheduler with interval: ${interval}`)

    this.cronJob = cron.schedule(interval, async () => {
      await this.processPendingTweets()
    }, {
      scheduled: true,
      timezone: 'UTC',
    })

    this.isRunning = true
    console.log('Tweet scheduler started successfully')
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning || !this.cronJob) {
      return
    }

    this.cronJob.stop()
    this.cronJob = null
    this.isRunning = false
    console.log('Tweet scheduler stopped')
  }

  /**
   * Process pending tweets that are ready to be published
   */
  private async processPendingTweets(): Promise<void> {
    try {
      console.log('Processing pending tweets...')

      // Get tweets that are scheduled and due for publishing
      const pendingTweets = await prisma.scheduledTweet.findMany({
        where: {
          status: 'scheduled',
          scheduledFor: {
            lte: new Date(),
          },
        },
        include: {
          twitterAccount: true,
          agent: true,
        },
        orderBy: {
          scheduledFor: 'asc',
        },
      })

      if (pendingTweets.length === 0) {
        console.log('No pending tweets to process')
        return
      }

      console.log(`Found ${pendingTweets.length} pending tweets`)

      // Group tweets by thread
      const threads = new Map<string, typeof pendingTweets>()
      const singleTweets: typeof pendingTweets = []

      for (const tweet of pendingTweets) {
        if (tweet.threadId) {
          if (!threads.has(tweet.threadId)) {
            threads.set(tweet.threadId, [])
          }
          threads.get(tweet.threadId)!.push(tweet)
        } else {
          singleTweets.push(tweet)
        }
      }

      // Process single tweets
      for (const tweet of singleTweets) {
        await this.publishSingleTweet(tweet)
      }

      // Process threads
      for (const [threadId, threadTweets] of threads) {
        await this.publishThread(threadTweets)
      }

      console.log('Finished processing pending tweets')
    } catch (error) {
      console.error('Error processing pending tweets:', error)
    }
  }

  /**
   * Publish a single tweet
   */
  private async publishSingleTweet(tweet: any): Promise<void> {
    try {
      console.log(`Publishing tweet ${tweet.id}`)

      if (!tweet.twitterAccount) {
        throw new Error('No Twitter account associated with this tweet')
      }

      // Get Twitter service for this user's account
      const twitterService = await this.getTwitterServiceForAccount(tweet.twitterAccount)

      // Publish tweet
      const twitterTweetId = await twitterService.postTweet(tweet.content, tweet.mediaUrls)

      // Update tweet status
      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'published',
          publishedAt: new Date(),
          twitterTweetId,
        },
      })

      // Update agent metrics
      await this.updateAgentMetrics(tweet.agentId)

      console.log(`Successfully published tweet ${tweet.id} as ${twitterTweetId}`)
    } catch (error) {
      console.error(`Failed to publish tweet ${tweet.id}:`, error)

      // Update tweet status to failed
      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'failed',
          errorMessage: error instanceof Error ? error.message : 'Unknown error occurred',
        },
      })
    }
  }

  /**
   * Publish a thread of tweets
   */
  private async publishThread(threadTweets: any[]): Promise<void> {
    try {
      // Sort tweets by thread order
      const sortedTweets = threadTweets.sort((a, b) => (a.threadOrder || 0) - (b.threadOrder || 0))
      const threadId = sortedTweets[0].threadId

      console.log(`Publishing thread ${threadId} with ${sortedTweets.length} tweets`)

      if (!sortedTweets[0].twitterAccount) {
        throw new Error('No Twitter account associated with this thread')
      }

      // Get Twitter service for this user's account
      const twitterService = await this.getTwitterServiceForAccount(sortedTweets[0].twitterAccount)

      // Prepare thread data
      const threadData = sortedTweets.map(tweet => ({
        content: tweet.content,
        mediaUrls: tweet.mediaUrls,
      }))

      // Publish thread
      const twitterTweetIds = await twitterService.postThread(threadData)

      // Update all tweets in the thread
      for (let i = 0; i < sortedTweets.length; i++) {
        const tweet = sortedTweets[i]
        const twitterTweetId = twitterTweetIds[i]

        await prisma.scheduledTweet.update({
          where: { id: tweet.id },
          data: {
            status: 'published',
            publishedAt: new Date(),
            twitterTweetId,
          },
        })

        // Update agent metrics for each tweet
        await this.updateAgentMetrics(tweet.agentId)
      }

      console.log(`Successfully published thread ${threadId}`)
    } catch (error) {
      console.error(`Failed to publish thread:`, error)

      // Update all tweets in thread to failed status
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      for (const tweet of threadTweets) {
        await prisma.scheduledTweet.update({
          where: { id: tweet.id },
          data: {
            status: 'failed',
            errorMessage,
          },
        })
      }
    }
  }

  /**
   * Get Twitter service for a specific account using OAuth 2.0 tokens
   */
  private async getTwitterServiceForAccount(twitterAccount: any): Promise<TwitterService> {
    if (!twitterAccount) {
      throw new Error('Twitter account is required')
    }

    try {
      // Decrypt OAuth tokens
      const accessTokenData = await decryptToken(twitterAccount.accessToken)
      const refreshTokenData = twitterAccount.refreshToken 
        ? await decryptToken(twitterAccount.refreshToken) 
        : null

      // Create service for this account using OAuth 2.0 (updated for v2 API)
      const twitterService = createTwitterServiceForAccount(
        accessTokenData.token || accessTokenData,
        refreshTokenData?.token || refreshTokenData
      )

      // Verify credentials are still valid
      const isValid = await twitterService.verifyCredentials()
      if (!isValid) {
        // Try to refresh the token if we have a refresh token
        if (refreshTokenData) {
          console.log(`Refreshing Twitter token for account ${twitterAccount.id}`)
          const refreshedTokens = await twitterService.refreshAccessToken()
          
          if (refreshedTokens) {
            // Update stored tokens
            const { encryptToken } = await import('../../../../lib/auth/crypto')
            
            await prisma.twitterAccount.update({
              where: { id: twitterAccount.id },
              data: {
                accessToken: await encryptToken({ token: refreshedTokens.accessToken }),
                ...(refreshedTokens.refreshToken && {
                  refreshToken: await encryptToken({ token: refreshedTokens.refreshToken })
                }),
                updatedAt: new Date(),
              },
            })

            // Create new service with refreshed tokens
            return createTwitterServiceForAccount(
              refreshedTokens.accessToken,
              refreshedTokens.refreshToken
            )
          }
        }
        
        throw new Error('Twitter credentials are invalid and could not be refreshed')
      }

      return twitterService
    } catch (error) {
      console.error(`Failed to create Twitter service for account ${twitterAccount.id}:`, error)
      throw error
    }
  }

  /**
   * Update agent metrics after successful tweet
   */
  private async updateAgentMetrics(agentId: string): Promise<void> {
    try {
      await prisma.agent.update({
        where: { id: agentId },
        data: {
          tweetsGenerated: {
            increment: 1,
          },
        },
      })
    } catch (error) {
      console.error(`Failed to update agent metrics for ${agentId}:`, error)
    }
  }
}

// Export singleton instance
export const tweetScheduler = new TweetScheduler()