import { <PERSON><PERSON><PERSON>, Twitter<PERSON><PERSON>ReadWrite } from 'twitter-api-v2'

export interface TwitterOAuthCredentials {
  accessToken: string
  refreshToken?: string
  clientId: string
  clientSecret: string
}

export class TwitterService {
  private client: TwitterApiReadWrite

  constructor(credentials: TwitterOAuthCredentials) {
    // Initialize with OAuth 2.0 for user context
    const baseClient = new TwitterA<PERSON>({
      clientId: credentials.clientId,
      clientSecret: credentials.clientSecret,
    })

    // Set user context with OAuth 2.0 tokens  
    this.client = baseClient.withOAuth2({
      accessToken: credentials.accessToken,
      refreshToken: credentials.refreshToken,
    }).readWrite
  }

  /**
   * Post a tweet with optional media
   */
  async postTweet(content: string, mediaUrls?: string[]): Promise<string> {
    try {
      let mediaIds: string[] = []

      // Upload media if provided
      if (mediaUrls && mediaUrls.length > 0) {
        mediaIds = await this.uploadMedia(mediaUrls)
      }

      // Post tweet
      const tweet = await this.client.v2.tweet({
        text: content,
        ...(mediaIds.length > 0 && { media: { media_ids: mediaIds } }),
      })

      return tweet.data.id
    } catch (error) {
      console.error('Twitter API error:', error)
      throw new Error(`Failed to post tweet: ${error}`)
    }
  }

  /**
   * Post a thread of tweets
   */
  async postThread(tweets: Array<{ content: string; mediaUrls?: string[] }>): Promise<string[]> {
    const tweetIds: string[] = []
    let replyToId: string | undefined

    for (const tweet of tweets) {
      try {
        let mediaIds: string[] = []

        // Upload media if provided
        if (tweet.mediaUrls && tweet.mediaUrls.length > 0) {
          mediaIds = await this.uploadMedia(tweet.mediaUrls)
        }

        // Post tweet
        const response = await this.client.v2.tweet({
          text: tweet.content,
          ...(replyToId && { reply: { in_reply_to_tweet_id: replyToId } }),
          ...(mediaIds.length > 0 && { media: { media_ids: mediaIds } }),
        })

        const tweetId = response.data.id
        tweetIds.push(tweetId)
        replyToId = tweetId // Next tweet will reply to this one
      } catch (error) {
        console.error(`Failed to post tweet ${tweetIds.length + 1} in thread:`, error)
        throw new Error(`Failed to post tweet ${tweetIds.length + 1} in thread: ${error}`)
      }
    }

    return tweetIds
  }

  /**
   * Upload media files to Twitter
   */
  private async uploadMedia(mediaUrls: string[]): Promise<string[]> {
    const mediaIds: string[] = []

    for (const url of mediaUrls) {
      try {
        // Download media from URL
        const response = await fetch(url)
        if (!response.ok) {
          throw new Error(`Failed to download media: ${response.statusText}`)
        }

        const buffer = await response.arrayBuffer()
        const uint8Array = new Uint8Array(buffer)

        // Determine media type
        const contentType = response.headers.get('content-type') || ''
        const isVideo = contentType.startsWith('video/')

        // Upload to Twitter
        const mediaId = await this.client.v1.uploadMedia(uint8Array, {
          mimeType: contentType,
          target: isVideo ? 'tweet' : 'tweet',
        })

        mediaIds.push(mediaId)
      } catch (error) {
        console.error('Media upload error:', error)
        throw new Error(`Failed to upload media: ${error}`)
      }
    }

    return mediaIds
  }

  /**
   * Verify Twitter credentials
   */
  async verifyCredentials(): Promise<boolean> {
    try {
      await this.client.v2.me()
      return true
    } catch (error) {
      console.error('Twitter credentials verification failed:', error)
      return false
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(): Promise<{ accessToken: string; refreshToken?: string } | null> {
    try {
      const refreshedTokens = await this.client.refreshOAuth2Token()
      return {
        accessToken: refreshedTokens.accessToken,
        refreshToken: refreshedTokens.refreshToken,
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      return null
    }
  }
}

/**
 * Create Twitter service instance for a specific user account
 */
export function createTwitterServiceForAccount(
  accessToken: string,
  refreshToken?: string
): TwitterService {
  const clientId = process.env.TWITTER_CLIENT_ID
  const clientSecret = process.env.TWITTER_CLIENT_SECRET

  if (!clientId || !clientSecret) {
    throw new Error('Twitter OAuth credentials not configured')
  }

  return new TwitterService({
    accessToken,
    refreshToken,
    clientId,
    clientSecret,
  })
}