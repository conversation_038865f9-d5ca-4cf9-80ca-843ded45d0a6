{"name": "xtask-server", "version": "0.1.0", "private": true, "scripts": {"dev": "node ../../scripts/start-backend.js", "dev:unified": "tsx src/server-unified.ts", "build": "tsc", "build:unified": "tsc src/server-unified.ts --outDir dist", "start": "node dist/server-simple.js", "start:unified": "node dist/server-unified.js", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.8.0", "express": "^4.21.1", "cors": "^2.8.5", "helmet": "^8.0.0", "compression": "^1.7.4", "dotenv": "^16.4.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/node": "^22.7.4", "tsx": "^4.19.1", "typescript": "^5.6.2"}}